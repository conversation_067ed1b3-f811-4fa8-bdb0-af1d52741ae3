from abc import ABC, abstractmethod
from collections import namedtuple

class ConfigRegisterGroup:
    MAX_VALUE = 0xFFFFFFFF  # 32-bit unsigned integer max value
    REG_NUMS = [
        19, # tld, tld-idx
        19, # tst, tst-idx
        8,  # TM
        11, # TP
        5,  # VP
        5,  # NOC
    ]

    BIT_POS = namedtuple('BIT_POS', ['idx1', 'idx2', 'start', 'width'])
    BIT_MAP = {
        'tld.type'                              :   BIT_POS(0, 0 , 0 , 2 ),
        'tld.wd'                                :   BIT_POS(0, 0 , 2 , 3 ),
        'tld.rem_dim0'                          :   BIT_POS(0, 1 , 0 , 6 ),
        'tld.size_dim0b'                        :   BIT_POS(0, 1 , 6 , 11),
        'tld.size_dim1'                         :   BIT_POS(0, 2 , 0 , 13),
        'tld.size_dim2'                         :   BIT_POS(0, 2 , 13, 13),
        'tld.stride_dim1_gmem'                  :   BIT_POS(0, 3 , 0 , 13),
        'tld.stride_dim2_gmem'                  :   BIT_POS(0, 4 , 0 , 25),
        'tld.stride_dim1_lmem'                  :   BIT_POS(0, 5 , 0 , 13),
        'tld.stride_dim2_lmem'                  :   BIT_POS(0, 6 , 0 , 25),
        'tld_idx.type'                          :   BIT_POS(0, 7 , 0 , 2 ),                                         
        'tld_idx.wd'                            :   BIT_POS(0, 7 , 2 , 3 ),                                         
        'tld_idx.type_index'                    :   BIT_POS(0, 8 , 0 , 2 ),                                                 
        'tld_idx.wd_index'                      :   BIT_POS(0, 8 , 2 , 3 ),                                             
        'tld_idx.rem_dim0'                      :   BIT_POS(0, 9 , 0 , 6 ),                                             
        'tld_idx.size_dim0b'                    :   BIT_POS(0, 9 , 6 , 11),                                                 
        'tld_idx.size_dim1'                     :   BIT_POS(0, 10, 0 , 13),                                             
        'tld_idx.size_dim2'                     :   BIT_POS(0, 10, 13, 13),                                             
        'tld_idx.rem_dim0_index'                :   BIT_POS(0, 11, 0 , 6 ),                                                     
        'tld_idx.size_dim0b_index'              :   BIT_POS(0, 11, 6 , 11),                                                     
        'tld_idx.rem_dim0_gmem'                 :   BIT_POS(0, 12, 0 , 6 ),                                                 
        'tld_idx.size_dim0b_gmem'               :   BIT_POS(0, 12, 6 , 11),                                                     
        'tld_idx.stride_dim1_gmem'              :   BIT_POS(0, 13, 0 , 13),                                                     
        'tld_idx.stride_dim2_gmem'              :   BIT_POS(0, 14, 0 , 25),                                                     
        'tld_idx.stride_dim1_lmem'              :   BIT_POS(0, 15, 0 , 13),                                                     
        'tld_idx.stride_dim2_lmem'              :   BIT_POS(0, 16, 0 , 25),                                                     
        'tld_idx.stride_dim1_lmem_index'        :   BIT_POS(0, 17, 0 , 13),                                                             
        'tld_idx.stride_dim2_lmem_inedx'        :   BIT_POS(0, 18, 0 , 25),                                                                 

        'tst.type'                              :   BIT_POS(1, 0 , 0 , 2 ),
        'tst.wd'                                :   BIT_POS(1, 0 , 2 , 3 ),
        'tst.rem_dim0'                          :   BIT_POS(1, 1 , 0 , 6 ),
        'tst.size_dim0b'                        :   BIT_POS(1, 1 , 6 , 11),
        'tst.size_dim1'                         :   BIT_POS(1, 2 , 0 , 13),
        'tst.size_dim2'                         :   BIT_POS(1, 2 , 13, 13),
        'tst.stride_dim1_gmem'                  :   BIT_POS(1, 3 , 0 , 13),
        'tst.stride_dim2_gmem'                  :   BIT_POS(1, 4 , 0 , 25),
        'tst.stride_dim1_lmem'                  :   BIT_POS(1, 5 , 0 , 13),
        'tst.stride_dim2_lmem'                  :   BIT_POS(1, 6 , 0 , 25),
        'tst_idx.type'                          :   BIT_POS(1, 7 , 0 , 2 ),                                         
        'tst_idx.wd'                            :   BIT_POS(1, 7 , 2 , 3 ),                                         
        'tst_idx.type_index'                    :   BIT_POS(1, 8 , 0 , 2 ),                                                 
        'tst_idx.wd_index'                      :   BIT_POS(1, 8 , 2 , 3 ),                                             
        'tst_idx.rem_dim0'                      :   BIT_POS(1, 9 , 0 , 6 ),                                             
        'tst_idx.size_dim0b'                    :   BIT_POS(1, 9 , 6 , 11),                                                 
        'tst_idx.size_dim1'                     :   BIT_POS(1, 10, 0 , 13),                                             
        'tst_idx.size_dim2'                     :   BIT_POS(1, 10, 13, 13),                                             
        'tst_idx.rem_dim0_index'                :   BIT_POS(1, 11, 0 , 6 ),                                                     
        'tst_idx.size_dim0b_index'              :   BIT_POS(1, 11, 6 , 11),                                                     
        'tst_idx.rem_dim0_gmem'                 :   BIT_POS(1, 12, 0 , 6 ),                                                 
        'tst_idx.size_dim0b_gmem'               :   BIT_POS(1, 12, 6 , 11),                                                     
        'tst_idx.stride_dim1_gmem'              :   BIT_POS(1, 13, 0 , 13),                                                     
        'tst_idx.stride_dim2_gmem'              :   BIT_POS(1, 14, 0 , 25),                                                     
        'tst_idx.stride_dim1_lmem'              :   BIT_POS(1, 15, 0 , 13),                                                     
        'tst_idx.stride_dim2_lmem'              :   BIT_POS(1, 16, 0 , 25),                                                     
        'tst_idx.stride_dim1_lmem_index'        :   BIT_POS(1, 17, 0 , 13),                                                             
        'tst_idx.stride_dim2_lmem_inedx'        :   BIT_POS(1, 18, 0 , 25),  

        'tm.type'                               :   BIT_POS(2, 0 , 0 , 2 ),                                                     
        'tm.wd'                                 :   BIT_POS(2, 0 , 2 , 3 ),                                                 
        'tm.rem_dim0_in'                        :   BIT_POS(2, 1 , 0 , 6 ),                                                             
        'tm.size_dim0b_in'                      :   BIT_POS(2, 1 , 6 , 11),                                                             
        'tm.rem_dim0_out'                       :   BIT_POS(2, 2 , 0 , 6 ), 
        'tm.size_dim0b_out'                     :   BIT_POS(2, 2 , 6 , 11), 
        'tm.size_dim1'                          :   BIT_POS(2, 3 , 0 , 13),                                                             
        'tm.size_dim2'                          :   BIT_POS(2, 3 , 13, 13),                                                             
        'tm.stride_dim1_out'                    :   BIT_POS(2, 4 , 0 , 13),                                                                 
        'tm.stride_dim2_out'                    :   BIT_POS(2, 5 , 0 , 25),                                                                 
        'tm.stride_dim1_in'                     :   BIT_POS(2, 6 , 0 , 13),                                                                 
        'tm.stride_dim2_in'                     :   BIT_POS(2, 7 , 0 , 25),  

        'tp.type_out'                           :   BIT_POS(3, 0 , 0 , 2 ),            
        'tp.type_orig'                          :   BIT_POS(3, 0 , 2 , 2 ),            
        'tp.type_in'                            :   BIT_POS(3, 0 , 4 , 2 ),            
        'tp.type_wt'                            :   BIT_POS(3, 0 , 6 , 2 ),            
        'tp.wd_out'                             :   BIT_POS(3, 0 , 8 , 3 ),        
        'tp.wd_orig'                            :   BIT_POS(3, 0 , 11, 3 ),            
        'tp.wd_in'                              :   BIT_POS(3, 0 , 14, 3 ),        
        'tp.wd_wt'                              :   BIT_POS(3, 0 , 17, 3 ),        
        'tp.accu'                               :   BIT_POS(3, 0 , 20, 1 ),        
        'tp.act'                                :   BIT_POS(3, 0 , 21, 1 ),        
        'tp.shift'                              :   BIT_POS(3, 0 , 22, 5 ),        
        'tp.rem_dim0_out'                       :   BIT_POS(3, 1 , 0 , 6 ),                
        'tp.size_dim0b_out'                     :   BIT_POS(3, 1 , 6 , 11),                
        'tp.size_dim1_out'                      :   BIT_POS(3, 2 , 0 , 13),                
        'tp.size_dim2_out'                      :   BIT_POS(3, 2 , 13, 13),                
        'tp.stride_dim1_out'                    :   BIT_POS(3, 3 , 0 , 13),                    
        'tp.stride_dim2_out'                    :   BIT_POS(3, 4 , 0 , 25),                    
        'tp.rem_dim0_in'                        :   BIT_POS(3, 5 , 0 , 6 ),                
        'tp.size_dim0b_in'                      :   BIT_POS(3, 5 , 6 , 11),                
        'tp.size_dim1_in'                       :   BIT_POS(3, 6 , 0 , 13),                
        'tp.size_dim2_in'                       :   BIT_POS(3, 6 , 13, 13),                
        'tp.stride_dim1_in'                     :   BIT_POS(3, 7 , 0 , 13),                
        'tp.stride_dim2_in'                     :   BIT_POS(3, 8 , 0 , 25),                
        'tp.k_x'                                :   BIT_POS(3, 9 , 0 , 5 ),        
        'tp.k_y'                                :   BIT_POS(3, 9 , 5 , 5 ),        
        'tp.slide_x'                            :   BIT_POS(3, 9 , 10, 5 ),            
        'tp.slide_y'                            :   BIT_POS(3, 9 , 15, 5 ),            
        'tp.dil_x'                              :   BIT_POS(3, 9 , 20, 5 ),        
        'tp.dil_y'                              :   BIT_POS(3, 9 , 25, 5 ),        
        'tp.log2trs_x'                          :   BIT_POS(3, 10, 0 , 3 ),            
        'tp.log2trs_y'                          :   BIT_POS(3, 10, 3 , 3 ),            
        'tp.pad_w'                              :   BIT_POS(3, 10, 6 , 4 ),        
        'tp.pad_n'                              :   BIT_POS(3, 10, 10, 4 ),        
        'tp.pad_val'                            :   BIT_POS(3, 10, 14, 16),  

        'vp.type_out'                           :   BIT_POS(4, 0 , 0 , 2 ),
        'vp.type_in1'                           :   BIT_POS(4, 0 , 2 , 2 ),
        'vp.type_in2'                           :   BIT_POS(4, 0 , 4 , 2 ),
        'vp.wd_out'                             :   BIT_POS(4, 0 , 6 , 3 ),
        'vp.wd_in1'                             :   BIT_POS(4, 0 , 9 , 3 ),
        'vp.wd_in2'                             :   BIT_POS(4, 0 , 12, 3 ),
        'vp.op'                                 :   BIT_POS(4, 0 , 15, 6 ),
        'vp.rem_dim0_out'                       :   BIT_POS(4, 1 , 0 , 6 ),
        'vp.size_dim0b_out'                     :   BIT_POS(4, 1 , 6 , 11),
        'vp.rem_dim0_in1'                       :   BIT_POS(4, 2 , 0 , 6 ),
        'vp.size_dim0b_in1'                     :   BIT_POS(4, 2 , 6 , 11),
        'vp.rem_dim0_in2'                       :   BIT_POS(4, 3 , 0 , 6 ),
        'vp.size_dim0b_in2'                     :   BIT_POS(4, 3 , 6 , 11),
        'vp.safu'                               :   BIT_POS(4, 4 , 0 , 1 ),                                     
        'vp.dis0'                               :   BIT_POS(4, 4 , 1 , 1 ),                                     
        'vp.rm'                                 :   BIT_POS(4, 4 , 2 , 3 ),  

        'noc.type'                              :   BIT_POS(5, 0 , 0 , 2 ),
        'noc.wd'                                :   BIT_POS(5, 0 , 2 , 3 ),
        'noc.rem_dim0'                          :   BIT_POS(5, 1 , 0 , 6 ),
        'noc.size_dim0b'                        :   BIT_POS(5, 1 , 6 , 11),
        'noc.size_dim1'                         :   BIT_POS(5, 2 , 0 , 13),
        'noc.size_dim2'                         :   BIT_POS(5, 2 , 13, 13),
        'noc.stride_dim1'                       :   BIT_POS(5, 3 , 0 , 13),
        'noc.stride_dim2'                       :   BIT_POS(5, 4 , 0 , 25),
    }

    def __init__(self):
        self._register = [[0] * num for num in self.REG_NUMS]

    def __getitem__(self, key):
        if isinstance(key, tuple):
            group_idx, reg_idx = self._unpack_indices(key)
            self._check_index(group_idx, reg_idx)
            return self._register[group_idx][reg_idx]
        elif isinstance(key, str):
            return self._read_alias(key)
        else:
            raise TypeError("Invalid key type")

    def __setitem__(self, key, value):
        if isinstance(key, tuple):
            group_idx, reg_idx = self._unpack_indices(key)
            self._check_index(group_idx, reg_idx)
            if not (0 <= value <= self.MAX_VALUE):
                raise ValueError(f"Value {value} out of range")
            self._register[group_idx][reg_idx] = value
        elif isinstance(key, str):
            self._write_alias(key, value)
        else:
            raise TypeError("Invalid key type")

    def _read_alias(self, name):
        if name not in self.BIT_MAP:
            raise KeyError(f"Unknown alias '{name}'")
        pos = self.BIT_MAP[name]
        value = self[pos.idx1, pos.idx2]
        mask = (1 << pos.width) - 1
        return (value >> pos.start) & mask

    def _write_alias(self, name, field_value):
        if name not in self.BIT_MAP:
            raise KeyError(f"Unknown alias '{name}'")
        pos = self.BIT_MAP[name]
        if field_value >= (1 << pos.width):
            raise ValueError(f"Value {field_value} too large for field '{name}' (max {(1 << pos.width) - 1})")
        reg_val = self[pos.idx1, pos.idx2]
        mask = ((1 << pos.width) - 1) << pos.start
        new_val = (reg_val & ~mask) | ((field_value << pos.start) & mask)
        self[pos.idx1, pos.idx2] = new_val

    def _unpack_indices(self, indices):
        if not (isinstance(indices, tuple) and len(indices) == 2):
            raise TypeError("Expected tuple (group_idx, reg_idx)")
        return indices

    def _check_index(self, group_idx, reg_idx):
        if not (0 <= group_idx < len(self.REG_NUMS)):
            raise IndexError("group_idx out of range")
        if not (0 <= reg_idx < self.REG_NUMS[group_idx]):
            raise IndexError("reg_idx out of range") 

    # def pack_dict(self, idx1=None):
    #     result = {}
    #     for name, pos in self.BIT_MAP.items():
    #         if idx1 is not None and pos.idx1 != idx1:
    #             continue
    #         result[name] = self[name]  
    #     return result
    def pack_dict(self, prefix=None):
        result = {}
        for name, pos in self.BIT_MAP.items():
            if prefix is None or name.startswith(prefix):
                result[name] = self[name]
        return result


class InstPreRegisterGroup:
    MAX_VALUE = 0xFFFFFFFF  # 32-bit unsigned integer max
    NAMES = [
        'tld_idx.base_addr_lmem',
        'tld_idx.base_addr_gmem',
        'tst_idx.base_addr_lmem',
        'tst_idx.base_addr_gmem',
        'conv.base_addr_in' ,
        'conv.base_addr_wt' ,
        'gemv.base_addr_in' ,
        'gemv.base_addr_wt' ,
        'gemm.base_addr_in' ,
        'gemm.base_addr_wt' ,
        'vvv.base_addr_in2' ,
        'vvv.base_addr_in1' ,
        'vsv.scalar_in'     ,
        'vsv.base_addr_in1' ,
    ]

    def __init__(self):
        self._registers = {name: 0 for name in self.NAMES}

    def __getitem__(self, name):
        if name not in self._registers:
            raise KeyError(f"Register '{name}' does not exist")
        return self._registers[name]

    def __setitem__(self, name, value):
        if name not in self._registers:
            raise KeyError(f"Register '{name}' does not exist")
        if not (0 <= value <= self.MAX_VALUE):
            raise ValueError(f"Value {value} out of range (0~0xFFFFFFFF)")
        self._registers[name] = value

    def pack_dict(self, prefix=None):
        result = {}
        for name, value in self._registers.items():
            if prefix is None:
                result[name] = value
            elif name.startswith(prefix):
                result[name] = value                
        return result


if __name__ == '__main__':
    rg = ConfigRegisterGroup()
    rg['tld.type'] = 2
    rg['tld.wd'] = 5
    print(rg['tld.type'])  
    print(rg['tld.wd'])    
    print(rg.pack_dict('tld'))  

    print('##########')

    pre_reg = InstPreRegisterGroup()
    print(pre_reg.pack_dict('vvv'))