import json
from typing import List, Dict, Any, NamedTuple
import torch

###############################################################################
# Private Function Tools                                                      #
###############################################################################

WORD_BITS = 256
WORD_BYTES = WORD_BITS // 8


def min_strides(shape, width):
    (dim2, dim1, dim0) = shape
    dim0a = WORD_BITS // width
    dim0b =(dim0 + dim0a - 1) // dim0a
    stride0b = 1
    stride1 = dim0b
    stride2 = stride1 * dim1
    return (stride2, stride1, stride0b)

def dtype_convert(dtype="INT4"):
    _map = {
        "INT4"      :   (4, torch.int8      ), # use int8 for calculation
        "INT8"      :   (8, torch.int8      ),
        "INT16"     :   (16,torch.int16     ),
        "INT32"     :   (32,torch.int32     ),
        "FP16"      :   (16,torch.float16   ),
        "FP32"      :   (32,torch.float32   ),
        "BF16"      :   (16,torch.bfloat16  )
    }

    if dtype not in _map:
        raise ValueError(f"Unsupported dtype: {dtype}")
    return _map[dtype]


def create_min_tensor(name: str, group_id: int, core_id: int,  address: int, shape: tuple, dtype: str) -> Dict[str, Any]:
    width, _ = dtype_convert(dtype)
    (dim2, dim1, dim0) = shape
    stride2, stride1, stride0b = min_strides(shape, width)
    template =  {
        "name": f"{name}_{group_id}_{core_id}",
        "address": f"0x{address:08x}",
        "datatype": dtype,
        "dimension": {
            "dim0": dim0,
            "dim1": dim1,
            "dim2": dim2
        },
        "stride": {
            "stride0b": stride0b,
            "stride1": stride1,
            "stride2": stride2
        }
    }
    return template

def mask2core_id(mask: List[int]) -> List[tuple]:
    config = []
    for group_id in range(4):
        for core_id in range(4):
            if mask[group_id] & (1 << core_id):
                config.append((group_id, core_id))
    return config


TensorDescriptor = NamedTuple("TensorDescriptor", [
    ("name", str),
    ("address", int),
    ("shape", tuple),
    ("dtype", str),
])

###############################################################################
# Configurations                                                              #
###############################################################################


# 这里使用vvv作为示例

MASK = [0xf, 0xf, 0xf, 0xf] # 根据需要选择 mask

input_tensors = [
    TensorDescriptor(name="in1" , address=0x0000_0000, shape=(1, 1, 256), dtype="BF16"),
]
output_tensors = [
]

core_ids = mask2core_id(MASK)

in_file  = "input_tensor.json"
out_file = "output_tensor.json"

in_configs = []
for core_id in core_ids:
    for tensor in input_tensors:
        config = create_min_tensor(tensor.name, core_id[0], core_id[1], tensor.address, tensor.shape, tensor.dtype)
        in_configs.append(config)

with open(in_file, "w", encoding='utf-8') as f:
    json.dump(in_configs, f, indent=4, ensure_ascii=False)

out_configs = []
for core_id in core_ids:
    for tensor in output_tensors:
        config = create_min_tensor(tensor.name, core_id[0], core_id[1], tensor.address, tensor.shape, tensor.dtype)
        out_configs.append(config)

with open(out_file, "w", encoding='utf-8') as f:
    json.dump(out_configs, f, indent=4, ensure_ascii=False)


