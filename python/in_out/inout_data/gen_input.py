import json
import sys
import torch
import os
from typing import Dict, List, Any, <PERSON><PERSON>

def get_datatype_size(datatype: str) -> float:
    """
    Returns the number of bytes per element based on data type
    
    Args:
        datatype: Data type string
        
    Returns:
        Number of bytes per element
    """
    datatype_sizes = {
        'INT4': 0.5,   # 4-bit integer, 0.5 bytes
        'INT8': 1,     # 8-bit integer, 1 byte
        'INT16': 2,    # 16-bit integer, 2 bytes
        'INT32': 4,    # 32-bit integer, 4 bytes
        'FP16': 2,     # 16-bit floating point, 2 bytes
        'FP32': 4,     # 32-bit floating point, 4 bytes
        'BF16': 2,     # 16-bit brain floating point, 2 bytes
    }
    
    if datatype not in datatype_sizes:
        raise ValueError(f"Unsupported data type: {datatype}. Supported types: {', '.join(datatype_sizes.keys())}")
    return datatype_sizes[datatype]

def calculate_memory_size(tensor: Dict[str, Any]) -> float:
    """
    Calculate memory size occupied by tensor (in bytes)
    
    Args:
        tensor: Tensor configuration dictionary
        
    Returns:
        Memory size (bytes)
    """
    if 'dimension' not in tensor:
        return 0
    
    dim = tensor['dimension']
    total_elements = dim.get('dim0', 1) * dim.get('dim1', 1) * dim.get('dim2', 1)
    
    datatype = tensor.get('datatype', 'FP32')
    bytes_per_element = get_datatype_size(datatype)
    
    return total_elements * bytes_per_element

def parse_tensor_config(json_file_path: str) -> List[Dict[str, Any]]:
    """
    Parse tensor configuration JSON file
    
    Args:
        json_file_path: JSON file path
        
    Returns:
        Parsed tensor configuration list
    """
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        print(f"Error: File not found {json_file_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Error: JSON parsing failed - {e}")
        sys.exit(1)

def print_tensor_info(tensor: Dict[str, Any], index: int) -> None:
    """
    Print detailed information for a single tensor
    
    Args:
        tensor: Tensor configuration dictionary
        index: Tensor index
    """
    print(f"\n{'='*50}")
    print(f"Tensor #{index + 1}: {tensor.get('name', 'Unknown')}")
    print(f"{'='*50}")
    
    # Basic information
    print(f"Address: {tensor.get('address', 'N/A')}")
    datatype = tensor.get('datatype', 'N/A')
    print(f"Data type: {datatype}")
    
    # Validate data type
    valid_datatypes = ['INT4', 'INT8', 'INT16', 'INT32', 'FP16', 'FP32', 'BF16']
    if datatype not in valid_datatypes and datatype != 'N/A':
        print(f"  Error: Unsupported data type '{datatype}'")
        print(f"  Supported data types: {', '.join(valid_datatypes)}")
        raise ValueError(f"Tensor '{tensor.get('name', 'Unknown')}' uses unsupported data type: {datatype}")
    
    # Dimension information
    if 'dimension' in tensor:
        dim = tensor['dimension']
        print(f"\nDimension information:")
        print(f"  - dim0: {dim.get('dim0', 'N/A')}")
        print(f"  - dim1: {dim.get('dim1', 'N/A')}")
        print(f"  - dim2: {dim.get('dim2', 'N/A')}")
        
        # Calculate total elements
        total_elements = dim.get('dim0', 1) * dim.get('dim1', 1) * dim.get('dim2', 1)
        print(f"  - Total elements: {total_elements}")
    
    # Stride information
    if 'stride' in tensor:
        stride = tensor['stride']
        print(f"\nStride information:")
        print(f"  - stride0b: {stride.get('stride0b', 'N/A')}")
        print(f"  - stride1: {stride.get('stride1', 'N/A')}")
        print(f"  - stride2: {stride.get('stride2', 'N/A')}")

def get_torch_dtype(datatype: str) -> torch.dtype:
    """
    Convert data type string to PyTorch data type
    
    Args:
        datatype: Data type string
        
    Returns:
        Corresponding PyTorch data type
    """
    dtype_mapping = {
        'INT4': torch.int8,    # INT4 stored as INT8
        'INT8': torch.int8,
        'INT16': torch.int16,
        'INT32': torch.int32,
        'FP16': torch.float16,
        'FP32': torch.float32,
        'BF16': torch.bfloat16,
    }
    
    if datatype not in dtype_mapping:
        raise ValueError(f"Unsupported data type: {datatype}. Supported types: {', '.join(dtype_mapping.keys())}")
    return dtype_mapping[datatype]

def get_random_range(datatype: str) -> Tuple[float, float]:
    """
    Return appropriate random number range based on data type
    
    Args:
        datatype: Data type string
        
    Returns:
        (min_value, max_value) tuple
    """
    if datatype == 'INT4':
        return (-8, 7)  # 4-bit signed integer range
    elif datatype == 'INT8':
        return (-128, 127)
    elif datatype == 'INT16':
        return (-32768, 32767)
    elif datatype == 'INT32':
        return (-2147483648, 2147483647)
    elif datatype in ['FP16', 'FP32', 'BF16']:
        return (-1.0, 1.0)  # Floating point uses normalized range
    else:
        raise ValueError(f"Unsupported data type: {datatype}. Supported types: INT4, INT8, INT16, INT32, FP16, FP32, BF16")

def create_random_tensor(tensor_config: Dict[str, Any]) -> torch.Tensor:
    """
    Create random PyTorch tensor based on tensor configuration
    
    Args:
        tensor_config: Tensor configuration dictionary
        
    Returns:
        Created PyTorch tensor
    """
    # Get dimensions
    dim = tensor_config.get('dimension', {})
    shape = (
        dim.get('dim2', 1),
        dim.get('dim1', 1),
        dim.get('dim0', 1)
    )
    
    # Get data type
    datatype = tensor_config.get('datatype', 'FP32')
    torch_dtype = get_torch_dtype(datatype)
    
    # Get random range
    min_val, max_val = get_random_range(datatype)
    
    # Create random tensor
    if datatype in ['INT4', 'INT8', 'INT16', 'INT32']:
        # Integer types
        tensor = torch.randint(min_val, max_val + 1, size=shape, dtype=torch_dtype)
    else:
        # Floating point types
        tensor = torch.rand(shape, dtype=torch_dtype) * (max_val - min_val) + min_val
    
    return tensor


def create_all_tensors(tensor_configs: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
    """
    Create all random tensors based on configurations
    
    Args:
        tensor_configs: List of tensor configurations
        
    Returns:
        Dictionary mapping tensor names to PyTorch tensors
    """
    print(f"\n{'='*50}")
    print("Creating random tensors")
    print(f"{'='*50}")
    
    tensors = {}
    for tensor_config in tensor_configs:
        name = tensor_config.get('name', 'unknown')
        try:
            tensor = create_random_tensor(tensor_config)
            tensors[name] = tensor
            
            datatype = tensor_config.get('datatype', 'FP32')
            print(f"\nCreated tensor: {name}")
            print(f"  Data type: {datatype}")
            print(f"  Shape: {tensor.shape}")
            if datatype != 'INT4':
                print(f"  Range: [{tensor.min().item():.4f}, {tensor.max().item():.4f}]")
        except Exception as e:
            print(f"\nError creating tensor {name}: {e}")
    
    return tensors

def save_tensor(name: str, tensor: torch.Tensor, tensor_config: Dict[str, Any], output_dir: str) -> None:
    """
    Save a single tensor to .pt file
    
    Args:
        name: Tensor name
        tensor: PyTorch tensor to save
        tensor_config: Tensor configuration
        output_dir: Output directory
    """
    filename = os.path.join(output_dir, f"{name}.pt")
    datatype = tensor_config.get('datatype', 'FP32')
    
    # Save as .pt file
    torch.save(tensor, filename)
    
    # Print save information
    print(f"\nSaved tensor: {name}")
    print(f"  Data type: {datatype} -> PyTorch: {tensor.dtype}")
    print(f"  Shape: {tensor.shape}")
    print(f"  Saved to: {filename}")

def save_tensors_to_pt(tensors: Dict[str, torch.Tensor], tensor_configs: List[Dict[str, Any]], output_dir: str = ".") -> None:
    """
    Save all tensors as .pt files
    
    Args:
        tensors: Dictionary of tensor names to PyTorch tensors
        tensor_configs: List of tensor configurations
        output_dir: Output directory
    """
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"\n{'='*50}")
    print("Saving tensors to .pt files")
    print(f"{'='*50}")
    
    # Create a config lookup dictionary
    config_dict = {config['name']: config for config in tensor_configs}
    
    for name, tensor in tensors.items():
        if name in config_dict:
            try:
                save_tensor(name, tensor, config_dict[name], output_dir)
            except Exception as e:
                print(f"\nError saving tensor {name}: {e}")
        else:
            print(f"\nWarning: No configuration found for tensor {name}")

def verify_saved_tensors(tensors: List[Dict[str, Any]], output_dir: str = ".") -> None:
    """
    Verify saved .pt files
    
    Args:
        tensors: Tensor configuration list
        output_dir: Output directory
    """
    print(f"\n{'='*50}")
    print("Verifying saved files")
    print(f"{'='*50}")
    
    for tensor_config in tensors:
        name = tensor_config.get('name', 'unknown')
        filename = os.path.join(output_dir, f"{name}.pt")
        
        if os.path.exists(filename):
            loaded_tensor = torch.load(filename)
            file_size = os.path.getsize(filename)
            print(f"\n{filename}")
            print(f"   File size: {file_size:,} bytes")
            print(f"   Tensor shape: {loaded_tensor.shape}")
            print(f"   Data type: {loaded_tensor.dtype}")
        else:
            print(f"\n{filename} does not exist")

def print_summary(tensors: List[Dict[str, Any]]) -> None:
    """
    Print summary information for all tensors
    
    Args:
        tensors: Tensor configuration list
    """
    print(f"\n{'='*50}")
    print("Summary Information")
    print(f"{'='*50}")
    print(f"Total tensors: {len(tensors)}")
    
    # Count data types
    datatype_count = {}
    for tensor in tensors:
        dt = tensor.get('datatype', 'Unknown')
        datatype_count[dt] = datatype_count.get(dt, 0) + 1
    
    print(f"\nData type distribution:")
    for dt, count in datatype_count.items():
        print(f"  - {dt}: {count} tensor(s)")
    
    print(f"\nMemory usage details:")
    total_memory = 0
    for tensor in tensors:
        memory_size = calculate_memory_size(tensor)
        total_memory += memory_size
        if memory_size >= 1024:
            print(f"  {tensor.get('name', 'Unknown')}: {memory_size:,.1f} bytes ({memory_size/1024:.2f} KB)")
        else:
            print(f"  {tensor.get('name', 'Unknown')}: {memory_size:,.1f} bytes")
    
    print(f"\nTotal memory usage: {total_memory:,.1f} bytes ({total_memory/1024:.2f} KB, {total_memory/1024/1024:.2f} MB)")

def main():

    json_file_path = "input_tensor.json"  
    
    output_dir = "in_data"
    
    print(f"Parsing file: {json_file_path}")
    print(f"Output directory: {output_dir}")

    # Parse JSON file
    tensors = parse_tensor_config(json_file_path)
    
    for i, tensor in enumerate(tensors):
        print_tensor_info(tensor, i)
    
    print_summary(tensors)
    
    # Create all tensors first
    created_tensors = create_all_tensors(tensors)
    
    # Save all tensors
    save_tensors_to_pt(created_tensors, tensors, output_dir)
    verify_saved_tensors(tensors, output_dir)


    # Calculate reference output with for gemm
    # in1 = created_tensors['in1_0_0']
    # wt = created_tensors['wt_0_0'].to(in1.dtype)  # Convert INT4/INT8 to match input dtype
    # orig = created_tensors['orig_0_0']
    
    # ref_out = in1 @ wt + orig
    # ref_out_filename = os.path.join(output_dir, "ref_out_mm.pt")
    # torch.save(ref_out, ref_out_filename)
    
    # print(f"\nSaved reference output: ref_out_mm.pt")
    # print(f"  Shape: {ref_out.shape}")
    # print(f"  Data type: {ref_out.dtype}")

    
    print(f"\nAll operations completed! Tensors have been saved to '{output_dir}' directory as .pt files.")

if __name__ == "__main__":
    main()