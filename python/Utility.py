# * * * * * * * * * * * * * * * * #
# Utility
# Author: <PERSON><PERSON><PERSON>
# * * * * * * * * * * * * * * * * #

import numpy as np
import torch
import struct


def datatype2width(datatype):
    if 'INT' in datatype:
        return int(datatype.split('INT')[1])
    elif 'FP' in datatype:
        return int(datatype.split('FP')[1])
    elif 'BF' in datatype:
        return int(datatype.split('BF')[1])
    else:
        raise Exception('Wrong datatype! datatype = ' + datatype)


def datatype2dtype(datatype):
    if datatype in ['INT4', 'INT8']:
        return np.int8
    elif datatype == 'INT16':
        return np.int16
    elif datatype == 'INT32':
        return np.int32
    elif datatype == 'INT64':
        return np.int64
    elif datatype == 'FP16':
        return np.float16
    elif datatype == 'FP32':
        return np.float32
    else:
        raise Exception('Wrong datatype! datatype = ' + datatype)


def datatype2type(datatype):
    if datatype in ['INT4', 'INT8']:
        return torch.int8
    elif datatype == 'INT16':
        return torch.int16
    elif datatype == 'INT32':
        return torch.int32
    elif datatype == 'INT64':
        return torch.int64
    elif datatype == 'FP16':
        return torch.float16
    elif datatype == 'FP32':
        return torch.float32
    else:
        raise Exception('Wrong datatype! datatype = ' + datatype)


def hexAddr_to_bin(hexAddr):
    # 输入 16 进制地址字符串，输出 2 进制地址的字符串
    # 其中输入地址字符串可能以 0x 为前缀，并且可能有下划线 _

    # 去除可能存在的 '0x' 前缀
    if hexAddr.lower().startswith('0x'):
        hexAddr = hexAddr[2:]
    # 将 16 进制转换为 2 进制
    binAddr = bin(int(hexAddr, 16))[2:]
    return binAddr


def float_to_int32(f: float) -> int:
    """
    将浮点数转换为带符号的 32 位整数（int32）
    超出范围时遵循 int32 的溢出规则（二进制补码截断）
    """
    # 先转换为 int64，再手动截断为 int32
    raw = int(f)  # Python 原生 int（无限精度）
    # 使用位掩码截断为 32 位（二进制补码）
    masked = raw & 0xFFFFFFFF  # 取低 32 位的无符号值
    # 如果最高位是 1（负数），转换为 Python 的负整数
    if masked & 0x80000000:
        return masked - 0x100000000
    else:
        return masked


import numpy as np
import struct

def show_inBinary(arr):
    """
    输入numpy数组，输出二进制表示的numpy数组，每个元素为字符串，宽度与numpy原有数据类型保持一致，需填充0
    参数：
    arr：numpy数组
    返回值：
    二进制表示的numpy数组，每个元素为字符串，宽度与numpy原有数据类型保持一致，需填充0
    """
    # 获取numpy数组的数据类型
    dtype = arr.dtype
    # 根据数据类型确定二进制表示的宽度
    if dtype == np.int8 or dtype == np.uint8:
        width = 8
    elif dtype == np.int16 or dtype == np.uint16:
        width = 16
    elif dtype == np.int32 or dtype == np.uint32:
        width = 32
    elif dtype == np.int64 or dtype == np.uint64:
        width = 64
    elif dtype == np.float16:
        width = 16
    elif dtype == np.float32:
        width = 32
    elif dtype == np.float64:
        width = 64
    else:
        raise ValueError("不支持的数据类型")

    # 定义转换函数
    def convert_to_binary(x):
        if dtype == np.float16:
            # 将float16转换为uint16，然后转换为二进制字符串
            binary_str = f'{np.array([x], dtype=np.float16).view(np.uint16)[0]:016b}'
        elif dtype == np.float32:
            # # 使用struct将float32转换为字节，然后转换为二进制字符串
            # byte_data = struct.pack('f', x)
            # binary_str = ''.join(f'{byte:08b}' for byte in byte_data)
            # 将float32转换为uint32，然后转换为二进制字符串
            binary_str = f'{np.array([x], dtype=np.float32).view(np.uint32)[0]:032b}'
        elif dtype == np.float64:
            # # 使用struct将float64转换为字节，然后转换为二进制字符串
            # byte_data = struct.pack('d', x)
            # binary_str = ''.join(f'{byte:08b}' for byte in byte_data)
            # 将float64转换为uint64，然后转换为二进制字符串
            binary_str = f'{np.array([x], dtype=np.float64).view(np.uint64)[0]:064b}'
        elif dtype == np.int8 or dtype == np.int16 or dtype == np.int32 or dtype == np.int64:
            x = int(x)
            # 对于有符号整数，先转换为无符号整数，然后转换为二进制字符串
            if dtype == np.int8:
                x = x & 0xFF
            elif dtype == np.int16:
                x = x & 0xFFFF
            elif dtype == np.int32:
                x = x & 0xFFFFFFFF
            elif dtype == np.int64:
                x = x & 0xFFFFFFFFFFFFFFFF
            binary_str = f'{x:0{width}b}'
        else:
            # 对于无符号整数，直接转换为二进制字符串并填充0
            binary_str = f'{x:0{width}b}'
        return binary_str

    # 使用numpy的vectorize函数将转换函数向量化
    bin_func = np.vectorize(convert_to_binary)
    # 对输入的numpy数组应用向量化的转换函数，得到二进制表示的numpy数组
    binary_arr = bin_func(arr)
    return binary_arr


# # 地址测试
# if __name__ == '__main__':
#     # 输入 16 进制地址
#     hex_address = input("请输入16进制地址：")
#     # 输出 2 进制地址
#     bin_address = hexAddr_to_bin(hex_address)
#     print("对应的2进制地址为：", bin_address)
#     print(bin_address[0:3])


# numpy转换为二进制测试
if __name__ == '__main__':
    # 测试整数数组
    arr_int = np.array([1, -2, 3, -4, 5], dtype=np.int8)
    print("整数数组：")
    print(arr_int)
    binary_arr_int = show_inBinary(arr_int)
    print("\n整数数组的二进制表示：")
    print(binary_arr_int)

    # 测试浮点数组
    arr_float16 = np.array([1.0, -2.5, 3.75, -4.125, 5.0], dtype=np.float16)
    print("\nfloat16数组：")
    print(arr_float16)
    binary_arr_float16 = show_inBinary(arr_float16)
    print("\nfloat16数组的二进制表示：")
    print(binary_arr_float16)

    arr_float32 = np.array([1.0, -2.5, 3.75, -4.125, 5.0], dtype=np.float32)
    print("\nfloat32数组：")
    print(arr_float32)
    binary_arr_float32 = show_inBinary(arr_float32)
    print("\nfloat32数组的二进制表示：")
    print(binary_arr_float32)
