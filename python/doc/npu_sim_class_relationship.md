```mermaid
classDiagram
    %% Abstract Base Classes
    class ModuleTemp {
        <<abstract>>
        +name: str
        +id: int
        +state: ModState
        +parent_core: NPUCore
        +run_prim(primitive)*
        +calculate_energy()*
    }
    
    class MemoryMod {
        <<abstract>>
        +address_space: dict
        +bit_width: float
        +op_energy: dict
        +record: dict
        +run_prim(primitive)*
    }
    
    %% Core Classes
    class PNMDie {
        +row: int
        +col: int
        +noc: Mesh2D
        +npu_cores: NPUCore[][]
        +golden_vm_system: GOLDEN_VM_SYSTEM
        +current_cycle: int
        +run_simulation_until()
        +execute_until(end_time)
        +end_simulation()
    }
    
    class NPUCore {
        +x: int
        +y: int
        +id: int
        +group: int
        +prim_pool: dict
        +dram_banks: DRAMBank[]
        +sram_banks: SRAMBank[]
        +vector_processing_unit: VPU
        +cim_cluster: CIMCluster
        +update(simulation_cycle)
        +prim_receive(dispatched_prim)
    }
    
    %% Memory Modules
    class DRAMBank {
        +buffer: ndarray
        +run_prim(primitive)
        +calculate_energy()
    }
    
    class SRAMBank {
        +buffer: ndarray
        +run_prim(primitive)
        +calculate_energy()
    }
    
    class CIMPage {
        +page_id: int
        +buffer: ndarray
        +run_prim(primitive)
        +calculate_energy()
    }
    
    %% Compute Modules
    class VPU {
        +vpe_num: int
        +run_prim(primitive)
        +calculate_energy()
    }
    
    class CIMCluster {
        +pages: CIMPage[]
        +macarray: MACArray
        +run_prim(primitive)
        +calculate_energy()
    }
    
    class LoadStoreUnit {
        +run_prim(primitive)
        +calculate_energy()
    }
    
    class ManageMod {
        +run_prim(primitive)
        +calculate_energy()
    }
    
    class NoCRouter {
        +port_type: str
        +noc: Mesh2D
        +run_prim(primitive)
        +calculate_energy()
    }
    
    %% Network Classes
    class Mesh2D {
        +row: int
        +col: int
        +nodes: Node2DMesh[][]
        +links_x: Link2DMeshX[][]
        +links_y: Link2DMeshY[][]
        +routing_request(master, slave)
    }
    
    class Node2DMesh {
        +x: int
        +y: int
        +master_port: bool
        +slave_port: bool
    }
    
    %% Data Structures
    class Primitive {
        +type: PrimName
        +tensor_in1: TensorInfo
        +tensor_in2: TensorInfo
        +tensor_out: TensorInfo
        +group: int
        +mask: int
    }
    
    class TensorInfo {
        +byte_base: int
        +byte_stride: tuple
        +size: tuple
        +type: str
        +width: int
        +layout: dict
        +memory: MemoryMod
    }
    
    %% Inheritance Relationships
    ModuleTemp <|-- MemoryMod
    ModuleTemp <|-- VPU
    ModuleTemp <|-- CIMCluster
    ModuleTemp <|-- LoadStoreUnit
    ModuleTemp <|-- ManageMod
    ModuleTemp <|-- NoCRouter
    
    MemoryMod <|-- DRAMBank
    MemoryMod <|-- SRAMBank
    MemoryMod <|-- CIMPage
    
    %% Composition Relationships
    PNMDie *-- NPUCore : contains
    PNMDie *-- Mesh2D : contains
    NPUCore *-- DRAMBank : contains
    NPUCore *-- SRAMBank : contains
    NPUCore *-- VPU : contains
    NPUCore *-- CIMCluster : contains
    NPUCore *-- LoadStoreUnit : contains
    NPUCore *-- ManageMod : contains
    NPUCore *-- NoCRouter : contains
    
    CIMCluster *-- CIMPage : contains
    Mesh2D *-- Node2DMesh : contains
    
    %% Association Relationships
    NPUCore --> Primitive : processes
    Primitive --> TensorInfo : references
    TensorInfo --> MemoryMod : stored_in
    NoCRouter --> Mesh2D : uses
```