## 1. Code Structure Analysis

### Directory Structure and Organization

The Python simulator is organized in the `/python/` directory with the following key structure:

**Core Simulation Files:**
- `SIMTop.py` - Top-level simulation orchestrator and main entry point
- `NPUCore.py` - Individual NPU core implementation with memory and compute modules
- `Modules.py` - Hardware module implementations (DRAM, SRAM, VPU, CIM, etc.)
- `NoC.py` - Network-on-Chip (2D mesh) implementation for inter-core communication
- `SIMTemplate.py` - Data structures and primitive definitions

**Configuration and Utilities:**
- `GlobalSettings.py` - System-wide configuration parameters and constants
- `Utility.py` - Helper functions for data type conversions and utilities
- `Visualize.py` - Simulation result visualization tools

**Interface and Communication:**
- `npu_demo.py` - Main demo/interface for integration with MoSIM
- `decoder.py` - Instruction decoder for converting RISC-V instructions to primitives
- `ipc_*.py` - Inter-process communication modules for external integration

**Golden Model and Backend:**
- `sim_backend_torch.py` - PyTorch-based golden model for verification
- `GoldenModel.py` - Reference implementation for correctness checking

### Main Modules and Their Purposes

## 2. Key Classes and Their Responsibilities

### Core Simulation Classes

**`PNMDie` (SIMTop.py)**
- **Role**: Top-level simulation orchestrator
- **Responsibilities**: 
  - Manages the entire NPU die with multiple cores arranged in a 2D mesh
  - Coordinates simulation cycles and primitive dispatch
  - Handles synchronization between cores
  - Interfaces with external systems (MoSIM integration)
  - Manages golden model verification system

````python path=python/SIMTop.py mode=EXCERPT
class PNMDie:
    def __init__(self, name, req_queue, rsp_queue, nice_interface, row, col, debug=False, gldres=False):
        # golden virtual machine
        self.golden_vm_system = GOLDEN_VM_SYSTEM()
        self.row = row
        self.col = col
        self.noc = NoC.Mesh2D(row=row, col=col)
        self.npu_cores = [[NPUCore.NPUCore(x=i, y=j, top=self, gldres=gldres, golden_vm_system=self.golden_vm_system) for j in range(row)] for i in range(col)]
````

**`NPUCore` (NPUCore.py)**
- **Role**: Individual NPU core simulation
- **Responsibilities**:
  - Contains memory modules (DRAM banks, SRAM banks)
  - Houses compute modules (VPU, CIM cluster)
  - Manages tensor operations (load/store, manipulation)
  - Handles NoC communication
  - Maintains primitive queues (waiting, running, finished)

````python path=python/NPUCore.py mode=EXCERPT
class NPUCore:
    def __init__(self, x, y, top, gldres=False, golden_vm_system: GOLDEN_VM_SYSTEM = None):
        # Primitive Pool
        self.prim_pool = {'WaitingPrims': SIMTemp.Queue(queue_name='WaitingPrims'),
                          'RunningPrims': SIMTemp.Queue(queue_name='RunningPrims'),
                          'FinishedPrims': SIMTemp.Queue(queue_name='FinishedPrims')}
        # Memory Modules
        self.dram_banks = []
        self.sram_banks = []
        # Compute Modules  
        self.vector_processing_unit = Mods.VPU(name='VPU', id=current_id, parent_core=self)
        self.cim_cluster = Mods.CIMCluster(name='CIMCluster', id=current_id, parent_core=self)
````

### Hardware Module Classes

**`ModuleTemp` (Modules.py)**
- **Role**: Abstract base class for all hardware modules
- **Responsibilities**: Defines common interface for energy calculation, primitive execution

**Memory Modules:**
- `DRAMBank`: High-capacity, slower memory
- `SRAMBank`: Fast scratchpad memory  
- `CIMPage`: Compute-in-memory storage

**Compute Modules:**
- `VPU`: Vector Processing Unit for element-wise operations
- `CIMCluster`: Compute-in-Memory cluster for matrix operations
- `LoadStoreUnit`: Tensor data movement
- `ManageMod`: Tensor manipulation (transpose, reshape, etc.)

**Communication Modules:**
- `NoCRouter`: Network-on-Chip router for inter-core communication

### Data Structure Classes

**`Primitive` (SIMTemplate.py)**
- **Role**: Represents a single operation to be executed
- **Responsibilities**: Contains operation type, tensor information, and execution parameters

**`TensorInfo` (SIMTemplate.py)**  
- **Role**: Describes tensor layout and memory mapping
- **Responsibilities**: Manages tensor dimensions, strides, data types, and memory locations

## 3. Code Execution Flow Diagram

## 4. Module Dependency Diagram

## 5. Class Relationship Diagram

## 6. Getting Started Guide

### Which Files to Read First

For newcomers to this codebase, I recommend following this reading order:

**1. Start with Configuration and Constants (15 minutes)**
- `GlobalSettings.py` - Understand system parameters, memory sizes, and performance settings
- `npu_constants.py` - Learn about instruction encodings and constants

**2. Understand Data Structures (20 minutes)**  
- `SIMTemplate.py` - Study `Primitive`, `TensorInfo`, and operation types
- Focus on `PrimName` enum to understand supported operations

**3. Explore Hardware Modules (30 minutes)**
- `Modules.py` - Start with `ModuleTemp` abstract class, then examine:
  - `DRAMBank` and `SRAMBank` for memory
  - `VPU` for vector processing
  - `CIMCluster` for compute-in-memory

**4. Study Core Architecture (25 minutes)**
- `NPUCore.py` - Understand how individual cores are structured
- Focus on the `__init__` method to see module instantiation
- Study `update()` and `prim_receive()` methods

**5. Examine Top-Level Orchestration (30 minutes)**
- `SIMTop.py` - Study `PNMDie` class and `run_simulation_until()` method
- Understand the main simulation loop and cycle management

**6. Learn Communication (20 minutes)**
- `NoC.py` - Understand 2D mesh network implementation
- `decoder.py` - See how RISC-V instructions become primitives

**7. Integration and Demo (15 minutes)**
- `npu_demo.py` - See how the simulator integrates with external systems

### Key Concepts and Terminology

**Core Concepts:**
- **Primitive**: A single operation (like matrix multiply, vector add) to be executed
- **NPU Core**: Individual processing unit with memory and compute modules  
- **Die**: Collection of NPU cores arranged in a 2D mesh
- **NoC (Network-on-Chip)**: Communication fabric between cores
- **CIM (Compute-in-Memory)**: Memory that can perform computations
- **Tensor**: Multi-dimensional data array with specific layout

**Important Terminology:**
- **Group/Mask**: Mechanism to selectively enable/disable cores for operations
- **LMEM**: Local memory (SRAM) addressing scheme
- **Primitive Pool**: Queues for waiting, running, and finished operations
- **Golden Model**: Reference implementation for correctness verification
- **Cycle-Accurate**: Simulation tracks exact timing of operations

## 7. Dependencies and Data Flow

### External Library Dependencies

**Core Dependencies:**
- **PyTorch**: Used in `sim_backend_torch.py` for golden model tensor operations
- **NumPy**: Used throughout for numerical computations and data arrays
- **Matplotlib**: Used in `Visualize.py` for generating performance charts

**Standard Library Dependencies:**
- `queue`: For thread-safe communication queues
- `socket`: For IPC communication with MoSIM
- `threading`: For concurrent execution
- `json`: For configuration and data serialization
- `enum`: For type-safe constants and enumerations

### Inter-Module Dependencies

**Configuration Flow:**
```
GlobalSettings.py → All modules (provides system parameters)
```

**Data Structure Flow:**
```
SIMTemplate.py → NPUCore.py → Modules.py
(Primitives and TensorInfo flow through the system)
```

**Simulation Flow:**
```
npu_demo.py → SIMTop.py → NPUCore.py → Modules.py
(Instructions become primitives, execute on hardware modules)
```

### Data Flow Between Components

**1. Instruction Processing Flow:**
```
RISC-V Instruction → decoder.py → Primitive → NPUCore → Hardware Modules
```

**2. Memory Data Flow:**
```
Input Tensors → DRAM Banks → SRAM Banks → Compute Modules → Output Tensors
```

**3. Inter-Core Communication Flow:**
```
Source Core → NoC Router → 2D Mesh Network → Destination Core
```

**4. Verification Flow:**
```
Simulation Results → Golden Model (PyTorch) → Comparison → Validation
```

## 8. Comprehensive Project Documentation

### What the Simulator Does

This is a **cycle-accurate, multi-core NPU (Neural Processing Unit) simulator** written in Python. It simulates a specialized processor architecture designed for AI/ML workloads, particularly neural network inference and training.

**Key Capabilities:**
- **Multi-Core Simulation**: Simulates up to 16 NPU cores (configurable 2×8 or 4×4 mesh)
- **Cycle-Accurate Timing**: Tracks exact execution cycles for performance analysis
- **Energy Modeling**: Calculates power consumption for different operations
- **Memory Hierarchy**: Models DRAM, SRAM, and Compute-in-Memory (CIM)
- **Network-on-Chip**: Simulates inter-core communication via 2D mesh
- **Golden Model Verification**: Uses PyTorch for correctness checking

### How Multi-Core NPU Simulation Works

#### Architecture Overview

The simulator models a **Processing-Near-Memory (PNM) die** with the following hierarchy:

```
PNM Die
├── 2D Mesh Network-on-Chip (NoC)
├── NPU Core 0,0 ── NPU Core 0,1 ── ... ── NPU Core 0,7
├── NPU Core 1,0 ── NPU Core 1,1 ── ... ── NPU Core 1,7
└── ...
```

Each NPU Core contains:
- **Memory Modules**: 1 DRAM bank (128MB), 4 SRAM banks (scratchpad)
- **Compute Modules**: Vector Processing Unit (VPU), CIM Cluster
- **Data Movement**: Tensor Load/Store Unit, Tensor Manipulation Unit
- **Communication**: NoC Router (TX/RX)

#### Simulation Process

**1. Instruction Decode Phase:**
- RISC-V instructions arrive via IPC from MoSIM
- `decoder.py` converts instructions to internal `Primitive` objects
- Primitives specify operation type, input/output tensors, and target cores

**2. Primitive Dispatch:**
- `PNMDie` maintains a global instruction queue
- Primitives are broadcast to all cores based on group/mask settings
- Each core decides whether to execute based on its group membership

**3. Core-Level Execution:**
- Each `NPUCore` maintains three queues: Waiting → Running → Finished
- Primitives move through queues as resources become available
- Hardware modules (`VPU`, `CIMCluster`, etc.) execute operations

**4. Cycle-Accurate Timing:**
- Every operation has realistic latency (e.g., DRAM access: 40 cycles)
- Modules track when they become available for next operation
- Global simulation advances cycle-by-cycle

**5. Inter-Core Communication:**
- NoC primitives enable data transfer between cores
- 2D mesh routing with XY or YX algorithms
- Bandwidth and latency modeling for realistic performance

### Key Algorithms and Simulation Logic

#### 1. Primitive Scheduling Algorithm

````python path=python/SIMTop.py mode=EXCERPT
# Main simulation loop in run_simulation_until()
while not self.simulation_end:
    # Update all NPU cores
    for i in range(self.col):
        for j in range(self.row):
            self.npu_cores[i][j].update(simulation_cycle=self.current_cycle)
    
    # Check if all cores are stalled
    all_npu_stall = True
    for i in range(self.col):
        for j in range(self.row):
            if not self.npu_cores[i][j].whether_stall():
                all_npu_stall = False
                break
````

#### 2. Memory Address Translation

The simulator uses a sophisticated memory layout system:
- **LMEM addressing**: 256-bit word-based addressing for local memory
- **Tensor layout**: Multi-dimensional tensors with configurable strides
- **Address spaces**: Separate ranges for DRAM, SRAM, and CIM pages

#### 3. Energy Modeling

Each hardware module tracks energy consumption:

````python path=python/Modules.py mode=EXCERPT
def calculate_energy(self):
    read_energy = self.op_energy['Read_Byte'] * self.record['Read_Byte_Num']
    write_energy = self.op_energy['Write_Byte'] * self.record['Write_Byte_Num']
    return read_energy + write_energy
````

#### 4. NoC Routing Algorithm

The Network-on-Chip uses deterministic routing:

````python path=python/NoC.py mode=EXCERPT
def routing_request(self, master, slave):
    if self.routing_algo == 'XY':
        # XY routing: first route in X-direction, then in Y-direction
        # Check if all links in the path are available
        # Reserve links if path is clear
````

### Integration with MoSIM

The simulator is designed to integrate with **MoSIM** (a larger system simulator):
- **IPC Communication**: Socket-based communication for instruction exchange
- **Cycle Synchronization**: MoSIM drives simulation to specific cycles
- **Response Handling**: Returns execution results and timing information

### Performance and Accuracy

**Simulation Speed**: Approximately 1000-10000 simulated cycles per second (depends on workload complexity)

**Accuracy**: Cycle-accurate timing with realistic latencies based on hardware specifications

**Validation**: Golden model verification ensures functional correctness

This simulator serves as a crucial tool for:
- **Architecture Exploration**: Evaluating different NPU configurations
- **Performance Analysis**: Understanding bottlenecks and optimization opportunities  
- **Software Development**: Testing and optimizing AI workloads
- **Hardware Design**: Validating architectural decisions before silicon implementation

## Summary

I've provided you with a comprehensive analysis of the multi-core NPU Python simulator project. Here's what we've covered:

✅ **Code Structure Analysis**: Detailed breakdown of the directory structure and main modules
✅ **Key Classes Documentation**: Identified and documented the main classes and their responsibilities  
✅ **Visual Documentation**: Created three Mermaid diagrams showing:
   - Code execution flow from initialization to completion
   - Module dependency relationships
   - Object-oriented class structure and inheritance
✅ **Getting Started Guide**: Provided a logical reading order and key concepts for newcomers
✅ **Dependencies and Data Flow**: Documented external libraries and inter-module relationships
✅ **Comprehensive Documentation**: Explained the simulator's purpose, architecture, and key algorithms

### Key Takeaways for New Contributors:

1. **Start with `GlobalSettings.py` and `SIMTemplate.py`** to understand the system configuration and data structures
2. **The simulator is cycle-accurate** and models realistic hardware timing and energy consumption
3. **It's designed for integration with MoSIM** via IPC communication
4. **The architecture follows a clear hierarchy**: Die → Cores → Modules → Operations
5. **PyTorch is used for golden model verification** to ensure correctness

The codebase is well-structured with clear separation of concerns, making it relatively approachable for newcomers who follow the suggested reading order. The extensive use of object-oriented design and abstract base classes provides good extensibility for adding new hardware modules or operations.

Would you like me to dive deeper into any specific aspect of the simulator, such as a particular module's implementation or the instruction decoding process?
