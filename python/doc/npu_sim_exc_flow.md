```mermaid
flowchart TD
    A[Start: npu_demo.py] --> B[Initialize Subsystem]
    B --> C[Create PNMDie with NPU cores]
    C --> D[Initialize NoC mesh network]
    D --> E[Setup IPC server for MoSIM]
    E --> F[Data import from input files]
    F --> G[Wait for instruction requests]
    
    G --> H{Receive IPC Request?}
    H -->|Yes| I[Decode instruction type]
    H -->|No| G
    
    I --> J{Request Type?}
    J -->|NICE_INSTR| K[Decode RISC-V instruction]
    J -->|VNICE_INSTR| L[Decode vector instruction]
    J -->|CHECK| M[Check for completed operations]
    J -->|NICE_READY| N[Return readiness status]
    
    K --> O[Convert to primitive]
    L --> O
    O --> P[Add to request queue]
    P --> Q[Execute until target cycle]
    
    Q --> R[Simulation Loop: run_simulation_until]
    R --> S[Update all NPU cores]
    S --> T[Check if all cores stalled]
    T --> U{Primitives in queue?}
    
    U -->|Yes| V[Dispatch primitive to cores]
    U -->|No| W[Calculate next cycle]
    
    V --> X[Cores execute primitives]
    X --> Y[Update module states]
    Y --> Z[Record energy/performance]
    Z --> AA[Check completion]
    
    AA --> BB{Simulation complete?}
    BB -->|No| S
    BB -->|Yes| CC[Generate response]
    
    W --> BB
    CC --> DD[Send IPC response]
    DD --> G
    
    M --> EE[Check response queue]
    EE --> FF[Return completed results]
    FF --> G
    
    N --> GG[Return queue status]
    GG --> G
    
    style A fill:#e1f5fe
    style R fill:#fff3e0
    style X fill:#f3e5f5
    style CC fill:#e8f5e8
```