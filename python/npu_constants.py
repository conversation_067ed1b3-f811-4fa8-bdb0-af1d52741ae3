from enum import IntEnum, Enum
from typing import List


class NPU_CONFIG:
    GROUP_NUM = 4
    NPU_NUM   = 4


class DTYPE(IntEnum):
    TYPE_INT   = 0
    TYPE_FP    = 1
    TYPE_BF    = 2
    TYPE_BBF   = 3


class WIDTH(IntEnum):
    WIDTH_4    = 2
    WIDTH_8    = 3
    WIDTH_16   = 4
    WIDTH_32   = 5


class OPTYPE(IntEnum):
    ADD                = 0   # 0b000000
    SUB                = 1   # 0b000001
    RSUB               = 2   # 0b000010
    MIN                = 8   # 0b001000
    MAX                = 9   # 0b001001
    EQUAL              = 16  # 0b010000
    NOT_EQUAL          = 17  # 0b010001
    GREATER            = 18  # 0b010010
    GREATER_OR_EQUAL   = 19  # 0b010011
    LESS               = 20  # 0b010100
    LESS_OR_EQUAL      = 21  # 0b010101
    LEFT_SHIFT         = 24  # 0b011000
    RIGHT_SHIFT_FLOOR  = 25  # 0b011001
    RIGHT_SHIFT_CEIL   = 26  # 0b011010
    RIGHT_SHIFT_ROUND  = 27  # 0b011011
    AND                = 32  # 0b100000
    OR                 = 33  # 0b100001
    XOR                = 34  # 0b100010
    MUL                = 40  # 0b101000


class InstGroup(IntEnum):
    CONTROL = 0
    NOC     = 1 
    TLS     = 2 # Tensor load/store
    TM      = 3 # Tensor movement
    TP      = 4 # Tensor processing
    VP      = 5 # Vector processing
    