# * * * * * * * * * * * * * * * * #
# Simulator Input Conversion Script
# Author: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
# * * * * * * * * * * * * * * * * #

import SIMTemplate as SIMTemp
import logging

# 配置日志
logging.basicConfig(
    filename="logs/prims.log",  # 指定日志文件
    filemode="w",
    format="%(asctime)s - %(levelname)s - %(message)s",
    level=logging.INFO
)

# Dataflow Testbench

# gqa_decode
# Dataflow_Name = 'gqa_decode'
# Dataflow_Name = 'gqa_decode_qkvgen_operator'
Dataflow_Name = 'gqa_decode_flashattention_operator'
# Dataflow_Name = 'gqa_decode_output_operator'
# Dataflow_Name = 'gqa_decode_allreduce_operator'
# Dataflow_Name = 'ffn_decode_operator'
# Dataflow_Name = 'ffn_decode_operator_all_reduce'
primitive_file = './tb_dataflow/' + Dataflow_Name + '.txt'

# Operarator Testbench
# Operator_Name = 'load_operator_int32'
# Operator_Name = 'store_operator_int32'
# Operator_Name = 'add_operator_int32'
# Operator_Name = 'sub_operator_int32'
# Operator_Name = 'clip_operator_int32'
# Operator_Name = 'add_scalar_operator_int8'
# Operator_Name = 'sub_scalar_operator_int8'
# Operator_Name = 'mul_tensor_operator_fp16'
# Operator_Name = 'mul_scalar_operator_fp16'
# Operator_Name = 'equal_operator_int8'
# Operator_Name = 'not_equal_operator_int8'
# Operator_Name = 'less_operator_fp16'
# Operator_Name = 'abs_operator_fp16'
# Operator_Name = 'exp_operator_fp16'
# Operator_Name = 'reciprocal_operator_fp16'
# Operator_Name = 'squareroot_operator_fp16'
# Operator_Name = 'signfunction_operator_fp16'
# Operator_Name = 'gemm_operator_int4'
# Operator_Name = 'gemv_operator_int4'
# Operator_Name = 'rmsnorm_batch_operator_fp16'
# primitive_file = './tb_operator/' + Operator_Name + '.txt'


def prims_init(riscv_prims, file=primitive_file, debug=False):
    try:
        with open(file, 'r') as f:
            operator = SIMTemp.Operator()       # 创建新 Operator
            primitive = None                    # 避免错误复用
            in1_dims = {}
            in2_dims = {}                       # 确保初始化
            out_dims = {}                       # 确保初始化
            orig_dims = {}

            prim_id = 0
            op_id = 0
            line_id = 0

            for line in f:
                line = line.strip()

                if debug:
                    print(f"{line_id} 读取行: {line}")       # 打印读取的每一行
                    line_id += 1

                if not line:
                    continue                    # 跳过空行

                # 跳过无关的头部信息
                if (line.startswith("Nuclei SDK Build Time") or
                        line.startswith("Download Mode") or
                        line.startswith("CPU Frequency") or
                        line.startswith("CPU HartID")):
                    continue

                # 用于在输入文件中添加注释
                if "#" in line:
                    continue

                # 如果行中含有 "Operator:"，则提取 operator 类型（例如 Add_operator）
                if "Operator:" in line:
                    parts = line.split("Operator:")
                    if len(parts) > 1:
                        type_str = parts[1].strip()
                        operator.type = getattr(SIMTemp.OpName, type_str, SIMTemp.OpName.UNKNOWN)
                        operator.op_id = op_id
                        op_id += 1
                        prim_id = 0
                    continue

                if "=" in line:
                    key, value = line.split("=", 1)
                    key, value = key.strip(), value.strip()
                    # 解析 NPU 相关参数
                    if key == "npu_group" and operator:
                        operator.npu_group = int(value)
                    elif key == "npu_mask" and operator:
                        operator.npu_mask = int(value)

                # 如果行中含有 "Primitive:"，则提取 primitive 类型（例如 TLD）
                if "Primitive:" in line:
                    if primitive:  # **这里需要先完成旧的 Primitive 处理**
                        if len(in1_dims) == 3:
                            primitive.tensor_in1.size = (in1_dims.get(2), in1_dims.get(1), in1_dims.get(0))
                        elif len(in1_dims) == 2:
                            primitive.tensor_in1.size = (1, in1_dims.get(1), in1_dims.get(0))
                        elif len(in1_dims) == 1:
                            primitive.tensor_in1.size = (1, 1, in1_dims.get(0))

                        if len(in2_dims) == 3:
                            primitive.tensor_in2.size = (in2_dims.get(2), in2_dims.get(1), in2_dims.get(0))
                        elif len(in2_dims) == 2:
                            primitive.tensor_in2.size = (1, in2_dims.get(1), in2_dims.get(0))
                        elif len(in2_dims) == 1:
                            primitive.tensor_in2.size = (1, 1, in2_dims.get(0))

                        if len(out_dims) == 3:
                            primitive.tensor_out.size = (out_dims.get(2), out_dims.get(1), out_dims.get(0))
                        elif len(out_dims) == 2:
                            primitive.tensor_out.size = (1, out_dims.get(1), out_dims.get(0))
                        elif len(out_dims) == 1:
                            primitive.tensor_out.size = (1, 1, out_dims.get(0))

                        if len(orig_dims) == 3:
                            primitive.tensor_orig.size = (orig_dims.get(2), orig_dims.get(1), orig_dims.get(0))
                        elif len(orig_dims) == 2:
                            primitive.tensor_orig.size = (1, orig_dims.get(1), orig_dims.get(0))
                        elif len(orig_dims) == 1:
                            primitive.tensor_orig.size = (1, 1, orig_dims.get(0))

                    # **创建新的 Primitive**
                    parts = line.split("Primitive:")
                    if len(parts) > 1:
                        type_str = parts[1].strip()
                        primitive = SIMTemp.Primitive(prim_id=prim_id)
                        prim_id += 1
                        primitive.type = getattr(SIMTemp.PrimName, type_str, SIMTemp.PrimName.UNKNOWN)
                        operator.primitives.push(primitive)  # **这里要确保存入的是完整的 primitive**

                        # **重置相关变量**
                        in1_dims, in2_dims, out_dims, orig_dims = {}, {}, {}, {}
                    continue

                # 如果行中含有 "VectorProcess:"，则提取 VectorProcess 类型（例如 ADD）
                if "VectorProcess:" in line:
                    parts = line.split("VectorProcess:")
                    if len(parts) > 1:
                        type_str = parts[1].strip()
                        primitive.vector_op = getattr(SIMTemp.VectorProcess, type_str, SIMTemp.VectorProcess.UNKNOWN)
                    continue

                # 对含有 '=' 的行进行键值对解析
                if "=" in line:
                    key, value = line.split("=", 1)
                    key = key.strip()
                    value = value.strip()
                    # 解析 Group, Mask 信息
                    if key == "npu_group":
                        primitive.npu_group = int(value)
                        continue
                    if key == "npu_mask":
                        primitive.npu_mask = int(value)
                        continue
                    # 解析 Switch CIMC 信息：
                    if key == "swcimc_node":
                        primitive.cimc_mode = int(value)
                        continue
                    # 解析 Loop Number 信息
                    if key == "Number_of_Loops":
                        primitive.loop_num = int(value)
                        continue
                    # 解析 Tensor_in1 信息
                    if key == "tensor_size_dim0_in1":
                        in1_dims[0] = int(value)
                        continue
                    if key == "tensor_size_dim1_in1":
                        in1_dims[1] = int(value)
                        continue
                    if key == "tensor_size_dim2_in1":
                        in1_dims[2] = int(value)
                        continue
                    if key == "tensor_type_in1":
                        primitive.tensor_in1.type = value  # 如 "INT"
                        continue
                    if key == "tensor_wd_in1":
                        primitive.tensor_in1.width = int(value)
                        continue
                    if key == "byte_stride_in1":
                        primitive.tensor_in1.byte_stride = int(value)
                        continue
                    if key == "base_addr_in1":
                        primitive.tensor_in1.byte_base = int(value, 16)
                        continue
                    # 解析 Tensor_in2 信息
                    if key == "val_in2":
                        primitive.scalar_val = int(value, 16)
                        continue
                    if key == "tensor_size_dim0_in2":
                        in2_dims[0] = int(value)
                        continue
                    if key == "tensor_size_dim1_in2":
                        in2_dims[1] = int(value)
                        continue
                    if key == "tensor_size_dim2_in2":
                        in2_dims[2] = int(value)
                        continue
                    if key == "tensor_type_in2":
                        primitive.tensor_in2.type = value  # 如 "INT"
                        continue
                    if key == "tensor_wd_in2":
                        primitive.tensor_in2.width = int(value)
                        continue
                    if key == "byte_stride_in2":
                        primitive.tensor_in2.byte_stride = int(value)
                        continue
                    if key == "base_addr_in2":
                        primitive.tensor_in2.byte_base = int(value, 16)
                        continue
                    # 解析 Tensor_out 信息
                    if key == "tensor_size_dim0_out":
                        out_dims[0] = int(value)
                        continue
                    if key == "tensor_size_dim1_out":
                        out_dims[1] = int(value)
                        continue
                    if key == "tensor_size_dim2_out":
                        out_dims[2] = int(value)
                        continue
                    if key == "tensor_type_out":
                        primitive.tensor_out.type = value
                        continue
                    if key == "tensor_wd_out":  # 确保键名一致
                        primitive.tensor_out.width = int(value)
                        continue
                    if key == "byte_stride_out":
                        primitive.tensor_out.byte_stride = int(value)
                        continue
                    if key == "base_addr_out":
                        primitive.tensor_out.byte_base = int(value, 16)
                        continue
                    # 解析Tensor_orig信息
                    if key == "tensor_size_dim0_orig":
                        orig_dims[0] = int(value)
                        continue
                    if key == "tensor_size_dim1_orig":
                        orig_dims[1] = int(value)
                        continue
                    if key == "tensor_size_dim2_orig":
                        orig_dims[2] = int(value)
                        continue
                    if key == "tensor_type_orig":
                        primitive.tensor_orig.type = value
                        continue
                    if key == "tensor_wd_orig":
                        primitive.tensor_orig.width = int(value)
                        continue
                    if key == "byte_stride_orig":
                        primitive.tensor_orig.byte_stride = int(value)
                        continue
                    if key == "base_addr_orig":
                        primitive.tensor_orig.byte_base = int(value, 16)
                        continue
                    # 解析ConvInfo信息
                    if key == "conv_byte_base_wt":
                        primitive.conv_settings.byte_base_wt = int(value, 16)
                        continue
                    if key == "conv_type_data":
                        primitive.conv_settings.type = value
                        continue
                    if key == "conv_wd_data":
                        primitive.conv_settings.width = int(value)
                        continue
                    if key == "conv_accumulate":
                        primitive.conv_settings.accu = int(value)
                        continue
                    if key == "conv_activate":
                        primitive.conv_settings.act = int(value)
                        continue
                    if key == "conv_shift":
                        primitive.conv_settings.shift = int(value)
                        continue
                    if key == "conv_size_x":
                        primitive.conv_settings.cfg_k_x = int(value)
                        continue
                    if key == "conv_size_y":
                        primitive.conv_settings.cfg_k_y = int(value)
                        continue
                    if key == "conv_slide_x":
                        primitive.conv_settings.slide_x = int(value)
                        continue
                    if key == "conv_slide_y":
                        primitive.conv_settings.slide_y = int(value)
                        continue
                    if key == "conv_padding_w":
                        primitive.conv_settings.pad_w = int(value)
                        continue
                    if key == "conv_padding_n":
                        primitive.conv_settings.pwd_n = int(value)
                        continue
                    if key == "conv_padding_value":
                        primitive.conv_settings.pad_val = int(value)
                        continue
                    # 解析NoC信息
                    if key == "src_idx":
                        primitive.noc_settings.src_id = int(value, 16) % pow(2, 16)
                        primitive.noc_settings.src_group = int(value, 16) // pow(2, 16)
                        continue
                    if key == "dest_idx":
                        primitive.noc_settings.dst_id = int(value, 16) % pow(2, 16)
                        primitive.noc_settings.dst_group = int(value, 16) // pow(2, 16)
                        continue
                    if key == "base_addr_src":
                        primitive.noc_settings.src_addr = int(value, 16)
                        continue
                    if key == "base_addr_dest":
                        primitive.noc_settings.dst_addr = int(value, 16)
                        continue

                # 组合尺寸信息
                if len(in1_dims) == 3:
                    primitive.tensor_in1.size = (in1_dims.get(2), in1_dims.get(1), in1_dims.get(0))
                elif len(in1_dims) == 2:
                    primitive.tensor_in1.size = (1, in1_dims.get(1), in1_dims.get(0))
                elif len(in1_dims) == 1:
                    primitive.tensor_in1.size = (1, 1, in1_dims.get(0))

                if len(in2_dims) == 3:
                    primitive.tensor_in2.size = (in2_dims.get(2), in2_dims.get(1), in2_dims.get(0))
                elif len(in2_dims) == 2:
                    primitive.tensor_in2.size = (1, in2_dims.get(1), in2_dims.get(0))
                elif len(in2_dims) == 1:
                    primitive.tensor_in2.size = (1, 1, in2_dims.get(0))

                if len(out_dims) == 3:
                    primitive.tensor_out.size = (out_dims.get(2), out_dims.get(1), out_dims.get(0))
                elif len(out_dims) == 2:
                    primitive.tensor_out.size = (1, out_dims.get(1), out_dims.get(0))
                elif len(out_dims) == 1:
                    primitive.tensor_out.size = (1, 1, out_dims.get(0))

                if len(orig_dims) == 3:
                    primitive.tensor_orig.size = (orig_dims.get(2), orig_dims.get(1), orig_dims.get(0))
                elif len(orig_dims) == 2:
                    primitive.tensor_orig.size = (1, orig_dims.get(1), orig_dims.get(0))
                elif len(orig_dims) == 1:
                    primitive.tensor_orig.size = (1, 1, orig_dims.get(0))

            # **循环结束，但最后一个 Primitive 信息需要处理**
            # 组合尺寸信息
            if len(in1_dims) == 3:
                primitive.tensor_in1.size = (in1_dims.get(2), in1_dims.get(1), in1_dims.get(0))
            elif len(in1_dims) == 2:
                primitive.tensor_in1.size = (1, in1_dims.get(1), in1_dims.get(0))
            elif len(in1_dims) == 1:
                primitive.tensor_in1.size = (1, 1, in1_dims.get(0))

            if len(in2_dims) == 3:
                primitive.tensor_in2.size = (in2_dims.get(2), in2_dims.get(1), in2_dims.get(0))
            elif len(in2_dims) == 2:
                primitive.tensor_in2.size = (1, in2_dims.get(1), in2_dims.get(0))
            elif len(in2_dims) == 1:
                primitive.tensor_in2.size = (1, 1, in2_dims.get(0))

            if len(out_dims) == 3:
                primitive.tensor_out.size = (out_dims.get(2), out_dims.get(1), out_dims.get(0))
            elif len(out_dims) == 2:
                primitive.tensor_out.size = (1, out_dims.get(1), out_dims.get(0))
            elif len(out_dims) == 1:
                primitive.tensor_out.size = (1, 1, out_dims.get(0))

            if len(orig_dims) == 3:
                primitive.tensor_orig.size = (orig_dims.get(2), orig_dims.get(1), orig_dims.get(0))
            elif len(orig_dims) == 2:
                primitive.tensor_orig.size = (1, orig_dims.get(1), orig_dims.get(0))
            elif len(orig_dims) == 1:
                primitive.tensor_orig.size = (1, 1, orig_dims.get(0))

            # 将填充好的 Primitive 对象添加到队列中
            riscv_prims.push(operator)

        # # 打印 riscv_prims.array 中的信息
        # if len(riscv_prims.array) == 0:
        #     print("riscv_prims.array 是空的")
        # else:
        #     for p in riscv_prims.array:
        #         print(p)

    except FileNotFoundError:
        print(f"文件 '{file}' 未找到！")


if __name__ == '__main__':
    rv_prims = SIMTemp.Queue(queue_name='RVPrims')
    print(rv_prims)
    prims_init(rv_prims, debug=False)
    print(rv_prims)
    print('----------')
    for id in range(0, len(rv_prims.top().primitives.array)):
        prim_id = id
        print('Monitoring top operator')
        target = rv_prims.top().primitives.array[prim_id]
        print(target)
        print('in1', target.tensor_in1)
        print('in2', target.tensor_in2)
        print('out', target.tensor_out)
        print('orig', target.tensor_orig)
        print(target.conv_settings)
        print('control: cimc_mode =', target.cimc_mode, ', group =', target.npu_group, ', mask =', target.npu_mask)
        print('')
