########################################
# Name: Golden model
# Developer: <PERSON><PERSON><PERSON>
# Date: 25.06.20
########################################

import torch
import numpy as np
import math
import GlobalSettings as GlbSet
import Utility as Utils
import inspect


def tensor2buffer(tensor, offset, strides, datatype, buffer):

    # DESCRIPTION
    # function: tensor to buffer
    # this function will save a 3-D tensor(a ndarray),
    # which originally ordered in [H, W, C] or [dim2, dim1, dim0],
    # in [H, W, C/pack_size, pack_size] or [dim2, dim1, dim0b, dim0a]way
    # pack_size * data_width = WD_BANK = 256b
    # data in buffer is stored in the form of byte(8 bits)
    # currently supports INT4/8/16/32/64, FP16/32

    # INPUT
    # tensor: a 3-D tensor ordered in [H, W, C] or [dim2, dim1, dim0]
    # offset: offset of address
    # strides: [stride_dim2, stride_dim1, stride_dim0b]
    # datatype: INT4/8/16/32/64, FP16/32
    # buffer: base address of target buffer

    # check tensor's dimension
    if tensor.ndim != 1 and tensor.ndim != 2 and tensor.ndim != 3:  # tensor
        raise Exception('Wrong tensor dim! should be 1 or 2 or 3, but ndim=' + str(tensor.ndim))

    # 检查是否存在越界
    if datatype in ['INT4', 'INT8', 'INT16', 'INT32', 'INT64']:
        width = int(datatype.split('INT')[1])
        upper_bound = pow(2, width-1) - 1
        lower_bound = -pow(2, width-1)
    elif datatype == 'FP16':
        upper_bound = np.float16(np.finfo(np.float16).max)
        lower_bound = np.float16(np.finfo(np.float16).min)
    elif datatype == 'FP32':
        upper_bound = np.float32(np.finfo(np.float32).max)
        lower_bound = np.float32(np.finfo(np.float32).min)
    else:
        raise Exception('Wrong datatype! datatype=' + datatype)
    if np.amax(tensor) > upper_bound or np.amin(tensor) < lower_bound:
        raise Exception('Overflow! (upper_bound, lower_bound) = (' + str(upper_bound) + ', ' + str(lower_bound)+')'
                        + ' while (max_num, min_num) = (' + str(np.amax(tensor)) + ', ' + str(np.amin(tensor))+')')

    # 检查地址是否符合要求
    bank_row_size = GlbSet.LMEM_WD // 8
    if offset % bank_row_size != 0:
        raise Exception('Wrong offset! offset = ' + str(offset) + ' while bank_row_size = ' + str(bank_row_size))

    # 进行补 0 操作，并检查 strides 是否符合要求:
    # 1）stride_dim0b 恒等于 1
    # 2）stride_dim1 不小于 size_dim0b
    # 3）stride_dim2 不小于 stride_dim1×size_dim1，且必须是 stride_dim1 的整数倍
    size_dim0 = tensor.shape[2]
    (s_dim2, s_dim1, s_dim0b) = strides
    size_dim0a = GlbSet.LMEM_WD // Utils.datatype2width(datatype)
    size_dim0b = math.ceil(size_dim0 / size_dim0a)
    if tensor.ndim == 3:
        size_dim2, size_dim1 = tensor.shape[0], tensor.shape[1]
        # dim0 维度进行填充，constant 默认填充 0
        tensor = np.pad(tensor, [(0, 0), (0, 0), (0, size_dim0a * size_dim0b - size_dim0)], mode='constant')
        if s_dim0b != 1:
            raise Exception('Stride of dim0b is NOT 1 ! s_dim0b = ' + str(s_dim0b))
        if s_dim1 < size_dim0b:
            raise Exception('Stride of dim1 is LESS than size of dim0b! s_dim1 = ' +
                            str(s_dim1) + ', size_dim0b = ' + str(size_dim0b))
        if s_dim2 < s_dim1 * size_dim1:
            raise Exception('Stride of dim2 is LESS than the product of size of dim0b and size of dim1! s_dim2 = ' +
                            str(s_dim2) + ', s_dim1 * size_dim1 = ' + str(s_dim1 * size_dim1))
        if s_dim2 % s_dim1 != 0:
            raise Exception('Stride of dim2 is not an integer multiple of stride of dim1, s_dim2 = ' +
                            str(s_dim2) + ', s_dim1 = ' + str(s_dim1))
    elif tensor.ndim == 2:
        size_dim2, size_dim1 = 1, tensor.shape[1]
        # dim0 维度进行填充，constant 默认填充 0
        tensor = np.pad(tensor, [(0, 0), (0, size_dim0a * size_dim0b - size_dim0)], mode='constant')
        tensor = tensor.reshape([size_dim2, size_dim1, size_dim0b*size_dim0a])
        if s_dim0b != 1:
            raise Exception('Stride of dim0b is NOT 1 ! s_dim0b = ' + str(s_dim0b))
        if s_dim1 < size_dim0b:
            raise Exception('Stride of dim1 is LESS than size of dim0b! s_dim1 = ' +
                            str(s_dim1) + ', size_dim0b = ' + str(size_dim0b))
    else:
        size_dim2, size_dim1 = 1, 1
        tensor = np.pad(tensor, [(0, size_dim0a * size_dim0b - size_dim0)], mode='constant')
        tensor = tensor.reshape([size_dim2, size_dim1], size_dim0b * size_dim0a)
        if s_dim0b != 1:
            raise Exception('Stride of dim0b is NOT 1 ! s_dim0b = ' + str(s_dim0b))

    # 将 tensor 存入 buffer 中
    if datatype == 'INT4':
        # 特殊情况，两个 int4 的值凑成一个 byte 存储
        # 对于数据类型 int4 的情况，要求原始数据用 int8 格式进行存储
        if tensor.dtype != np.int8:
            raise Exception('Datatype INT4 not stored in np.int8!')
        # 原始存储格式为 int8, 需对其进行一些移位拼接操作
        tensor1 = tensor[:, :, ::2]
        tensor2 = tensor[:, :, 1::2]
        tensor1u = tensor1.copy().astype(np.uint8)
        tensor2u = tensor2.copy().astype(np.uint8)
        tensor1final = np.right_shift(np.left_shift(tensor1u, 4), 4)
        tensor2final = np.left_shift(tensor2u, 4)
        tensor3 = tensor1final + tensor2final
        size_dim0a = size_dim0a // 2
        tensor3 = tensor3.reshape(size_dim2, size_dim1, -1, size_dim0a)
        for cur_dim2 in range(size_dim2):
            for cur_dim1 in range(size_dim1):
                for cur_dim0b in range(size_dim0b):
                    cur_addr = offset + (cur_dim1 * s_dim1 + cur_dim2 * s_dim2 + cur_dim0b * s_dim0b) * bank_row_size
                    buffer[cur_addr:cur_addr+bank_row_size] = tensor3[cur_dim2, cur_dim1, cur_dim0b]
    else:
        # check whether datatype corresponds to stored type
        if datatype == 'INT8' and tensor.dtype != np.int8:
            raise Exception('Datatype INT8 not stored in np.int8!')
        elif datatype == 'INT16' and tensor.dtype != np.int16:
            raise Exception('Datatype INT16 not stored in np.int16!')
        elif datatype == 'INT32' and tensor.dtype != np.int32:
            raise Exception('Datatype INT32 not stored in np.int32!')
        elif datatype == 'INT64' and tensor.dtype != np.int64:
            raise Exception('Datatype INT64 not stored in np.int64!')
        elif datatype == 'FP16' and tensor.dtype != np.float16:
            raise Exception('Datatype FP16 not stored in np.float16!')
        elif datatype == 'FP32' and tensor.dtype != np.float32:
            raise Exception('Datatype FP32 not stored in np.float32!')

        # print('aa before', tensor, tensor.shape)
        tensor = tensor.reshape((size_dim2, size_dim1, -1, size_dim0a))
        # print('aa after', tensor, tensor.shape, offset)
        # print('aaa', tensor, offset)
        for cur_dim2 in range(size_dim2):
            for cur_dim1 in range(size_dim1):
                for cur_dim0b in range(size_dim0b):
                    cur_addr = offset + (cur_dim1 * s_dim1 + cur_dim2 * s_dim2 + cur_dim0b * s_dim0b) * bank_row_size
                    cur_tensor = tensor[cur_dim2, cur_dim1, cur_dim0b].flatten()
                    buffer[cur_addr:cur_addr + bank_row_size:1] = np.frombuffer(cur_tensor, dtype=np.int8)
        # print('bbb', buffer, id(buffer))


def buffer2tensor(tensor, offset, strides, datatype, buffer):

    # DESCRIPTION
    # function: buffer to tensor
    # reverse process of tensor2buffer function

    # INPUT
    # tensor: a 3-D ndarray with expected [H, W, C] shape and dtype
    # offset: offset of address
    # strides: [stride_dim2, stride_dim1, stride_dim0b]
    # datatype: INT4/8/16/32/64, FP16/32
    # buffer: base address of target buffer

    # check tensor's dimension
    if tensor.ndim != 1 and tensor.ndim != 2 and tensor.ndim != 3:  # tensor
        raise Exception('Wrong tensor dim! should be 1 or 2 or 3, but ndim=' + str(tensor.ndim))

    # 检查地址是否符合要求
    bank_row_size = GlbSet.LMEM_WD // 8
    if offset % bank_row_size != 0:
        raise Exception('Wrong offset! offset = ' + str(offset) + ' while bank_row_size = ' + str(bank_row_size))


    # 进行补0操作，并检查strides是否符合要求
    size_dim0 = tensor.shape[2]
    (s_dim2, s_dim1, s_dim0b) = strides
    size_dim0a = GlbSet.LMEM_WD // Utils.datatype2width(datatype)
    size_dim0b = math.ceil(size_dim0 / size_dim0a)
    shape_orig = tensor.shape
    shape_orig_pad = [size_dim0b * size_dim0b, shape_orig[1], shape_orig[2]]
    if tensor.ndim == 3:
        size_dim2, size_dim1 = tensor.shape[0], tensor.shape[1]
        # dim0 维度进行填充，constant 默认填充 0
        tensor = np.pad(tensor, [(0, 0), (0, 0), (0, size_dim0a * size_dim0b - size_dim0)], mode='constant')
        if s_dim0b != 1:
            raise Exception('Stride of dim0b is NOT 1 ! s_dim0b = ' + str(s_dim0b))
        if s_dim1 < size_dim0b:
            raise Exception('Stride of dim1 is LESS than size of dim0b! s_dim1 = ' +
                            str(s_dim1) + ', size_dim0b = ' + str(size_dim0b))
        if s_dim2 < s_dim1 * size_dim1:
            raise Exception('Stride of dim2 is LESS than the product of size of dim0b and size of dim1! s_dim2 = ' +
                            str(s_dim2) + ', s_dim1 * size_dim1 = ' + str(s_dim1 * size_dim1))
        if s_dim2 % s_dim1 != 0:
            raise Exception('Stride of dim2 is not an integer multiple of stride of dim1, s_dim2 = ' +
                            str(s_dim2) + ', s_dim1 = ' + str(s_dim1))
    elif tensor.ndim == 2:
        size_dim2, size_dim1 = 1, tensor.shape[1]
        # dim0 维度进行填充，constant 默认填充 0
        tensor = np.pad(tensor, [(0, 0), (0, size_dim0a * size_dim0b - size_dim0)], mode='constant')
        tensor = tensor.reshape([size_dim2, size_dim1, size_dim0b*size_dim0a])
        if s_dim0b != 1:
            raise Exception('Stride of dim0b is NOT 1 ! s_dim0b = ' + str(s_dim0b))
        if s_dim1 < size_dim0b:
            raise Exception('Stride of dim1 is LESS than size of dim0b! s_dim1 = ' +
                            str(s_dim1) + ', size_dim0b = ' + str(size_dim0b))
    else:
        size_dim2, size_dim1 = 1, 1
        tensor = np.pad(tensor, [(0, size_dim0a * size_dim0b - size_dim0)], mode='constant')
        tensor = tensor.reshape([size_dim2, size_dim1], size_dim0b * size_dim0a)
        if s_dim0b != 1:
            raise Exception('Stride of dim0b is NOT 1 ! s_dim0b = ' + str(s_dim0b))

    # 最大支持的dim0a对应的地址数，即多少个 256 bits
    # 假设最大支持到 128M，则对应 128*1024*1024*8/256=4194304
    new_buf = np.zeros([4194304 * 32, ], dtype=np.int8)

    # 将数据从 buffer 中取出
    # step0: 检查数据类型是否与存储类型一致
    if datatype == 'INT4' and tensor.dtype != np.int8:
        # 对于 INT4 数据类型，要求用 np.int8 存储
        raise Exception('Datatype int4 not stored in np.int8!')
    elif datatype == 'INT8' and tensor.dtype != np.int8:
        raise Exception('Datatype int8 not stored in np.int8!')
    elif datatype == 'INT16' and tensor.dtype != np.int16:
        raise Exception('Datatype int16 not stored in np.int16!')
    elif datatype == 'INT32' and tensor.dtype != np.int32:
        raise Exception('Datatype int32 not stored in np.int32!')
    elif datatype == 'INT64' and tensor.dtype != np.int64:
        raise Exception('Datatype int64 not stored in np.int64!')
    elif datatype == 'FP16' and tensor.dtype != np.float16:
        raise Exception('Datatype fp16 not stored in np.float16!')
    elif datatype == 'FP32' and tensor.dtype != np.float32:
        raise Exception('Datatype fp32 not stored in np.float32!')
    # step1: 将数据从buffer中取出，放入new buffer中，这一步的目的是为了消除各种stride的影响，即new buffer中数据一定是连续的
    for cur_dim2 in range(size_dim2):
        for cur_dim1 in range(size_dim1):
            for cur_dim0b in range(size_dim0b):
                # reads tensor from buffer with strides
                cur_offset = offset + (cur_dim1 * s_dim1 + cur_dim2 * s_dim2 + cur_dim0b * s_dim0b) * bank_row_size
                tensor_want = np.frombuffer(buffer=buffer, dtype=np.int8, count=bank_row_size, offset=cur_offset)
                # put it into new buffer without strides
                cur_addr = (cur_dim0b + cur_dim1 * size_dim0b + cur_dim2 * size_dim0b * size_dim1) * bank_row_size
                new_buf[cur_addr:cur_addr + bank_row_size] = tensor_want
                # print(cur_dim2, cur_dim1, cur_dim0b)
                # print(tensor_want.shape)
                # print(new_buf[cur_addr:cur_addr + bank_row_size].shape)
    # print('Byte Result is Here!', new_buf)
    # step2: 将数据从 new buffer 中取出，恢复为 tensor
    if datatype == 'INT4':
        tensor_want = np.frombuffer(buffer=new_buf, dtype=Utils.datatype2dtype(datatype),
                                    count=tensor.size // 2, offset=0)
        tensor1 = np.right_shift(np.left_shift(tensor_want, 4), 4).reshape([-1, 1])
        tensor2 = np.right_shift(tensor_want, 4).reshape([-1, 1])
        tensor3 = np.concatenate((tensor1, tensor2), axis=1).reshape((size_dim2, size_dim1, -1))
    else:
        tensor_want = np.frombuffer(buffer=new_buf, dtype=Utils.datatype2dtype(datatype),
                                    count=tensor.size, offset=0)
        # print('Before reshape, result is here!', Utils.datatype2dtype(datatype), tensor_want)
        tensor3 = tensor_want.reshape((size_dim2, size_dim1, -1))
    # print('aa', tensor3, tensor3.shape, shape_orig)
    return tensor3[:, :, 0:size_dim0].reshape(shape_orig).copy()


def tensor_relu(tensor):
    tensor_relu_ans = 1 * (tensor > 0) * tensor
    tensor_relu_ans = tensor_relu_ans.astype(tensor.dtype)
    return tensor_relu_ans


def matrix_align(mat_np, manti_datatype='INT8', shift_mode='cut', return_mode='split_dec', debug=False):
    """
    :param
        matrix: input 2-D matrix, currently numpy array, should support pytorch tensor in the near future
        manti_datatype: mantissa output datatype, only support 'INT4', 'INT8' or 'INT16'
        shift_mode: cut or round
    :return
        return_mode = split: 1) max exponent vector (subtracted exp_offset),
                         and 2) column-wise exponent aligned mantissa_matrix
        return_mode = full: aligned matrix
    """

    # check if 2D
    if mat_np.ndim == 2:
        pass
    elif mat_np.ndim==3 and mat_np.shape[0] == 1:
        mat_np = mat_np.squeeze(axis=0)
    else:
        raise Exception('Matrix must be 2-D or 3-D with shape [1, :, :]')


    matrix = torch.from_numpy(mat_np)

    BF16_EXP = 8
    BF16_MANTI = 7
    FP16_EXP = 5
    FP16_MANTI = 10
    FP32_EXP = 8
    FP32_MANTI = 23
    SHIFT_MODE = shift_mode

    # step0: The input matrix format should be either FP16 or BF16.
    assert matrix.dtype == torch.float16 or matrix.dtype == torch.bfloat16 or matrix.dtype == torch.float32, \
        'matrix.dtype = ' + str(matrix.dtype)
    if matrix.dtype == torch.float16:
        exp = FP16_EXP
        manti = FP16_MANTI
    elif matrix.dtype == torch.bfloat16:
        exp = BF16_EXP
        manti = BF16_MANTI
    elif matrix.dtype == torch.float32:
        exp = FP32_EXP
        manti = FP32_MANTI
    else:
        raise Exception('Wrong input matrix format! input_matrix.dtype =', matrix.dtype)

    # step0: Transform the matrix into multiple blocks
    row, reduction = matrix.shape
    matrix = matrix.reshape(row, 1, reduction)
    # row, reduction = matrix.shape
    # if BLOCK_SIZE and reduction % BLOCK_SIZE != 0:
    #     device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    #     remainder = reduction % BLOCK_SIZE
    #     expand = BLOCK_SIZE - remainder
    #     expand_mat = torch.zeros([row, expand], dtype=matrix.dtype).to(device)
    #     matrix = torch.cat([matrix, expand_mat], dim=1)
    #
    # if BLOCK_SIZE:
    #     matrix = matrix.reshape(row, -1, BLOCK_SIZE)
    # else:
    #     matrix = matrix.reshape(row, 1, reduction)

    # step1: Extract the sign bit, exponent part, and mantissa part of the matrix.
    matrix_sign_mask = matrix < 0
    matrix_sign = torch.ones_like(matrix, dtype=torch.int16).masked_fill_(matrix_sign_mask, -1)
    matrix_abs = matrix.abs()
    if matrix.dtype == torch.float32:
        matrix_int_view = matrix_abs.view(torch.int32)
    else:
        matrix_int_view = matrix_abs.view(torch.int16)
    matrix_exponent = matrix_int_view // pow(2, manti) - (pow(2, exp - 1) - 1)
    if debug:
        print('matrix_exponent_size:', matrix_exponent.size())
        print('matrix_exponent:', matrix_exponent)
    matrix_mantissa = matrix_int_view % pow(2, manti)

    # step2: For each block, find the largest exponent.
    matrix_exponent_max, matrix_exponent_max_index = matrix_exponent.max(dim=0)
    if debug:
        print('matrix_exponent_max_size:', matrix_exponent_max.size())
        print('matrix_exponent_max:', matrix_exponent_max)

    # step3: Perform exponent alignment, which involves shifting the mantissa part.
    if debug:
        print('matrix_exponent_max.unsqueeze(dim=1).size():', matrix_exponent_max.unsqueeze(dim=1).size())
        print('matrix_exponent_max.unsqueeze(dim=1):', matrix_exponent_max.unsqueeze(dim=1))
        print('matrix_exponent_max.unsqueeze(dim=1).repeat(matrix_exponent.shape[0], 1, 1).size():',
              matrix_exponent_max.unsqueeze(dim=1).repeat(matrix_exponent.shape[0], 1, 1).size())
        print('matrix_exponent_max.unsqueeze(dim=1).repeat(matrix_exponent.shape[0], 1, 1):',
              matrix_exponent_max.unsqueeze(dim=1).repeat(matrix_exponent.shape[0], 1, 1))
    shift = matrix_exponent - matrix_exponent_max.unsqueeze(dim=1).repeat(matrix_exponent.shape[0], 1, 1)
    shift_number = torch.pow(2, shift.type(matrix.dtype))
    if debug:
        print('shift_number.size():', shift_number.size())
        print('shift_number:', shift_number)
    if SHIFT_MODE == 'cut':
        if matrix.dtype == torch.float32:
            matrix_mantissa_shift = ((pow(2, manti) + matrix_mantissa) * shift_number).type(torch.int32)
        else:
            matrix_mantissa_shift = ((pow(2, manti) + matrix_mantissa) * shift_number).type(torch.int16)
    elif SHIFT_MODE == 'round':
        if matrix.dtype == torch.float32:
            matrix_mantissa_shift = torch.round(((pow(2, manti) + matrix_mantissa) * shift_number)).type(torch.int32)
        else:
            matrix_mantissa_shift = torch.round(((pow(2, manti) + matrix_mantissa) * shift_number)).type(torch.int16)
    else:
        raise Exception('Wrong SHIFT_MODE! SHIFT_MODE =', SHIFT_MODE)
    restore = matrix_exponent_max.unsqueeze(dim=1).repeat(matrix_exponent.shape[0], 1, 1)
    restore_number = torch.pow(2, restore.type(matrix.dtype) - manti)
    matrix_aligned = matrix_mantissa_shift * restore_number * matrix_sign
    matrix_aligned = matrix_aligned.reshape(row, -1)[:, 0:reduction]
    if debug:
        print('matrix_aligned:', matrix_aligned)

    if return_mode == 'full':
        return matrix_aligned.squeeze().numpy()
    elif return_mode == 'split_dec':
        matrix_mantissa_shift_complement = matrix_mantissa_shift * matrix_sign
        manti_diff = (Utils.datatype2width(manti_datatype) - 1) - (manti + 1)
        dummy_zeros = torch.zeros_like(matrix_mantissa_shift_complement).type(matrix.dtype)
        mat_manti_shift_comp_out = matrix_mantissa_shift_complement * torch.pow(2, dummy_zeros + manti_diff)
        return matrix_exponent_max.squeeze().numpy().astype(dtype=np.int8), \
               mat_manti_shift_comp_out.squeeze().numpy().round().astype(dtype=Utils.datatype2dtype(manti_datatype))
    elif return_mode == 'split-bin':
        pass
    else:
        raise Exception('Wrong return_mode! return_mode =', return_mode)

    # # calculate relative discrepancy
    # discrepancy = matrix - matrix_aligned
    # discrepancy_ratio = discrepancy / matrix
    # # There is no difference between zero and aligned zero.
    # discrepancy_zero = discrepancy == 0
    # discrepancy_ratio = discrepancy_ratio.masked_fill_(discrepancy_zero, 0)
    # # If a tiny number becomes zero, it is also considered to be right.
    # discrepancy_small = matrix_abs < 1e-2
    # discrepancy_ratio = discrepancy_ratio.masked_fill_(discrepancy_small, 0)
    # # print('discrepancy_ratio \n', discrepancy_ratio)
    # max_discrepancy = discrepancy_ratio.abs().max()
    # max_discrepancy_arg = discrepancy_ratio.abs().argmax()
    # # If the discrepancy exceeds 10%, then issue a warning.
    # assert max_discrepancy <= 0.1, f'max discrepancy = {max_discrepancy}, arg = {max_discrepancy_arg}'


def restore_aligned_matrix(mat_exp_max, mat_mantissa, manti_datatype='INT8', restore_datatype='FP16'):
    # manti_datatype should be INT4 / INT8 / INT16
    # restore_datatype should be FP16 or FP32, will support BF16 in the future
    if restore_datatype in ['FP16' or 'FP32']:
        pass
    else:
        raise Exception('Wrong restore_datatype! restore_datatype =', restore_datatype)

    # pow(2, exp_max) × shifted_mantissa × pow(2, k)
    if manti_datatype == 'INT4':
        k = 8
    elif manti_datatype == 'INT8':
        k = 4
    elif manti_datatype == 'INT16':
        k = -4
    else:
        raise Exception('Wrong restore_datatype! restore_datatype =', restore_datatype)

    tensor_exp_max = torch.from_numpy(mat_exp_max)
    tensor_mat_manti = torch.from_numpy(mat_mantissa)
    restore_mat = torch.pow(2, tensor_exp_max.type(torch.float32)) * tensor_mat_manti * pow(2, k) / 1024

    return restore_mat.numpy().astype(Utils.datatype2dtype(restore_datatype))


def vvv_add(vector1, vector2):
    vector1 = vector1.astype(dtype=np.float32)
    vector2 = vector2.astype(dtype=np.float32)
    return vector1 + vector2


def vvv_sub(vector1, vector2):
    vector1 = vector1.astype(dtype=np.float32)
    vector2 = vector2.astype(dtype=np.float32)
    return vector1 - vector2


def vvv_rsub(vector1, vector2):
    vector1 = vector1.astype(dtype=np.float32)
    vector2 = vector2.astype(dtype=np.float32)
    return vector2 - vector1


def vvv_min(vector1, vector2):
    return np.where(vector1 < vector2, vector1, vector2)


def vvv_max(vector1, vector2):
    return np.where(vector1 > vector2, vector1, vector2)


def vvv_equal(vector1, vector2):
    return np.where(vector1 == vector2, 1, 0)


def vvv_not_equal(vector1, vector2):
    return np.where(vector1 != vector2, 1, 0)


def vvv_greater(vector1, vector2):
    return np.where(vector1 > vector2, 1, 0)


def vvv_greater_equal(vector1, vector2):
    return np.where(vector1 >= vector2, 1, 0)


def vvv_less(vector1, vector2):
    return np.where(vector1 < vector2, 1, 0)


def vvv_less_equal(vector1, vector2):
    return np.where(vector1 <= vector2, 1, 0)


def vvv_left_shift(vector1, vector2):
    return np.left_shift(vector1, vector2)


def vvv_right_shift(vector1, vector2):
    return np.right_shift(vector1, vector2)


def vvv_and(vector1, vector2):
    return np.bitwise_and(vector1, vector2)


def vvv_or(vector1, vector2):
    return np.bitwise_or(vector1, vector2)


def vvv_xor(vector1, vector2):
    return np.bitwise_xor(vector1, vector2)


def vvv_mul(vector1, vector2):
    vector1 = vector1.astype(dtype=np.float32)
    vector2 = vector2.astype(dtype=np.float32)
    return np.multiply(vector1, vector2)


def vs_sum(vector1):
    vector1 = vector1.astype(dtype=np.float32)
    return np.sum(vector1)


def vs_min(vector1):
    return np.min(vector1)


def vs_max(vector1):
    return np.max(vector1)


def vs_and(vector1):
    ans = vector1[0]
    for scalar in np.nditer(vector1[1:]):
        ans = ans & scalar
    return ans


def vs_or(vector1):
    ans = vector1[0]
    for scalar in np.nditer(vector1[1:]):
        ans = ans | scalar
    return ans


def vs_xor(vector1):
    ans = vector1[0]
    for scalar in np.nditer(vector1[1:]):
        ans = ans ^ scalar
    return ans


# # test case for tensor2buffer and buffer2tensor
# if __name__ == '__main__':
#
#     np.set_printoptions(threshold=100000, linewidth=1024)
#
#     tensor_test = torch.tensor([[[1, 2, 3], [4, 5, 6]], [[-1, -2, -3], [-4, -5, -6]]])
#     # shape: 各个维度的尺寸，等价于 size() 方法
#     # dtype: 数据类型，datatype
#     print(tensor_test.size(), tensor_test.shape, tensor_test.dtype)
#
#     random_numbers = torch.randint(0, 1841, (100,))
#     print(random_numbers)
#
#
#     buf = np.zeros([1024 * 48, ], dtype=np.int8)
#     # for int4 tb1
#     o11 = np.linspace(start=-8, stop=3, num=12, dtype=np.int8).reshape((1, 12, 1))
#     o21 = np.broadcast_to(o11, [5, 12, 64])
#     o12 = np.linspace(start=-6, stop=5, num=12, dtype=np.int8).reshape((1, 12, 1))
#     o22 = np.broadcast_to(o12, [5, 12, 64])
#     o31 = np.linspace(start=7, stop=-4, num=12, dtype=np.int8).reshape((1, 12, 1))
#     o41 = np.broadcast_to(o31, [5, 12, 64])
#     o32 = np.linspace(start=5, stop=-6, num=12, dtype=np.int8).reshape((1, 12, 1))
#     o42 = np.broadcast_to(o32, [5, 12, 64])
#     o5 = np.empty([5, 12, 256], dtype=np.int8)
#     o5[:, :, ::4] = o21
#     o5[:, :, 1::4] = o22
#     o5[:, :, 2::4] = o41
#     o5[:, :, 3::4] = o42
#     print(o5)
#     tensor2buffer(tensor=o5, offset=64, strides=[72, 6, 1], datatype='INT4', buffer=buf)
#     counter = 0
#     for i in np.nditer(buf):
#         print(counter, np.binary_repr(i, width=8), end='  ')
#         counter += 1
#
#         if counter % 8 == 0:
#             print()
#     tensor_int4_back = np.zeros_like(o5, dtype=np.int8)
#     tensor_int4_back = buffer2tensor(tensor_int4_back, offset=64, strides=[72, 6, 1], datatype='INT4', buffer=buf)
#     print('-----------------------------------------------------------------------------')
#     print(tensor_int4_back)


# test case for float16 → INT4 / INT8 / INT16
if __name__ == '__main__':
    np.set_printoptions(threshold=100000, linewidth=1024)
    tensor_test = np.random.uniform(-1000, 1000, (1, 2, 4)).astype(np.float16)

    print('---- to int8 ----')
    print('tensor_test:', tensor_test)
    tensor_test_bin = Utils.show_inBinary(tensor_test)
    print('tensor_test_bin:', tensor_test_bin)
    exp_max, shifted_manti = matrix_align(mat_np=tensor_test, debug=True)
    print('exp_max:', exp_max, exp_max.dtype)
    exp_max_bin = Utils.show_inBinary(exp_max)
    print('exp_max_bin:', exp_max_bin)
    shifted_manti_bin = Utils.show_inBinary(shifted_manti)
    print('shifted_manti:', shifted_manti)
    print('shifted_manti_bin:', shifted_manti_bin)
    restored_tensor = restore_aligned_matrix(mat_exp_max=exp_max, mat_mantissa=shifted_manti,
                                             manti_datatype='INT8', restore_datatype='FP16')
    print('restored_tensor:', restored_tensor)

    print('---- to int4 ----')
    print('tensor_test:', tensor_test)
    tensor_test_bin = Utils.show_inBinary(tensor_test)
    print('tensor_test_bin:', tensor_test_bin)
    exp_max, shifted_manti = matrix_align(mat_np=tensor_test, manti_datatype='INT4', debug=True)
    print('exp_max:', exp_max)
    exp_max_bin = Utils.show_inBinary(exp_max)
    print('exp_max_bin:', exp_max_bin)
    shifted_manti_bin = Utils.show_inBinary(shifted_manti)
    print('shifted_manti:', shifted_manti)
    print('shifted_manti_bin:', shifted_manti_bin)
    restored_tensor = restore_aligned_matrix(mat_exp_max=exp_max, mat_mantissa=shifted_manti,
                                             manti_datatype='INT4', restore_datatype='FP16')
    print('restored_tensor:', restored_tensor)

    print('---- to int16 ----')
    print('tensor_test:', tensor_test)
    tensor_test_bin = Utils.show_inBinary(tensor_test)
    print('tensor_test_bin:', tensor_test_bin)
    exp_max, shifted_manti = matrix_align(mat_np=tensor_test, manti_datatype='INT16', debug=True)
    print('exp_max:', exp_max)
    exp_max_bin = Utils.show_inBinary(exp_max)
    print('exp_max_bin:', exp_max_bin)
    shifted_manti_bin = Utils.show_inBinary(shifted_manti)
    print('shifted_manti:', shifted_manti)
    print('shifted_manti_bin:', shifted_manti_bin)
    restored_tensor = restore_aligned_matrix(mat_exp_max=exp_max, mat_mantissa=shifted_manti,
                                             manti_datatype='INT16', restore_datatype='FP16')
    print('restored_tensor:', restored_tensor)
