from enum import IntEnum, Enum
from typing import List
from dataclasses import dataclass, field
import json
from collections import namedtuple
import inspect

from reggroup import ConfigRegisterGroup, InstPreRegisterGroup
from npu_constants import *


# Simulator interface
# from tensorinfo import Primitive, convert_Primitive
from tensorinfo import convert_Primitive




class ConverterStatus:
    def __init__(self):
        self.group = 0
        self.mask  = 0 
        self.cfg_regs = ConfigRegisterGroup()
        self.pre_regs = InstPreRegisterGroup()



def mask_group_drv  (converter_status, rs1, rs2):
    converter_status.group = rs1 
    converter_status.mask  = rs2
    info_dict = {}
    return True, info_dict

def npu_cfg_drv     (converter_status, rs1, rs2):
    id1 = (rs1 >> 16) & 0xffff
    id2 = (rs1      ) & 0xffff
    converter_status.cfg_regs[id1, id2] = rs2
    return False, None

def wr_lmem_drv     (converter_status, rs1, rs2):
    info_dict = {}
    info_dict['wr_lmem.base_addr'] = rs1
    info_dict['wr_lmem.scalar_in'] = rs2
    return True, info_dict

def wr_lmem_drv_v   (converter_status, rs1, rs2):
    info_dict = {}
    info_dict['wr_lmem.base_addr'] = rs1
    info_dict['wr_lmem.scalar_in'] = rs2
    return True, info_dict

def rd_lmem_drv     (converter_status, rs1, rs2):
    info_dict = {}
    info_dict['rd_lmem.base_addr'] = rs1
    return True, info_dict 

def rd_lmem_drv_v   (converter_status, rs1, rs2):
    info_dict = {}
    info_dict['rd_lmem.base_addr'] = rs1
    return True, info_dict 

def switch_CIMC_drv (converter_status, rs1, rs2):
    info_dict = {}
    info_dict['sw_cimc.base_addr'] = rs1
    info_dict['sw_cimc.cimc_mode'] = rs2
    return True, info_dict

def sync_drv        (converter_status, rs1, rs2):
    info_dict = {}
    return True, info_dict

def noc_src_drv     (converter_status, rs1, rs2):
    info_dict = converter_status.cfg_regs.pack_dict('noc.')
    info_dict['noc.base_addr'] = rs1
    info_dict['noc.dest_idx']  = rs2
    return True, info_dict
    
def noc_dest_drv    (converter_status, rs1, rs2):
    info_dict = converter_status.cfg_regs.pack_dict('noc.')
    info_dict['noc.base_addr'] = rs1
    info_dict['noc.src_idx']   = rs2
    return True, info_dict

def noc_fence_drv   (converter_status, rs1, rs2):
    info_dict = {}
    return True, info_dict

def noc_src_drv_v01 (converter_status, rs1, rs2):
    info_dict = converter_status.cfg_regs.pack_dict('noc.')
    info_dict['noc.base_addr'] = rs1
    info_dict['noc.dest_idx']  = rs2
    return True, info_dict

def noc_src_drv_v10 (converter_status, rs1, rs2):
    info_dict = converter_status.cfg_regs.pack_dict('noc.')
    info_dict['noc.base_addr'] = rs1
    info_dict['noc.dest_idx']  = rs2
    return True, info_dict

def noc_src_drv_v11 (converter_status, rs1, rs2):
    info_dict = converter_status.cfg_regs.pack_dict('noc.')
    info_dict['noc.base_addr'] = rs1
    info_dict['noc.dest_idx']  = rs2
    return True, info_dict

def noc_dest_drv_v01(converter_status, rs1, rs2):
    info_dict = converter_status.cfg_regs.pack_dict('noc.')
    info_dict['noc.base_addr'] = rs1
    info_dict['noc.src_idx']   = rs2
    return True, info_dict

def noc_dest_drv_v10(converter_status, rs1, rs2):
    info_dict = converter_status.cfg_regs.pack_dict('noc.')
    info_dict['noc.base_addr'] = rs1
    info_dict['noc.src_idx']   = rs2
    return True, info_dict

def noc_dest_drv_v11(converter_status, rs1, rs2):
    info_dict = converter_status.cfg_regs.pack_dict('noc.')
    info_dict['noc.base_addr'] = rs1
    info_dict['noc.src_idx']   = rs2
    return True, info_dict

def tld_drv         (converter_status, rs1, rs2):
    info_dict = converter_status.cfg_regs.pack_dict('tld.')
    info_dict['tld.base_addr_gmem'] = rs1
    info_dict['tld.base_addr_lmem'] = rs2
    return True, info_dict    

def tld_trans_drv         (converter_status, rs1, rs2):
    info_dict = converter_status.cfg_regs.pack_dict('tld.')
    info_dict['tld.base_addr_gmem'] = rs1
    info_dict['tld.base_addr_lmem'] = rs2
    return True, info_dict    

def tld_index_pre   (converter_status, rs1, rs2):
    converter_status.pre_regs['tld_idx.base_addr_gmem'] = rs1
    converter_status.pre_regs['tld_idx.base_addr_lmem'] = rs2
    return False, None

def tld_index_drv   (converter_status, rs1, rs2):
    cfg_regs = converter_status.cfg_regs.pack_dict('tld_idx.')
    pre_regs = converter_status.pre_regs.pack_dict('tld_idx.')     
    info_dict = cfg_regs | pre_regs
    info_dict['tld_idx.base_addr_index'] = rs1
    return True, info_dict

def tst_drv         (converter_status, rs1, rs2):
    info_dict = converter_status.cfg_regs.pack_dict('tst.')
    info_dict['tst.base_addr_gmem'] = rs1
    info_dict['tst.base_addr_lmem'] = rs2
    return True, info_dict 

def tst_index_pre   (converter_status, rs1, rs2):
    converter_status.pre_regs['tst_idx.base_addr_gmem'] = rs1
    converter_status.pre_regs['tst_idx.base_addr_lmem'] = rs2
    return False, None

def tst_index_drv   (converter_status, rs1, rs2):
    cfg_regs = converter_status.cfg_regs.pack_dict('tst_idx.')
    pre_regs = converter_status.pre_regs.pack_dict('tst_idx.')     
    info_dict = cfg_regs | pre_regs
    info_dict['tst_idx.base_addr_index'] = rs1
    return True, info_dict 


def bc_drv          (converter_status, rs1, rs2):
    cfg_regs = converter_status.cfg_regs.pack_dict('tm.')
    info_dict = cfg_regs
    info_dict['bc.base_addr_out'] = rs1
    info_dict['bc.scalar_in']     = rs2
    return True, info_dict
    
def bc_drv_v        (converter_status, rs1, rs2):
    cfg_regs = converter_status.cfg_regs.pack_dict('tm.')
    info_dict = cfg_regs
    info_dict['bc.base_addr_out'] = rs1
    info_dict['bc.scalar_in']     = rs2
    return True, info_dict

def mv_drv          (converter_status, rs1, rs2):
    cfg_regs = converter_status.cfg_regs.pack_dict('tm.')
    info_dict = cfg_regs
    info_dict['mv.base_addr_in']  = rs1
    info_dict['mv.base_addr_out'] = rs2
    return True, info_dict

def tran_drv        (converter_status, rs1, rs2):
    cfg_regs = converter_status.cfg_regs.pack_dict('tm.')
    info_dict = cfg_regs
    info_dict['trans.base_addr_in']  = rs1
    info_dict['trans.base_addr_out'] = rs2
    return True, info_dict

def setCIMExp       (converter_status, rs1, rs2):
    raise NotImplementedError()
def getCIMExp       (converter_status, rs1, rs2):
    raise NotImplementedError()

def conv_pre        (converter_status, rs1, rs2):
    converter_status.pre_regs['conv.base_addr_wt'] = rs1  
    converter_status.pre_regs['conv.base_addr_in'] = rs2
    return False, None

def conv_drv        (converter_status, rs1, rs2):
    cfg_regs = converter_status.cfg_regs.pack_dict('tp.')
    pre_regs = converter_status.pre_regs.pack_dict('conv.') 
    info_dict = cfg_regs | pre_regs
    info_dict['conv.base_addr_out'] = rs1
    info_dict['conv.base_addr_ori'] = rs1
    return True, info_dict

def gemv_pre        (converter_status, rs1, rs2):
    converter_status.pre_regs['gemv.base_addr_wt'] = rs1  
    converter_status.pre_regs['gemv.base_addr_in'] = rs2
    return False, None
def gemv_drv        (converter_status, rs1, rs2):
    cfg_regs = converter_status.cfg_regs.pack_dict('tp.')
    pre_regs = converter_status.pre_regs.pack_dict('gemv.') 
    info_dict = cfg_regs | pre_regs
    info_dict['gemv.base_addr_out'] = rs1
    info_dict['gemv.base_addr_ori'] = rs1
    return True, info_dict

def gemm_pre        (converter_status, rs1, rs2):
    converter_status.pre_regs['gemm.base_addr_wt'] = rs1  
    converter_status.pre_regs['gemm.base_addr_in'] = rs2
    return False, None
def gemm_drv        (converter_status, rs1, rs2):
    cfg_regs = converter_status.cfg_regs.pack_dict('tp.')
    pre_regs = converter_status.pre_regs.pack_dict('gemm.') 
    info_dict = cfg_regs | pre_regs
    info_dict['gemm.base_addr_out'] = rs1
    info_dict['gemm.base_addr_ori'] = rs1
    return True, info_dict

def vvv_pre(converter_status, rs1, rs2):
    converter_status.pre_regs['vvv.base_addr_in1'] = rs1
    converter_status.pre_regs['vvv.base_addr_in2'] = rs2  
    return False, None

def vvv_drv(converter_status, rs1, rs2):
    cfg_regs = converter_status.cfg_regs.pack_dict('vp.')
    pre_regs = converter_status.pre_regs.pack_dict('vvv.') 
    info_dict = cfg_regs | pre_regs
    info_dict['vvv.base_addr_out'] = rs1
    return True, info_dict

def vsv_pre(converter_status, rs1, rs2):
    converter_status.pre_regs['vsv.scalar_in']      = rs2
    converter_status.pre_regs['vsv.base_addr_in1']  = rs1
    return False, None

def vsv_pre_v(converter_status, rs1, rs2):
    converter_status.pre_regs['vsv.scalar_in']      = rs2
    converter_status.pre_regs['vsv.base_addr_in1']  = rs1
    return False, None

def vsv_drv(converter_status, rs1, rs2):
    cfg_regs = converter_status.cfg_regs.pack_dict('vp.')
    pre_regs = converter_status.pre_regs.pack_dict('vsv.') 
    info_dict = cfg_regs | pre_regs
    info_dict['vsv.base_addr_out'] = rs1
    return True, info_dict

def vs_drv(converter_status, rs1, rs2):
    cfg_regs = converter_status.cfg_regs.pack_dict('vp.')
    info_dict = cfg_regs
    info_dict['vs.base_addr_in1'] = rs1
    return True, info_dict

def vs_drv_v(converter_status, rs1, rs2):
    cfg_regs = converter_status.cfg_regs.pack_dict('vp.')
    info_dict = cfg_regs
    info_dict['vs.base_addr_in1'] = rs1
    return True, info_dict

def vv_drv(converter_status, rs1, rs2):
    cfg_regs = converter_status.cfg_regs.pack_dict('vp.')   
    info_dict = cfg_regs
    info_dict['vv.base_addr_in1'] = rs1
    info_dict['vv.base_addr_out'] = rs2
    return True, info_dict



# instructions:
Instruction = namedtuple('Instruction', 
                ['base',    'mask',     'name',              'group',            'primitive',          'operation'])
inst_list = [
    Instruction(0x0000300B, 0xfe00707f, "mask_group_drv"    , InstGroup.CONTROL, "mask_group_drv"    , mask_group_drv      ),
    Instruction(0x0200300B, 0xfe00707f, "npu_cfg_drv"       , InstGroup.CONTROL, "npu_cfg_drv"       , npu_cfg_drv         ),
    Instruction(0x0400300B, 0xfe00707f, "wr_lmem_drv"       , InstGroup.CONTROL, "wr_lmem_drv"       , wr_lmem_drv         ),
    Instruction(0x4400302B, 0xfe00707f, "wr_lmem_drv.v"     , InstGroup.CONTROL, "wr_lmem_drv"       , wr_lmem_drv_v       ),
    Instruction(0x0600600B, 0xfe00707f, "rd_lmem_drv"       , InstGroup.CONTROL, "rd_lmem_drv"       , rd_lmem_drv         ),
    Instruction(0x0600602B, 0xfe00707f, "rd_lmem_drv.v"     , InstGroup.CONTROL, "rd_lmem_drv"       , rd_lmem_drv_v       ),
    Instruction(0x0800300B, 0xfe00707f, "switch_CIMC_drv"   , InstGroup.CONTROL, "switch_CIMC_drv"   , switch_CIMC_drv     ),
    Instruction(0x0A00400B, 0xfe00707f, "sync_drv"          , InstGroup.CONTROL, "sync_drv"          , sync_drv            ),
    Instruction(0x2000300B, 0xfe00707f, "noc_src_drv"       , InstGroup.NOC    , "noc_src_drv"       , noc_src_drv         ),
    Instruction(0x2200300B, 0xfe00707f, "noc_dest_drv"      , InstGroup.NOC    , "noc_dest_drv"      , noc_dest_drv        ),
    Instruction(0x2400400B, 0xfe00707f, "noc_fence_drv"     , InstGroup.NOC    , "noc_fence_drv"     , noc_fence_drv       ),
    Instruction(0x4C00302B, 0xfe00707f, "noc_src_drv.v01"   , InstGroup.NOC    , "noc_src_drv"       , noc_src_drv_v01     ),
    Instruction(0x8C00302B, 0xfe00707f, "noc_src_drv.v10"   , InstGroup.NOC    , "noc_src_drv"       , noc_src_drv_v10     ),
    Instruction(0xCC00302B, 0xfe00707f, "noc_src_drv.v11"   , InstGroup.NOC    , "noc_src_drv"       , noc_src_drv_v11     ),
    Instruction(0x4E00302B, 0xfe00707f, "noc_dest_drv.v01"  , InstGroup.NOC    , "noc_dest_drv"      , noc_dest_drv_v01    ),
    Instruction(0x8E00302B, 0xfe00707f, "noc_dest_drv.v10"  , InstGroup.NOC    , "noc_dest_drv"      , noc_dest_drv_v10    ),
    Instruction(0xCE00302B, 0xfe00707f, "noc_dest_drv.v11"  , InstGroup.NOC    , "noc_dest_drv"      , noc_dest_drv_v11    ),
    Instruction(0x4000300B, 0xfe00707f, "tld_drv"           , InstGroup.TLS    , "tld_drv"           , tld_drv             ),
    Instruction(0x4c00300b, 0xfe00707f, "tld_trans_drv"     , InstGroup.TLS    , "tld_trans_drv"     , tld_trans_drv       ),
    Instruction(0x4200300B, 0xfe00707f, "tld_index_pre"     , InstGroup.TLS    , "tld_index_pre"     , tld_index_pre       ),
    Instruction(0x4400200B, 0xfe00707f, "tld_index_drv"     , InstGroup.TLS    , "tld_index_drv"     , tld_index_drv       ),
    Instruction(0x4600300B, 0xfe00707f, "tst_drv"           , InstGroup.TLS    , "tst_drv"           , tst_drv             ),
    Instruction(0x4800300B, 0xfe00707f, "tst_index_pre"     , InstGroup.TLS    , "tst_index_pre"     , tst_index_pre       ),
    Instruction(0x4A00200B, 0xfe00707f, "tst_index_drv"     , InstGroup.TLS    , "tst_index_drv"     , tst_index_drv       ),
    Instruction(0x6000300B, 0xfe00707f, "bc_drv"            , InstGroup.TM     , "bc_drv"            , bc_drv              ),
    Instruction(0x5C00302B, 0xfe00707f, "bc_drv.v"          , InstGroup.TM     , "bc_drv"            , bc_drv_v            ),
    Instruction(0x6200300B, 0xfe00707f, "mv_drv"            , InstGroup.TM     , "mv_drv"            , mv_drv              ),
    Instruction(0x6400300B, 0xfe00707f, "tran_drv"          , InstGroup.TM     , "tran_drv"          , tran_drv            ),
    Instruction(0x6600300B, 0xfe00707f, "setCIMExp"         , InstGroup.TM     , "setCIMExp"         , setCIMExp           ),
    Instruction(0x6800300B, 0xfe00707f, "getCIMExp"         , InstGroup.TM     , "getCIMExp"         , getCIMExp           ),
    Instruction(0x8000300B, 0xfe00707f, "conv_pre"          , InstGroup.TP     , "conv_pre"          , conv_pre            ),
    Instruction(0x8200300B, 0xfe00707f, "conv_drv"          , InstGroup.TP     , "conv_drv"          , conv_drv            ),
    Instruction(0x8400300B, 0xfe00707f, "gemv_pre"          , InstGroup.TP     , "gemv_pre"          , gemv_pre            ),
    Instruction(0x8600300B, 0xfe00707f, "gemv_drv"          , InstGroup.TP     , "gemv_drv"          , gemv_drv            ),
    Instruction(0x8800300B, 0xfe00707f, "gemm_pre"          , InstGroup.TP     , "gemm_pre"          , gemm_pre            ),
    Instruction(0x8A00300B, 0xfe00707f, "gemm_drv"          , InstGroup.TP     , "gemm_drv"          , gemm_drv            ),
    Instruction(0xA000300B, 0xfe00707f, "vvv_pre"           , InstGroup.VP     , "vvv_pre"           , vvv_pre             ),
    Instruction(0xA200200B, 0xfe00707f, "vvv_drv"           , InstGroup.VP     , "vvv_drv"           , vvv_drv             ),
    Instruction(0xA400300B, 0xfe00707f, "vsv_pre"           , InstGroup.VP     , "vsv_pre"           , vsv_pre             ),
    Instruction(0xA600200B, 0xfe00707f, "vsv_drv"           , InstGroup.VP     , "vsv_drv"           , vsv_drv             ),
    Instruction(0x7000302B, 0xfe00707f, "vsv_pre.v"         , InstGroup.VP     , "vsv_pre"           , vsv_pre_v           ),
    Instruction(0xA800600B, 0xfe00707f, "vs_drv"            , InstGroup.VP     , "vs_drv"            , vs_drv              ),
    Instruction(0x7400602B, 0xfe00707f, "vs_drv.v"          , InstGroup.VP     , "vs_drv"            , vs_drv_v            ),
    Instruction(0xAA00300B, 0xfe00707f, "vv_drv"            , InstGroup.VP     , "vv_drv"            , vv_drv              )
]


def remove_prefix(info_dict):
    info = info_dict
    new_info = {}
    for key, value in info.items():
        new_key = key.split(".")[-1]
        new_info[new_key] = value
    return new_info

def check_vnice(inst, rs1, rs2):
    opcode = inst & 0x7f
    vx1 = (((inst >> 31) & 0x1) == 1) and (((inst >> 13) & 0x1) == 1)
    vx2 = (((inst >> 30) & 0x1) == 1) and (((inst >> 12) & 0x1) == 1)

    if opcode == 0x0b:
        assert isinstance(rs1, int) and isinstance(rs2, int)
    elif opcode == 0x2b:
        assert vx1 == (isinstance(rs1, list))
        assert vx2 == (isinstance(rs2, list))
    else:
        raise TypeError("Invalid Vnice Instruction")
    
    vs       = rs2 if vx2 else (rs1 if vx1 else None)

    return vs


class NiceInterface:
    def __init__(self):
        self.converter_status = ConverterStatus()

        self.inst_logfile = open("logs/ni_instruction.log", "w") 
        self.prim_logfile = open("logs/ni_primitive.log", "w")

    def decoder(self, id, inst, op1, op2, cycle=0):
        # vs =  check_vnice(inst, rs1, rs2)

        for inst_mod in inst_list:
            if (inst_mod.base & inst_mod.mask) == (inst & inst_mod.mask):
                print(f"[{self.__class__.__name__}::{inspect.currentframe().f_code.co_name}]", end=' ')
                print('handle ' + inst_mod.name + f"({inst:08x}, {op1}, {op2})")

                self.inst_logfile.write(f"[{cycle}] receive inst {inst:08x}, op1={op1:08x}, op2={op2}\n")
                self.inst_logfile.flush()

                is_drv, info_dict = inst_mod.operation(self.converter_status, op1, op2)
                if is_drv:
                    info_dict['group'] = self.converter_status.group
                    info_dict['mask']  = self.converter_status.mask

                    primitive_info = {
                        'primitive_name' : inst_mod.primitive,
                    }
                    primitive_info = primitive_info | info_dict
                    primitive_info = remove_prefix(primitive_info)

                    if "scalar_in" not in primitive_info:
                        # Handle VNICE instructions with vector operands
                        if isinstance(op2, list) and len(op2) == NPU_CONFIG.NPU_NUM:
                            # Convert byte arrays to integers for VNICE instructions
                            scalars = []
                            for i in range(NPU_CONFIG.NPU_NUM):
                                if isinstance(op2[i], bytes) and len(op2[i]) == 4:
                                    # Convert 4-byte array to 32-bit integer (big-endian)
                                    scalar_val = int.from_bytes(op2[i], 'big', signed=False)
                                    scalars.append(scalar_val)
                                elif isinstance(op2[i], int):
                                    scalars.append(op2[i])
                                else:
                                    scalars.append(-1)
                            primitive_info['scalars'] = scalars
                        else:
                            primitive_info['scalars'] = [-1] * NPU_CONFIG.NPU_NUM
                    elif isinstance(primitive_info["scalar_in"], list):
                        assert len(op2) == NPU_CONFIG.NPU_NUM
                        primitive_info['scalars'] = primitive_info['scalar_in']
                    else:
                        primitive_info['scalars'] = [primitive_info['scalar_in']] * NPU_CONFIG.NPU_NUM
                        

                    self.prim_logfile.write(f"[{cycle}] Create PrimitiveInfo {json.dumps(info_dict)}\n")
                    self.prim_logfile.flush()


                    sim_prim = convert_Primitive(primitive_info)
                    return True, sim_prim, primitive_info
                else: # not a drv instrution
                    return False, None, None
        else: # not found instruction
            raise ValueError(f"inst {hex(inst)} not found")


if __name__ == '__main__':
    ni = NiceInterface()
    
    ni.decoder(0, 0x0000300B, 0x1, 0x2)  # "mask_group_drv"    
    ni.decoder(0, 0x0200300B, 0x1, 0x2)  # "npu_cfg_drv"       
    ni.decoder(0, 0x0400300B, 0x1, 0x2)  # "wr_lmem_drv"       
    ni.decoder(0, 0x4400302B, 0x1, 0x2)  # "wr_lmem_drv.v"     
    ni.decoder(0, 0x0600600B, 0x1, 0x2)  # "rd_lmem_drv"       
    ni.decoder(0, 0x0600602B, 0x1, 0x2)  # "rd_lmem_drv.v"     
    ni.decoder(0, 0x0800300B, 0x1, 0x2)  # "switch_CIMC_drv"   
    ni.decoder(0, 0x0A00400B, 0x1, 0x2)  # "sync_drv"          
    ni.decoder(0, 0x2000300B, 0x1, 0x2)  # "noc_src_drv"       
    ni.decoder(0, 0x2200300B, 0x1, 0x2)  # "noc_dest_drv"      
    ni.decoder(0, 0x2400400B, 0x1, 0x2)  # "noc_fence_drv"     
    ni.decoder(0, 0x4C00302B, 0x1, 0x2)  # "noc_src_drv.v01"   
    ni.decoder(0, 0x8C00302B, 0x1, 0x2)  # "noc_src_drv.v10"   
    ni.decoder(0, 0xCC00302B, 0x1, 0x2)  # "noc_src_drv.v11"   
    ni.decoder(0, 0x4E00302B, 0x1, 0x2)  # "noc_dest_drv.v01"  
    ni.decoder(0, 0x8E00302B, 0x1, 0x2)  # "noc_dest_drv.v10"  
    ni.decoder(0, 0xCE00302B, 0x1, 0x2)  # "noc_dest_drv.v11"  
    ni.decoder(0, 0x4000300B, 0x1, 0x2)  # "tld_drv"           
    # ni.decoder(0, 0x4200300B, 0x1, 0x2)  # "tld_index_pre"     
    # ni.decoder(0, 0x4400200B, 0x1, 0x2)  # "tld_index_drv"     
    ni.decoder(0, 0x4600300B, 0x1, 0x2)  # "tst_drv"           
    # ni.handle(0, 0x4800300B, 0x1, 0x2)  # "tst_index_pre"     
    # ni.handle(0, 0x4A00200B, 0x1, 0x2)  # "tst_index_drv"     
    ni.decoder(0, 0x6000300B, 0x1, 0x2)  # "bc_drv"            
    ni.decoder(0, 0x5C00302B, 0x1, 0x2)  # "bc_drv.v"          
    ni.decoder(0, 0x6200300B, 0x1, 0x2)  # "mv_drv"            
    ni.decoder(0, 0x6400300B, 0x1, 0x2)  # "tran_drv"          
    # ni.decoder(0, 0x6600300B, 0x1, 0x2)  # "setCIMExp"         
    # ni.decoder(0, 0x6800300B, 0x1, 0x2)  # "getCIMExp"         
    # ni.decoder(0, 0x8000300B, 0x1, 0x2)  # "conv_pre"          
    # ni.decoder(0, 0x8200300B, 0x1, 0x2)  # "conv_drv"          
    ni.decoder(0, 0x8400300B, 0x1, 0x2)  # "gemv_pre"          
    ni.decoder(0, 0x8600300B, 0x1, 0x2)  # "gemv_drv"          
    ni.decoder(0, 0x8800300B, 0x1, 0x2)  # "gemm_pre"          
    ni.decoder(0, 0x8A00300B, 0x1, 0x2)  # "gemm_drv"          
    ni.decoder(0, 0xA000300B, 0x1, 0x2)  # "vvv_pre"           
    ni.decoder(0, 0xA200200B, 0x1, 0x2)  # "vvv_drv"           
    ni.decoder(0, 0xA400300B, 0x1, 0x2)  # "vsv_pre"           
    ni.decoder(0, 0xA600200B, 0x1, 0x2)  # "vsv_drv"           
    ni.decoder(0, 0x7000302B, 0x1, 0x2)  # "vsv_pre.v"         
    ni.decoder(0, 0xA800600B, 0x1, 0x2)  # "vs_drv"            
    ni.decoder(0, 0x3400602B, 0x1, 0x2)  # "vs_drv.v"          
    ni.decoder(0, 0xAA00300B, 0x1, 0x2)  # "vv_drv"            









