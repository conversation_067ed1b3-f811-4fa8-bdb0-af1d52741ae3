import os
import time
import datetime
import threading
import random
from collections import namedtuple
import queue
import inspect
import abc
from enum import IntEnum

from ipc_message import IpcResponse, IpcRequest
from ipc_socket_server import IpcSocketServer
from ipc_socket_client import <PERSON>pc<PERSON><PERSON><PERSON><PERSON><PERSON>

from decoder import NiceInterface
import json

import sys
from SIMTop import PNMDie


class ResponseStatus(IntEnum):
    StatusOK = 200

class RequestType(IntEnum):
    TYPE_NICE_READY = 1
    TYPE_NICE_INSTR = 2
    TYPE_CHECK      = 5
    TYPE_VNICE_INSTR= 6

TYPE_NULL=0xF1


INST_END_SIM = 0xfe00000b # indicate last request to simulator
INST_MASK    = 0xfe00707f




INST_REQ_TYPE = namedtuple("REQ_TYPE", ["req_time", "id", "inst", "op1", "op2"])
INST_RSP_TYPE = namedtuple("RSP_TYPE", ["rsp_time", "id", "type", "ret", "xd"]) # type "nice"/"vnice"


class PeakableQueue(queue.Queue):
    def peek(self):
        with self.mutex:
            return self.queue[0]

class BaseSimulator(abc.ABC):
    def __init__(self, name: str, req_queue, rsp_queue):
        self.name = name
        self.req_queue = req_queue
        self.rsp_queue = rsp_queue
        self.sim_time = 0

    @abc.abstractmethod
    def execute_until(self, end_time: int):
        pass

    @abc.abstractmethod
    def get_check_cycle(self):
        pass

    @abc.abstractmethod
    def end_simulation(self):
        pass
        


class DummyNPUSimulator(BaseSimulator):
    def __init__(self, name: str, req_queue, rsp_queue):
        super().__init__(name, req_queue, rsp_queue)

        self.executing_queue = PeakableQueue()

        self.instruction_logfile = open("npu.log", "w") 
        self.primitive_logfile = open("primitive.log", "w")

        self.nice_interface = NiceInterface()

    def step_cycle(self):
        self.sim_time += 1

    def execute_step(self):
        '''
        Execute a single step of the simulation.
        '''
        inst_req = None
        if (not self.req_queue.empty()) and (self.sim_time >= self.req_queue.peek().req_time):
            inst_req = self.req_queue.get()
        else:
            inst_req = None
        
        if inst_req is not None:
            self.instruction_logfile.write(f"[{self.sim_time}] receive inst {inst_req.inst:08x}\n")
            self.instruction_logfile.flush()
            
            is_drv, primitive, info_dict = self.nice_interface.decoder(inst_req.id, inst_req.inst, inst_req.op1, inst_req.op2)
            if is_drv:
                self.primitive_logfile.write(f"[{self.sim_time}] Create PrimitiveInfo {json.dumps(info_dict)}\n")
                self.primitive_logfile.flush()


            # add to executing queue
            if inst_req.inst & 0x7f == 0x0B:
                inst_rsp = INST_RSP_TYPE(
                    rsp_time=self.sim_time + 100, 
                    id=inst_req.id,
                    type="nice",
                    ret=0x01010101,  # random return value
                    xd=(inst_req.inst & 0x4000) > 0  # xd bit
                )
            elif inst_req.inst & 0x7f == 0x2B:
                inst_rsp = INST_RSP_TYPE(
                    rsp_time=self.sim_time + 200, 
                    id=inst_req.id,
                    type="vnice",
                    ret=[0x01010101, 0x02020202, 0x03030303, 0x04040404],  # random vector return value
                    xd=(inst_req.inst & 0x4000) > 0  # xd bit
                )
            else:
                raise ValueError(f"Unknown instruction type: 0x{inst_req.inst:08x}")

            self.executing_queue.put(inst_rsp)


        if (not self.executing_queue.empty()) and (self.sim_time >= self.executing_queue.peek().rsp_time):
            # simulate processing time
            inst_rsp = self.executing_queue.get()

            if inst_rsp.xd == True:
                self.rsp_queue.put(inst_rsp)

        self.step_cycle() # increment simulation time 
    

    def execute_until(self, end_time: int):
        '''
        Execute until the end_time.
        '''
        while self.sim_time < end_time:
            self.execute_step()
    
    def get_check_cycle(self):
        return self.sim_time + 50 # <TODO>  implement check_cycle
 
    def end_simulation(self):
        '''
        indicate no more instuctions will be sent
        detach from cpu, excute until input queue clear 
        '''
        self.execute_until(self.sim_time + 1000)  
        self.instruction_logfile.close()
        self.primitive_logfile.close()
        print(f"[{self.__class__.__name__}::{inspect.currentframe().f_code.co_name}] End simulation at cycle {self.sim_time}")



class Subsystem:
    def __init__(self, name: str):
        self.name = name

        self.req_queue = PeakableQueue(maxsize=128)
        self.rsp_queue = PeakableQueue(maxsize=128)        

        user_home = os.environ.get('HOME')
        sockets_directory = f"{user_home}/.mosim/sockets"
        os.makedirs(sockets_directory, exist_ok=True)
        self.server = IpcSocketServer(f"{sockets_directory}/npu.sock", self)
        self.server.start()

        # self.simulator = DummyNPUSimulator("npu_simulator", self.req_queue, self.rsp_queue)
        self.simulator = PNMDie(name="npu_simulator", 
                                req_queue=self.req_queue, 
                                rsp_queue=self.rsp_queue, 
                                nice_interface = NiceInterface(),
                                row=2, 
                                col=8, 
                                debug=True, 
                                gldres=True)
        self.simulator.data_import()

        self.pending_ret_count = 0
        self.running_state = True


    def handle(self, req: IpcRequest) -> IpcResponse:
        self.simulator.execute_until(req.current_cycle) # drive simulator to this point

        print(f"[{self.__class__.__name__}::{inspect.currentframe().f_code.co_name}]", end=' ')
        match req.type:
            case RequestType.TYPE_NICE_READY:
                print(f"Received handshake request @sim_time=", f"{req.current_cycle}")
                return self.handshake_handler(req)
            case RequestType.TYPE_NICE_INSTR:
                print(f"Received nice instruction request @sim_time=", f"{req.current_cycle}")
                return self.nice_handler(req)
            case RequestType.TYPE_VNICE_INSTR:
                print(f"Received vnice instruction request @sim_time=", f"{req.current_cycle}")
                return self.vnice_handler(req)
            case RequestType.TYPE_CHECK:
                print(f"Received check request @sim_time=", f"{req.current_cycle}")
                return self.check_handler(req)
            case _:
                raise ValueError(f"Unknown request type: {req.type}")
 

    def handshake_handler(self, req: IpcRequest) -> IpcResponse:
        ready = 1 if not self.req_queue.full() else 0

        rsp = IpcResponse()
        rsp.status = ResponseStatus.StatusOK
        rsp.length = 1
        rsp.data = ready.to_bytes(1, 'big')
        return rsp

    def nice_handler(self, req: IpcRequest) -> IpcResponse:
        req_inst = int.from_bytes(req.data[0:4],  'big', signed=False)  # instruction
        req_rs1  = int.from_bytes(req.data[4:8],  'big', signed=False)  # register rs1
        req_rs2  = int.from_bytes(req.data[8:12], 'big', signed=False)  # register rs2

        if req_inst & INST_MASK == INST_END_SIM:
            print(f"[{self.__class__.__name__}::{inspect.currentframe().f_code.co_name}] Received end simulation instruction @sim_time=", f"{req.current_cycle}")
            self.simulator.end_simulation()
            check_cycle = 0
        else:
            inst_req = INST_REQ_TYPE(
                req_time = req.current_cycle,
                id       = 0, # not used
                inst     = req_inst,
                op1      = req_rs1,
                op2      = req_rs2
            )

            self.req_queue.put(inst_req)

            if req_inst & 0x4000:
                check_cycle = self.simulator.get_check_cycle()
                self.pending_ret_count += 1
            else:
                check_cycle = 0   
        
        rsp = IpcResponse()
        rsp.status = ResponseStatus.StatusOK
        rsp.length = 9
        rsp.data = bytearray()
        rsp.data.extend(check_cycle.to_bytes(8, 'big'))
        rsp.data.extend(TYPE_NULL.to_bytes(1, "big"))      
        return rsp



    def vnice_handler(self, req: IpcRequest) -> IpcResponse:
        req_inst = int.from_bytes(req.data[0:4], 'big', signed=False)
        beat     = int.from_bytes(req.data[4:8], 'big', signed=False)
        lvmul    = int.from_bytes(req.data[8:12], 'big', signed=False)
        vsew     = int.from_bytes(req.data[12:16], 'big', signed=False)
        rounding_mode     = int.from_bytes(req.data[16:20], 'big', signed=False)
        fpu_rounding_mode = int.from_bytes(req.data[20:24], 'big', signed=False)
        rs1        = int.from_bytes(req.data[24:28], 'big', signed=False)
        active_len = int.from_bytes(req.data[28:32], 'big', signed=False)
        vs_len     = int.from_bytes(req.data[32:36], 'big', signed=False)
        element_active = req.data[36:36+active_len]
        vs1 = req.data[36+active_len:36+active_len+vs_len]
        vs2 = req.data[36+active_len+vs_len:36+active_len+vs_len*2]
        vd  = req.data[36+active_len+vs_len*2:36+active_len+vs_len*3]

        # Based on log analysis, vs2 data is actually at the end of the packet
        # Let's use the last 16 bytes as vs2 data
        if len(req.data) >= 88:
            vs2 = req.data[-16:]  # Last 16 bytes contain the actual vs2 data

        assert beat == 3
        # assert lvmul == 0
        # assert vsew == 2
        assert rounding_mode == 0
        assert fpu_rounding_mode == 0

        assert active_len == 4
        assert element_active == b'\x00\x00\x00\x00'



        rs1_val = rs1
        vs1_val = []
        vs2_val = []
        for i in range(4):
            vs1_val.append(vs1[i*4 : (i+1)*4])
            vs2_val.append(vs2[i*4 : (i+1)*4])

        op1_sel = (req_inst>>31) & 0x1 == 1

        inst_req = INST_REQ_TYPE(
            req_time = req.current_cycle,
            id       = 0, # not used
            inst     = req_inst,
            op1      = vs1_val if op1_sel else rs1_val,
            op2      = vs2_val
        )

        self.req_queue.put(inst_req)


        if req_inst & 0x4000:
            check_cycle = self.simulator.get_check_cycle()
            self.pending_ret_count += 1
        else:
            check_cycle = 0

        rsp = IpcResponse()
        rsp.status = ResponseStatus.StatusOK
        rsp.length = 9
        rsp.data = bytearray()
        rsp.data.extend(check_cycle.to_bytes(8, 'big'))
        rsp.data.extend(TYPE_NULL.to_bytes(1, 'big'))      
        return rsp


    def check_handler(self, req: IpcRequest) -> IpcResponse:
        rsp = IpcResponse()
        rsp.status = ResponseStatus.StatusOK
        if self.pending_ret_count == 0:
            check_cycle = 0
            rsp.length = 8
            rsp.data = check_cycle.to_bytes(8, 'big')
        else:
            if self.rsp_queue.empty():

                check_cycle = self.simulator.get_check_cycle()

                rsp.length = 9
                rsp.data = bytearray()
                rsp.data.extend(check_cycle.to_bytes(8,'big'))
                rsp.data.extend(TYPE_NULL.to_bytes(1, 'big'))

            else:
                inst_rsp = self.rsp_queue.get()
                self.pending_ret_count -= 1

                check_cycle = 0 # no next check
                if inst_rsp.type == "nice":
                    rsp.length = 13
                    rsp.data = bytearray()
                    rsp.data.extend(check_cycle.to_bytes(8, 'big'))
                    rsp.data.extend(RequestType.TYPE_NICE_INSTR.to_bytes(1,'big'))
                    rsp.data.extend(inst_rsp.ret.to_bytes(4, 'big'))                   
                    print(f"[{self.__class__.__name__}::{inspect.currentframe().f_code.co_name}]", end=' ')
                    print(f"check nice instruction response: id={inst_rsp.id}, type={inst_rsp.type}, ret=0x{inst_rsp.ret:08x}")

                elif inst_rsp.type == "vnice":
                    wbck_vxsat = 0
                    wbck_fflag = 0

                    rsp.length = 9 + 5 + 16
                    rsp.data = bytearray()
                    rsp.data.extend(check_cycle.to_bytes(8, 'big'))
                    rsp.data.extend(RequestType.TYPE_VNICE_INSTR.to_bytes(1,'big'))
                    rsp.data.extend(wbck_vxsat.to_bytes(1, 'big'))
                    rsp.data.extend(wbck_fflag.to_bytes(4, 'big'))
                    for i in range(4):
                        rsp.data.extend(inst_rsp.ret[i].to_bytes(4, 'little'))
                    print(f"[{self.__class__.__name__}::{inspect.currentframe().f_code.co_name}]", end=' ')
                    print(f"check vnice instruction response: id={inst_rsp.id}, type={inst_rsp.type}, ret={inst_rsp.ret}")
                    
                else:
                    raise ValueError("rsp type must be nice or vnice")
        return rsp

    def running(self):
        try:
            while True:
                print(f"at {datetime.datetime.now()} {self.name} running ...")
                time.sleep(30)
        finally:
            self.server.stop()



if __name__ == "__main__":
    npu = Subsystem("npu")
    npu.running()




