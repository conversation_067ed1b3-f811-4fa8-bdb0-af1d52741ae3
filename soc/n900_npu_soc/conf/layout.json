{"modules": {"bus": {"width": 1060, "height": 80, "x": 245, "y": -100.00000000000006}, "aon_clk": {"width": 90, "height": 80, "x": -35, "y": 49.99999999999993}, "reset": {"width": 90, "height": 100, "x": -35.000000000000036, "y": -229.99999999999994}, "ddr": {"width": 90, "height": 80, "x": 1095, "y": 210}, "timer_clk": {"width": 90, "height": 80, "x": -35, "y": -100.00000000000006}, "mrom": {"width": 90, "height": 80, "x": 344.9999999999999, "y": 210}, "gpio": {"width": 90, "height": 80, "x": 449.9999999999999, "y": 210}, "uart": {"width": 90, "height": 80, "x": 562.9999999999999, "y": 210}, "qspi0": {"width": 90, "height": 80, "x": 669.9999999999999, "y": 210}, "qspi1": {"width": 90, "height": 80, "x": 774.9999999999999, "y": 210}, "hole": {"width": 90, "height": 80, "x": 884.9999999999999, "y": 210}, "xip": {"width": 90, "height": 80, "x": 990.9999999999999, "y": 210}, "por_reset": {"width": 90, "height": 80, "x": -35, "y": -540}, "core_reset": {"width": 90, "height": 80, "x": -35, "y": -393}, "acc0": {"width": 90, "height": 80, "x": 234.9999999999999, "y": 210}, "acc1": {"width": 90, "height": 80, "x": 1205, "y": 210}, "nice_remote_adapter": {"width": 180, "height": 80, "x": 864.9999999999999, "y": -393}, "n900_nice": {"width": 180, "height": 80, "x": 500, "y": -370}}, "ports": {"bus.rst": {"group": "left"}, "bus.clk_0": {"group": "left"}, "bus.m_9": {"args": {"dx": -4.000030517578068}}, "reset.rst": {"group": "right"}}, "edges": {"reset.rst:ddr.rst": [{"x": 210, "y": -80}, {"x": 210, "y": -70}, {"x": 220, "y": 190}], "aon_clk.clk_out:por_reset.clk_0": [{"x": -80, "y": -570}, {"x": -80, "y": -580}], "reset.rst:nice_remote_adapter.rst": [{"x": 230, "y": -540}], "aon_clk.clk_out:nice_remote_adapter.clk_0": [{"x": 1021, "y": -540}]}, "numeration": {"n900_hybrid:cci_parameters:reset_vector": true, "n900_hybrid:cci_parameters:reset_vector_r": true, "n900_nice:cci_parameters:reset_vector": true}, "launchConfig": {"prelaunch": "\ncd ../../python\npython3 npu_demo.py", "launch": "rm -rf ./logs/*; $MOSIM_HOME/bin/mosim-top instance -d ./conf -g ./conf/log_config.toml -n n900_npu_soc", "postlaunchBackground": true, "postlaunch": "pid=$(ps aux| grep npu_demo|grep -v grep | awk '{print $2}') && kill -9 $pid"}, "setting": {"merge_clk_and_rst": false}}