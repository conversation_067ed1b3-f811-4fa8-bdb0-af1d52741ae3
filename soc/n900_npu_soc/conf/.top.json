{"name": "n900_npu_soc", "type": "top", "version": 1, "cci_parameters": {"global_quantum": 2, "global_quantum_tu": 2, "max_wave_file_size": 100, "monitor_enabled": true, "monitor_host": "0.0.0.0", "monitor_port": 7000, "sc_duration": 0, "sc_duration_tu": 2, "trace_config": "", "wave_en": false, "wave_file": "./logs/mosim_wave"}, "modules": [{"name": "bus", "angle": 0, "type": "mosim_bus", "lib": "libmosim.so", "arguments": {"slave_num": 2, "master_num": 11}, "cci_parameters": {"trace": "", "run_mode": 0, "rules": [{"master_id": 0, "master_name": "m_0", "slave_id": 0, "slave_name": "s_0", "addr": "0x00100000", "size": "0x00100000"}, {"master_id": 1, "master_name": "m_1", "slave_id": 0, "slave_name": "s_0", "addr": "0x00000000", "size": "0x00100000"}, {"master_id": 2, "master_name": "m_2", "slave_id": 0, "slave_name": "s_0", "addr": "0x10012000", "size": "0x00001000"}, {"master_id": 3, "master_name": "m_3", "slave_id": 0, "slave_name": "s_0", "addr": "0x10013000", "size": "0x00001000"}, {"master_id": 4, "master_name": "m_4", "slave_id": 0, "slave_name": "s_0", "addr": "0x10014000", "size": "0x00010000"}, {"master_id": 5, "master_name": "m_5", "slave_id": 0, "slave_name": "s_0", "addr": "0x10034000", "size": "0x0f000000"}, {"master_id": 6, "master_name": "m_6", "slave_id": 0, "slave_name": "s_0", "addr": "0x00110000", "size": "0x0fee0000"}, {"master_id": 7, "master_name": "m_7", "slave_id": 0, "slave_name": "s_0", "addr": "0x20000000", "size": "0x20000000"}, {"master_id": 8, "master_name": "m_8", "slave_id": 0, "slave_name": "s_0", "addr": "0x70180000", "size": "0x20000000"}, {"master_id": 9, "master_name": "m_9", "slave_id": 0, "slave_name": "s_0", "addr": "0x10023000", "size": "0x00001000"}, {"master_id": 10, "master_name": "m_10", "slave_id": 0, "slave_name": "s_0", "addr": "0x70000000", "size": "0x00180000"}, {"master_id": 9, "master_name": "m_9", "slave_id": 1, "slave_name": "s_1", "addr": "0x10023000", "size": "0x00001000"}]}, "ports": [{"name": "rst", "kind": "mos_in_reset", "direction": "input"}, {"name": "clk_0", "kind": "mos_in_clk", "direction": "input"}, {"name": "s_0", "kind": "mos_bus_slave_port", "direction": "input", "parameters": [{"name": "s_0.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "s_0.aw_delay", "desc": "aw path delay -> awvalid, unit is cycle", "type": "integer", "default_value": 0, "value": 1}, {"name": "s_0.aw_depth", "desc": "write address/aw channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_delay", "desc": "w path delay after awvalid -> wvalid, unit is cycle", "type": "integer", "default_value": 0, "value": 3}, {"name": "s_0.w_depth", "desc": "write data/w channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "s_0.b_delay", "type": "integer", "default_value": 0, "desc": "aw/w process delay -> bvalid, unit is cycle"}, {"name": "s_0.ar_delay", "desc": "ar path delay -> arvalid, unit is cycle", "type": "integer", "default_value": 0, "value": 5}, {"name": "s_0.ar_depth", "desc": "read address/ar channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.r_delay", "type": "integer", "default_value": 0, "desc": "ar/r process delay -> rvalid, unit is cycle"}, {"name": "s_0.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}]}, {"name": "m_0", "kind": "mos_bus_master_port", "direction": "output", "parameters": [{"name": "m_0.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "m_0.w_outstanding", "desc": "outstanding write transaction number", "type": "integer", "default_value": 8}, {"name": "m_0.w_burst", "desc": "max len of 1 write transaction: (w_burst+1)*w_width", "type": "integer", "default_value": 7}, {"name": "m_0.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_0.b_delay", "desc": "write b/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_0.r_outstanding", "desc": "outstanding read transaction number", "type": "integer", "default_value": 8}, {"name": "m_0.r_burst", "desc": "max len of 1 read transaction: (r_burst+1)*r_width", "type": "integer", "default_value": 7}, {"name": "m_0.r_delay", "desc": "read data/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_0.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_0.images", "type": "string", "default_value": "", "desc": "axf/elf/bin file path: elf_path, axf_path, bin_path@address"}]}, {"name": "m_1", "kind": "mos_bus_master_port", "direction": "output", "parameters": [{"name": "m_1.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "m_1.w_outstanding", "desc": "outstanding write transaction number", "type": "integer", "default_value": 8}, {"name": "m_1.w_burst", "desc": "max len of 1 write transaction: (w_burst+1)*w_width", "type": "integer", "default_value": 7}, {"name": "m_1.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_1.b_delay", "desc": "write b/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_1.r_outstanding", "desc": "outstanding read transaction number", "type": "integer", "default_value": 8}, {"name": "m_1.r_burst", "desc": "max len of 1 read transaction: (r_burst+1)*r_width", "type": "integer", "default_value": 7}, {"name": "m_1.r_delay", "desc": "read data/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_1.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_1.images", "type": "string", "default_value": "", "desc": "axf/elf/bin file path: elf_path, axf_path, bin_path@address"}]}, {"name": "m_2", "kind": "mos_bus_master_port", "direction": "output", "parameters": [{"name": "m_2.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "m_2.w_outstanding", "desc": "outstanding write transaction number", "type": "integer", "default_value": 8}, {"name": "m_2.w_burst", "desc": "max len of 1 write transaction: (w_burst+1)*w_width", "type": "integer", "default_value": 7}, {"name": "m_2.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_2.b_delay", "desc": "write b/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_2.r_outstanding", "desc": "outstanding read transaction number", "type": "integer", "default_value": 8}, {"name": "m_2.r_burst", "desc": "max len of 1 read transaction: (r_burst+1)*r_width", "type": "integer", "default_value": 7}, {"name": "m_2.r_delay", "desc": "read data/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_2.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_2.images", "type": "string", "default_value": "", "desc": "axf/elf/bin file path: elf_path, axf_path, bin_path@address"}]}, {"name": "m_3", "kind": "mos_bus_master_port", "direction": "output", "parameters": [{"name": "m_3.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "m_3.w_outstanding", "desc": "outstanding write transaction number", "type": "integer", "default_value": 8}, {"name": "m_3.w_burst", "desc": "max len of 1 write transaction: (w_burst+1)*w_width", "type": "integer", "default_value": 7}, {"name": "m_3.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_3.b_delay", "desc": "write b/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_3.r_outstanding", "desc": "outstanding read transaction number", "type": "integer", "default_value": 8}, {"name": "m_3.r_burst", "desc": "max len of 1 read transaction: (r_burst+1)*r_width", "type": "integer", "default_value": 7}, {"name": "m_3.r_delay", "desc": "read data/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_3.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_3.images", "type": "string", "default_value": "", "desc": "axf/elf/bin file path: elf_path, axf_path, bin_path@address"}]}, {"name": "m_4", "kind": "mos_bus_master_port", "direction": "output", "parameters": [{"name": "m_4.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "m_4.w_outstanding", "desc": "outstanding write transaction number", "type": "integer", "default_value": 8}, {"name": "m_4.w_burst", "desc": "max len of 1 write transaction: (w_burst+1)*w_width", "type": "integer", "default_value": 7}, {"name": "m_4.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_4.b_delay", "desc": "write b/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_4.r_outstanding", "desc": "outstanding read transaction number", "type": "integer", "default_value": 8}, {"name": "m_4.r_burst", "desc": "max len of 1 read transaction: (r_burst+1)*r_width", "type": "integer", "default_value": 7}, {"name": "m_4.r_delay", "desc": "read data/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_4.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_4.images", "type": "string", "default_value": "", "desc": "axf/elf/bin file path: elf_path, axf_path, bin_path@address"}]}, {"name": "m_5", "kind": "mos_bus_master_port", "direction": "output", "parameters": [{"name": "m_5.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "m_5.w_outstanding", "desc": "outstanding write transaction number", "type": "integer", "default_value": 8}, {"name": "m_5.w_burst", "desc": "max len of 1 write transaction: (w_burst+1)*w_width", "type": "integer", "default_value": 7}, {"name": "m_5.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_5.b_delay", "desc": "write b/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_5.r_outstanding", "desc": "outstanding read transaction number", "type": "integer", "default_value": 8}, {"name": "m_5.r_burst", "desc": "max len of 1 read transaction: (r_burst+1)*r_width", "type": "integer", "default_value": 7}, {"name": "m_5.r_delay", "desc": "read data/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_5.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_5.images", "type": "string", "default_value": "", "desc": "axf/elf/bin file path: elf_path, axf_path, bin_path@address"}]}, {"name": "m_6", "kind": "mos_bus_master_port", "direction": "output", "parameters": [{"name": "m_6.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "m_6.w_outstanding", "desc": "outstanding write transaction number", "type": "integer", "default_value": 8}, {"name": "m_6.w_burst", "desc": "max len of 1 write transaction: (w_burst+1)*w_width", "type": "integer", "default_value": 7}, {"name": "m_6.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_6.b_delay", "desc": "write b/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_6.r_outstanding", "desc": "outstanding read transaction number", "type": "integer", "default_value": 8}, {"name": "m_6.r_burst", "desc": "max len of 1 read transaction: (r_burst+1)*r_width", "type": "integer", "default_value": 7}, {"name": "m_6.r_delay", "desc": "read data/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_6.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_6.images", "type": "string", "default_value": "", "desc": "axf/elf/bin file path: elf_path, axf_path, bin_path@address"}]}, {"name": "m_7", "kind": "mos_bus_master_port", "direction": "output", "parameters": [{"name": "m_7.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "m_7.w_outstanding", "desc": "outstanding write transaction number", "type": "integer", "default_value": 8}, {"name": "m_7.w_burst", "desc": "max len of 1 write transaction: (w_burst+1)*w_width", "type": "integer", "default_value": 7}, {"name": "m_7.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_7.b_delay", "desc": "write b/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_7.r_outstanding", "desc": "outstanding read transaction number", "type": "integer", "default_value": 8}, {"name": "m_7.r_burst", "desc": "max len of 1 read transaction: (r_burst+1)*r_width", "type": "integer", "default_value": 7}, {"name": "m_7.r_delay", "desc": "read data/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_7.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_7.images", "type": "string", "default_value": "", "desc": "axf/elf/bin file path: elf_path, axf_path, bin_path@address"}]}, {"name": "m_8", "kind": "mos_bus_master_port", "direction": "output", "parameters": [{"name": "m_8.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "m_8.w_outstanding", "desc": "outstanding write transaction number", "type": "integer", "default_value": 8}, {"name": "m_8.w_burst", "desc": "max len of 1 write transaction: (w_burst+1)*w_width", "type": "integer", "default_value": 7}, {"name": "m_8.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_8.b_delay", "desc": "write b/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_8.r_outstanding", "desc": "outstanding read transaction number", "type": "integer", "default_value": 8}, {"name": "m_8.r_burst", "desc": "max len of 1 read transaction: (r_burst+1)*r_width", "type": "integer", "default_value": 7}, {"name": "m_8.r_delay", "desc": "read data/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_8.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_8.images", "type": "string", "default_value": "", "desc": "axf/elf/bin file path: elf_path, axf_path, bin_path@address"}]}, {"name": "m_9", "kind": "mos_bus_master_port", "direction": "output", "parameters": [{"name": "m_9.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "m_9.w_outstanding", "desc": "outstanding write transaction number", "type": "integer", "default_value": 8}, {"name": "m_9.w_burst", "desc": "max len of 1 write transaction: (w_burst+1)*w_width", "type": "integer", "default_value": 7}, {"name": "m_9.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_9.b_delay", "desc": "write b/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_9.r_outstanding", "desc": "outstanding read transaction number", "type": "integer", "default_value": 8}, {"name": "m_9.r_burst", "desc": "max len of 1 read transaction: (r_burst+1)*r_width", "type": "integer", "default_value": 7}, {"name": "m_9.r_delay", "desc": "read data/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_9.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_9.images", "type": "string", "default_value": "", "desc": "axf/elf/bin file path: elf_path, axf_path, bin_path@address"}]}, {"name": "m_10", "kind": "mos_bus_master_port", "direction": "output", "parameters": [{"name": "m_10.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "m_10.w_outstanding", "desc": "outstanding write transaction number", "type": "integer", "default_value": 8}, {"name": "m_10.w_burst", "desc": "max len of 1 write transaction: (w_burst+1)*w_width", "type": "integer", "default_value": 7}, {"name": "m_10.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_10.b_delay", "desc": "write b/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_10.r_outstanding", "desc": "outstanding read transaction number", "type": "integer", "default_value": 8}, {"name": "m_10.r_burst", "desc": "max len of 1 read transaction: (r_burst+1)*r_width", "type": "integer", "default_value": 7}, {"name": "m_10.r_delay", "desc": "read data/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_10.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_10.images", "type": "string", "default_value": "", "desc": "axf/elf/bin file path: elf_path, axf_path, bin_path@address"}]}, {"name": "s_1", "kind": "mos_bus_slave_port", "direction": "input", "parameters": [{"name": "s_1.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "s_1.aw_delay", "desc": "aw path delay -> awvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_1.aw_depth", "desc": "write address/aw channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_1.w_delay", "desc": "w path delay after awvalid -> wvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_1.w_depth", "desc": "write data/w channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_1.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "s_1.b_delay", "type": "integer", "default_value": 0, "desc": "aw/w process delay -> bvalid, unit is cycle"}, {"name": "s_1.ar_delay", "desc": "ar path delay -> arvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_1.ar_depth", "desc": "read address/ar channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_1.r_delay", "type": "integer", "default_value": 0, "desc": "ar/r process delay -> rvalid, unit is cycle"}, {"name": "s_1.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}]}]}, {"name": "aon_clk", "type": "mosim_clock", "lib": "libmosim.so", "arguments": {}, "cci_parameters": {"cx_duty_cycle": 0.5, "cx_period_tu": 2, "cx_period_v": 2, "cx_posedge_first": true, "cx_start_time_tu": 2, "cx_start_time_v": 0, "pv_duty_cycle": 0.5, "pv_period_tu": 3, "pv_period_v": 40, "pv_posedge_first": true, "pv_start_time_tu": 3, "pv_start_time_v": 40, "trace": "", "run_mode": 1}, "ports": [{"name": "clk_out", "kind": "mos_clock", "direction": "output"}]}, {"name": "reset", "type": "mosim_reset", "lib": "libmosim.so", "arguments": {}, "cci_parameters": {"active_high": false, "hold_cycle": 0, "trace": "", "trigger_enable": false}, "ports": [{"name": "rst", "kind": "mos_signal_rst", "direction": "output"}, {"name": "clk_0", "kind": "mos_in_clk", "direction": "input"}]}, {"name": "ddr", "type": "mosim_simple_mem", "lib": "libmosim_common_model.so", "arguments": {"bus_slave_num": 1}, "cci_parameters": {"trace": "", "dmi_en": true, "dmi_end_addr": 1073741824, "dmi_start_addr": 0, "mem_size": 1073741824, "store": true, "run_mode": 0}, "ports": [{"name": "rst", "kind": "mos_in_reset", "direction": "input"}, {"name": "s_0", "kind": "mos_bus_slave_port", "direction": "input", "parameters": [{"name": "s_0.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "s_0.aw_delay", "desc": "aw path delay -> awvalid, unit is cycle", "type": "integer", "default_value": 0, "value": 2}, {"name": "s_0.aw_depth", "desc": "write address/aw channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_delay", "desc": "w path delay after awvalid -> wvalid, unit is cycle", "type": "integer", "default_value": 0, "value": 0}, {"name": "s_0.w_depth", "desc": "write data/w channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "s_0.b_delay", "type": "integer", "default_value": 0, "desc": "aw/w process delay -> bvalid, unit is cycle"}, {"name": "s_0.ar_delay", "desc": "ar path delay -> arvalid, unit is cycle", "type": "integer", "default_value": 0, "value": 2}, {"name": "s_0.ar_depth", "desc": "read address/ar channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.r_delay", "type": "integer", "default_value": 0, "desc": "ar/r process delay -> rvalid, unit is cycle"}, {"name": "s_0.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}]}, {"name": "clk_0", "kind": "mos_in_clk", "direction": "input"}]}, {"name": "timer_clk", "type": "mosim_clock", "lib": "libmosim.so", "arguments": {}, "cci_parameters": {"cx_duty_cycle": 0.5, "cx_period_tu": 2, "cx_period_v": 20, "cx_posedge_first": true, "cx_start_time_tu": 2, "cx_start_time_v": 0, "pv_duty_cycle": 0.5, "pv_period_tu": 3, "pv_period_v": 40, "pv_posedge_first": true, "pv_start_time_tu": 3, "pv_start_time_v": 40, "trace": "", "run_mode": 1}, "ports": [{"name": "clk_out", "kind": "mos_clock", "direction": "output"}]}, {"name": "mrom", "type": "mosim_simple_mem", "lib": "libmosim_common_model.so", "arguments": {"bus_slave_num": 1}, "cci_parameters": {"trace": "", "dmi_en": false, "dmi_end_addr": 65536, "dmi_start_addr": 0, "mem_size": 131072, "store": true, "run_mode": 0}, "ports": [{"name": "rst", "kind": "mos_in_reset", "direction": "input"}, {"name": "s_0", "kind": "mos_bus_slave_port", "direction": "input", "parameters": [{"name": "s_0.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "s_0.aw_delay", "desc": "aw path delay -> awvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.aw_depth", "desc": "write address/aw channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_delay", "desc": "w path delay after awvalid -> wvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.w_depth", "desc": "write data/w channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "s_0.b_delay", "type": "integer", "default_value": 0, "desc": "aw/w process delay -> bvalid, unit is cycle"}, {"name": "s_0.ar_delay", "desc": "ar path delay -> arvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.ar_depth", "desc": "read address/ar channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.r_delay", "type": "integer", "default_value": 0, "desc": "ar/r process delay -> rvalid, unit is cycle"}, {"name": "s_0.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}]}, {"name": "clk_0", "kind": "mos_in_clk", "direction": "input"}]}, {"name": "gpio", "type": "mosim_simple_mem", "lib": "libmosim_common_model.so", "arguments": {"bus_slave_num": 1}, "cci_parameters": {"trace": "", "dmi_en": false, "dmi_end_addr": 0, "dmi_start_addr": 0, "mem_size": 4096, "store": true, "run_mode": 0}, "ports": [{"name": "rst", "kind": "mos_in_reset", "direction": "input"}, {"name": "s_0", "kind": "mos_bus_slave_port", "direction": "input", "parameters": [{"name": "s_0.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "s_0.aw_delay", "desc": "aw path delay -> awvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.aw_depth", "desc": "write address/aw channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_delay", "desc": "w path delay after awvalid -> wvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.w_depth", "desc": "write data/w channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "s_0.b_delay", "type": "integer", "default_value": 0, "desc": "aw/w process delay -> bvalid, unit is cycle"}, {"name": "s_0.ar_delay", "desc": "ar path delay -> arvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.ar_depth", "desc": "read address/ar channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.r_delay", "type": "integer", "default_value": 0, "desc": "ar/r process delay -> rvalid, unit is cycle"}, {"name": "s_0.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}]}, {"name": "clk_0", "kind": "mos_in_clk", "direction": "input"}]}, {"name": "uart", "type": "mosim_nuclei_uart", "lib": "libmosim_common_model.so", "arguments": {"external_terminal": 0}, "cci_parameters": {"show_time": true, "trace": "", "run_mode": 0}, "ports": [{"name": "rst", "kind": "mos_in_reset", "direction": "input"}, {"name": "clk_0", "kind": "mos_in_clk", "direction": "input"}, {"name": "s_0", "kind": "mos_bus_slave_port", "direction": "input", "parameters": [{"name": "s_0.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "s_0.aw_delay", "desc": "aw path delay -> awvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.aw_depth", "desc": "write address/aw channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_delay", "desc": "w path delay after awvalid -> wvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.w_depth", "desc": "write data/w channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "s_0.b_delay", "type": "integer", "default_value": 0, "desc": "aw/w process delay -> bvalid, unit is cycle"}, {"name": "s_0.ar_delay", "desc": "ar path delay -> arvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.ar_depth", "desc": "read address/ar channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.r_delay", "type": "integer", "default_value": 0, "desc": "ar/r process delay -> rvalid, unit is cycle"}, {"name": "s_0.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}]}, {"name": "out_1", "kind": "mos_gpio_out_port", "direction": "output", "stub": true}]}, {"name": "qspi0", "type": "mosim_simple_mem", "lib": "libmosim_common_model.so", "arguments": {"bus_slave_num": 1}, "cci_parameters": {"trace": "", "dmi_en": false, "dmi_end_addr": 0, "dmi_start_addr": 0, "mem_size": 4096, "store": true, "run_mode": 0}, "ports": [{"name": "rst", "kind": "mos_in_reset", "direction": "input"}, {"name": "s_0", "kind": "mos_bus_slave_port", "direction": "input", "parameters": [{"name": "s_0.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "s_0.aw_delay", "desc": "aw path delay -> awvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.aw_depth", "desc": "write address/aw channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_delay", "desc": "w path delay after awvalid -> wvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.w_depth", "desc": "write data/w channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "s_0.b_delay", "type": "integer", "default_value": 0, "desc": "aw/w process delay -> bvalid, unit is cycle"}, {"name": "s_0.ar_delay", "desc": "ar path delay -> arvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.ar_depth", "desc": "read address/ar channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.r_delay", "type": "integer", "default_value": 0, "desc": "ar/r process delay -> rvalid, unit is cycle"}, {"name": "s_0.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}]}, {"name": "clk_0", "kind": "mos_in_clk", "direction": "input"}]}, {"name": "qspi1", "type": "mosim_simple_mem", "lib": "libmosim_common_model.so", "arguments": {"bus_slave_num": 1}, "cci_parameters": {"trace": "", "dmi_en": false, "dmi_end_addr": 0, "dmi_start_addr": 0, "mem_size": 251658240, "store": true, "run_mode": 0}, "ports": [{"name": "rst", "kind": "mos_in_reset", "direction": "input"}, {"name": "s_0", "kind": "mos_bus_slave_port", "direction": "input", "parameters": [{"name": "s_0.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "s_0.aw_delay", "desc": "aw path delay -> awvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.aw_depth", "desc": "write address/aw channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_delay", "desc": "w path delay after awvalid -> wvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.w_depth", "desc": "write data/w channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "s_0.b_delay", "type": "integer", "default_value": 0, "desc": "aw/w process delay -> bvalid, unit is cycle"}, {"name": "s_0.ar_delay", "desc": "ar path delay -> arvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.ar_depth", "desc": "read address/ar channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.r_delay", "type": "integer", "default_value": 0, "desc": "ar/r process delay -> rvalid, unit is cycle"}, {"name": "s_0.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}]}, {"name": "clk_0", "kind": "mos_in_clk", "direction": "input"}]}, {"name": "hole", "type": "mosim_simple_mem", "lib": "libmosim_common_model.so", "arguments": {"bus_slave_num": 1}, "cci_parameters": {"trace": "", "dmi_en": false, "dmi_end_addr": 0, "dmi_start_addr": 0, "mem_size": 267255808, "store": true, "run_mode": 0}, "ports": [{"name": "rst", "kind": "mos_in_reset", "direction": "input"}, {"name": "s_0", "kind": "mos_bus_slave_port", "direction": "input", "parameters": [{"name": "s_0.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "s_0.aw_delay", "desc": "aw path delay -> awvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.aw_depth", "desc": "write address/aw channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_delay", "desc": "w path delay after awvalid -> wvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.w_depth", "desc": "write data/w channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "s_0.b_delay", "type": "integer", "default_value": 0, "desc": "aw/w process delay -> bvalid, unit is cycle"}, {"name": "s_0.ar_delay", "desc": "ar path delay -> arvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.ar_depth", "desc": "read address/ar channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.r_delay", "type": "integer", "default_value": 0, "desc": "ar/r process delay -> rvalid, unit is cycle"}, {"name": "s_0.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}]}, {"name": "clk_0", "kind": "mos_in_clk", "direction": "input"}]}, {"name": "xip", "type": "mosim_simple_mem", "lib": "libmosim_common_model.so", "arguments": {"bus_slave_num": 1}, "cci_parameters": {"trace": "", "dmi_en": false, "dmi_end_addr": 0, "dmi_start_addr": 0, "mem_size": 536870912, "store": true, "run_mode": 0}, "ports": [{"name": "rst", "kind": "mos_in_reset", "direction": "input"}, {"name": "s_0", "kind": "mos_bus_slave_port", "direction": "input", "parameters": [{"name": "s_0.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "s_0.aw_delay", "desc": "aw path delay -> awvalid, unit is cycle", "type": "integer", "default_value": 0, "value": 0}, {"name": "s_0.aw_depth", "desc": "write address/aw channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_delay", "desc": "w path delay after awvalid -> wvalid, unit is cycle", "type": "integer", "default_value": 0, "value": 0}, {"name": "s_0.w_depth", "desc": "write data/w channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "s_0.b_delay", "type": "integer", "default_value": 0, "desc": "aw/w process delay -> bvalid, unit is cycle"}, {"name": "s_0.ar_delay", "desc": "ar path delay -> arvalid, unit is cycle", "type": "integer", "default_value": 0, "value": 0}, {"name": "s_0.ar_depth", "desc": "read address/ar channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.r_delay", "type": "integer", "default_value": 0, "desc": "ar/r process delay -> rvalid, unit is cycle"}, {"name": "s_0.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}]}, {"name": "clk_0", "kind": "mos_in_clk", "direction": "input"}]}, {"name": "por_reset", "type": "mosim_reset", "lib": "libmosim.so", "arguments": {}, "cci_parameters": {"active_high": false, "hold_cycle": 20, "trace": "", "trigger_enable": false}, "ports": [{"name": "rst", "kind": "mos_signal_rst", "direction": "output"}, {"name": "clk_0", "kind": "mos_in_clk", "direction": "input"}]}, {"name": "core_reset", "type": "mosim_reset", "lib": "libmosim.so", "arguments": {}, "cci_parameters": {"active_high": false, "hold_cycle": 30, "trace": "", "trigger_enable": false}, "ports": [{"name": "rst", "kind": "mos_signal_rst", "direction": "output"}, {"name": "clk_0", "kind": "mos_in_clk", "direction": "input"}]}, {"name": "acc0", "type": "mosim_simple_mem", "lib": "libmosim_common_model.so", "arguments": {"bus_slave_num": 1}, "cci_parameters": {"trace": "", "dmi_en": false, "dmi_end_addr": 0, "dmi_start_addr": 0, "mem_size": 65536, "store": true, "run_mode": 0}, "ports": [{"name": "rst", "kind": "mos_in_reset", "direction": "input"}, {"name": "s_0", "kind": "mos_bus_slave_port", "direction": "input", "parameters": [{"name": "s_0.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "s_0.aw_delay", "desc": "aw path delay -> awvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.aw_depth", "desc": "write address/aw channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_delay", "desc": "w path delay after awvalid -> wvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.w_depth", "desc": "write data/w channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "s_0.b_delay", "type": "integer", "default_value": 0, "desc": "aw/w process delay -> bvalid, unit is cycle"}, {"name": "s_0.ar_delay", "desc": "ar path delay -> arvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.ar_depth", "desc": "read address/ar channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.r_delay", "type": "integer", "default_value": 0, "desc": "ar/r process delay -> rvalid, unit is cycle"}, {"name": "s_0.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}]}, {"name": "clk_0", "kind": "mos_in_clk", "direction": "input"}]}, {"name": "acc1", "type": "mosim_simple_mem", "lib": "libmosim_common_model.so", "arguments": {"bus_slave_num": 1}, "cci_parameters": {"trace": "", "dmi_en": false, "dmi_end_addr": 0, "dmi_start_addr": 0, "mem_size": 4096, "store": true, "run_mode": 0}, "ports": [{"name": "rst", "kind": "mos_in_reset", "direction": "input"}, {"name": "s_0", "kind": "mos_bus_slave_port", "direction": "input", "parameters": [{"name": "s_0.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "s_0.aw_delay", "desc": "aw path delay -> awvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.aw_depth", "desc": "write address/aw channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_delay", "desc": "w path delay after awvalid -> wvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.w_depth", "desc": "write data/w channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "s_0.b_delay", "type": "integer", "default_value": 0, "desc": "aw/w process delay -> bvalid, unit is cycle"}, {"name": "s_0.ar_delay", "desc": "ar path delay -> arvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "s_0.ar_depth", "desc": "read address/ar channel fifo depth", "type": "integer", "default_value": 16}, {"name": "s_0.r_delay", "type": "integer", "default_value": 0, "desc": "ar/r process delay -> rvalid, unit is cycle"}, {"name": "s_0.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}]}, {"name": "clk_0", "kind": "mos_in_clk", "direction": "input"}]}, {"name": "nice_remote_adapter", "type": "mosim_nice_remote_adapter", "lib": "libmosim_common_model.so", "arguments": {}, "cci_parameters": {"clk_period_tu": 2, "clk_period_v": 2, "trace": "", "run_mode": 0}, "ports": [{"name": "rst", "kind": "mos_in_reset", "direction": "input"}, {"name": "clk_0", "kind": "mos_in_clk", "direction": "input"}, {"name": "m_0", "kind": "mos_bus_master_port", "direction": "output", "parameters": [{"name": "m_0.run_mode", "type": "integer", "default_value": 0, "desc": "0-PV/function mode, 1-CX mode, 2-CA mode"}, {"name": "m_0.w_outstanding", "desc": "outstanding write transaction number", "type": "integer", "default_value": 8}, {"name": "m_0.w_burst", "desc": "max len of 1 write transaction: (w_burst+1)*w_width", "type": "integer", "default_value": 7}, {"name": "m_0.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_0.b_delay", "desc": "write b/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_0.r_outstanding", "desc": "outstanding read transaction number", "type": "integer", "default_value": 8}, {"name": "m_0.r_burst", "desc": "max len of 1 read transaction: (r_burst+1)*r_width", "type": "integer", "default_value": 7}, {"name": "m_0.r_delay", "desc": "read data/rsp process delay, unit is cycle", "type": "integer", "default_value": 0}, {"name": "m_0.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "m_0.images", "type": "string", "default_value": "", "desc": "axf/elf/bin file path: elf_path, axf_path, bin_path@address"}]}, {"name": "nice", "kind": "mos_bus_slave_port", "direction": "input", "parameters": [{"name": "nice.run_mode", "desc": "0-PV/function mode, 1-CX mode, 2-CA mode", "type": "integer", "default_value": 0}, {"name": "nice.aw_delay", "desc": "aw path delay -> awvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "nice.aw_depth", "desc": "write address/aw channel fifo depth", "type": "integer", "default_value": 16}, {"name": "nice.w_delay", "desc": "w path delay after awvalid -> wvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "nice.w_depth", "desc": "write data/w channel fifo depth", "type": "integer", "default_value": 16}, {"name": "nice.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "nice.b_delay", "desc": "aw/w process delay -> bvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "nice.ar_delay", "desc": "ar path delay -> arvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "nice.ar_depth", "desc": "read address/ar channel fifo depth", "type": "integer", "default_value": 16}, {"name": "nice.r_delay", "desc": "ar/r process delay -> rvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "nice.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}]}, {"name": "vnice", "kind": "mos_bus_slave_port", "direction": "input", "parameters": [{"name": "vnice.run_mode", "desc": "0-PV/function mode, 1-CX mode, 2-CA mode", "type": "integer", "default_value": 0}, {"name": "vnice.aw_delay", "desc": "aw path delay -> awvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "vnice.aw_depth", "desc": "write address/aw channel fifo depth", "type": "integer", "default_value": 16}, {"name": "vnice.w_delay", "desc": "w path delay after awvalid -> wvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "vnice.w_depth", "desc": "write data/w channel fifo depth", "type": "integer", "default_value": 16}, {"name": "vnice.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64}, {"name": "vnice.b_delay", "desc": "aw/w process delay -> bvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "vnice.ar_delay", "desc": "ar path delay -> arvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "vnice.ar_depth", "desc": "read address/ar channel fifo depth", "type": "integer", "default_value": 16}, {"name": "vnice.r_delay", "desc": "ar/r process delay -> rvalid, unit is cycle", "type": "integer", "default_value": 0}, {"name": "vnice.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64}], "stub": true}]}, {"name": "n900_nice", "type": "mosim_n900_nice_hybrid", "lib": "libn900_nice.so", "arguments": {}, "cci_parameters": {"gdb_port": 7008, "gdb_wait": false, "reset_vector": 1879048192, "run_mode": 0, "trace": "", "trace_pc": false}, "ports": [{"name": "rst", "kind": "mos_in_reset", "direction": "input"}, {"name": "aon_clk", "kind": "mos_in_clk", "direction": "input"}, {"name": "mtime_toggle_a_clk", "kind": "mos_in_clk", "direction": "input"}, {"name": "por_rst", "kind": "mos_in_reset", "direction": "input"}, {"name": "core_rst", "kind": "mos_in_reset", "direction": "input"}, {"name": "slv__64_axi_s", "inner_name": "cpu_bus.s_0", "kind": "mos_bus_slave_export", "direction": "input", "parameters": [{"name": "slv__64_axi_s.run_mode", "desc": "0-PV/function mode, 1-CX mode, 2-CA mode", "type": "integer", "default_value": 0, "inner": true}, {"name": "slv__64_axi_s.aw_delay", "desc": "aw path delay -> awvalid, unit is cycle", "type": "integer", "default_value": 0, "inner": true}, {"name": "slv__64_axi_s.aw_depth", "desc": "write address/aw channel fifo depth", "type": "integer", "default_value": 16, "inner": true}, {"name": "slv__64_axi_s.w_delay", "desc": "w path delay after awvalid -> wvalid, unit is cycle", "type": "integer", "default_value": 0, "inner": true}, {"name": "slv__64_axi_s.w_depth", "desc": "write data/w channel fifo depth", "type": "integer", "default_value": 16, "inner": true}, {"name": "slv__64_axi_s.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64, "inner": true}, {"name": "slv__64_axi_s.b_delay", "desc": "aw/w process delay -> bvalid, unit is cycle", "type": "integer", "default_value": 0, "inner": true}, {"name": "slv__64_axi_s.ar_delay", "desc": "ar path delay -> arvalid, unit is cycle", "type": "integer", "default_value": 0, "inner": true}, {"name": "slv__64_axi_s.ar_depth", "desc": "read address/ar channel fifo depth", "type": "integer", "default_value": 16, "inner": true}, {"name": "slv__64_axi_s.r_delay", "desc": "ar/r process delay -> rvalid, unit is cycle", "type": "integer", "default_value": 0, "inner": true}, {"name": "slv__64_axi_s.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64, "inner": true}], "stub": true}, {"name": "backdoor", "inner_name": "cpu_bus.s_1", "kind": "mos_bus_slave_export", "direction": "input", "parameters": [{"name": "backdoor.run_mode", "desc": "0-PV/function mode, 1-CX mode, 2-CA mode", "type": "integer", "default_value": 0, "inner": true}, {"name": "backdoor.aw_delay", "desc": "aw path delay -> awvalid, unit is cycle", "type": "integer", "default_value": 0, "inner": true}, {"name": "backdoor.aw_depth", "desc": "write address/aw channel fifo depth", "type": "integer", "default_value": 16, "inner": true}, {"name": "backdoor.w_delay", "desc": "w path delay after awvalid -> wvalid, unit is cycle", "type": "integer", "default_value": 0, "inner": true}, {"name": "backdoor.w_depth", "desc": "write data/w channel fifo depth", "type": "integer", "default_value": 16, "inner": true}, {"name": "backdoor.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64, "inner": true}, {"name": "backdoor.b_delay", "desc": "aw/w process delay -> bvalid, unit is cycle", "type": "integer", "default_value": 0, "inner": true}, {"name": "backdoor.ar_delay", "desc": "ar path delay -> arvalid, unit is cycle", "type": "integer", "default_value": 0, "inner": true}, {"name": "backdoor.ar_depth", "desc": "read address/ar channel fifo depth", "type": "integer", "default_value": 16, "inner": true}, {"name": "backdoor.r_delay", "desc": "ar/r process delay -> rvalid, unit is cycle", "type": "integer", "default_value": 0, "inner": true}, {"name": "backdoor.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64, "inner": true}]}, {"name": "nice_m__32", "inner_name": "cpu_bus.m_0", "kind": "mos_bus_master_export", "direction": "output", "parameters": [{"name": "nice_m__32.run_mode", "desc": "0-PV/function mode, 1-CX mode, 2-CA mode", "type": "integer", "default_value": 0, "inner": true}, {"name": "nice_m__32.w_outstanding", "desc": "outstanding write transaction number", "type": "integer", "default_value": 8, "inner": true}, {"name": "nice_m__32.w_burst", "desc": "max len of 1 write transaction: (w_burst+1)*w_width", "type": "integer", "default_value": 7, "inner": true}, {"name": "nice_m__32.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64, "inner": true}, {"name": "nice_m__32.b_delay", "desc": "write b/rsp process delay, unit is cycle", "type": "integer", "default_value": 0, "inner": true}, {"name": "nice_m__32.r_outstanding", "desc": "outstanding read transaction number", "type": "integer", "default_value": 8, "inner": true}, {"name": "nice_m__32.r_burst", "desc": "max len of 1 read transaction: (r_burst+1)*r_width", "type": "integer", "default_value": 7, "inner": true}, {"name": "nice_m__32.r_delay", "desc": "read data/rsp process delay, unit is cycle", "type": "integer", "default_value": 0, "inner": true}, {"name": "nice_m__32.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64, "inner": true}, {"name": "nice_m__32.images", "desc": "axf/elf/bin file path: elf_path, axf_path, bin_path@address", "type": "string", "default_value": "", "inner": true}]}, {"name": "mem__128_axi_m", "inner_name": "cpu_bus.m_1", "kind": "mos_bus_master_export", "direction": "output", "parameters": [{"name": "mem__128_axi_m.run_mode", "desc": "0-PV/function mode, 1-CX mode, 2-CA mode", "type": "integer", "default_value": 0, "inner": true}, {"name": "mem__128_axi_m.w_outstanding", "desc": "outstanding write transaction number", "type": "integer", "default_value": 8, "inner": true}, {"name": "mem__128_axi_m.w_burst", "desc": "max len of 1 write transaction: (w_burst+1)*w_width", "type": "integer", "default_value": 7, "inner": true}, {"name": "mem__128_axi_m.w_width", "desc": "write data width, unit is bit", "type": "integer", "default_value": 64, "inner": true}, {"name": "mem__128_axi_m.b_delay", "desc": "write b/rsp process delay, unit is cycle", "type": "integer", "default_value": 0, "inner": true}, {"name": "mem__128_axi_m.r_outstanding", "desc": "outstanding read transaction number", "type": "integer", "default_value": 8, "inner": true}, {"name": "mem__128_axi_m.r_burst", "desc": "max len of 1 read transaction: (r_burst+1)*r_width", "type": "integer", "default_value": 7, "inner": true}, {"name": "mem__128_axi_m.r_delay", "desc": "read data/rsp process delay, unit is cycle", "type": "integer", "default_value": 0, "inner": true}, {"name": "mem__128_axi_m.r_width", "desc": "read data width, unit is bit", "type": "integer", "default_value": 64, "inner": true}, {"name": "mem__128_axi_m.images", "desc": "axf/elf/bin file path: elf_path, axf_path, bin_path@address", "type": "string", "default_value": "", "inner": true, "value": "./fw/test_n900.elf"}]}, {"name": "irq_0", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_1", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_2", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_3", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_4", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_5", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_6", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_7", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_8", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_9", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_10", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_11", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_12", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_13", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_14", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_15", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_16", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_17", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_18", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_19", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_20", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_21", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_22", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_23", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_24", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_25", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_26", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_27", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_28", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_29", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_30", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_31", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_32", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_33", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_34", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_35", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_36", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_37", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_38", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_39", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_40", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_41", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_42", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_43", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_44", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_45", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_46", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_47", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_48", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_49", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_50", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_51", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_52", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_53", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_54", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_55", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_56", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_57", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_58", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_59", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_60", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_61", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_62", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_63", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_64", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_65", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_66", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_67", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_68", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_69", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_70", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_71", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_72", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_73", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_74", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_75", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_76", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_77", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_78", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_79", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_80", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_81", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_82", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_83", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_84", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_85", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_86", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_87", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_88", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_89", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_90", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_91", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_92", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_93", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_94", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_95", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_96", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_97", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_98", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_99", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_100", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_101", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_102", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_103", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_104", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_105", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_106", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_107", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_108", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_109", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_110", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_111", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_112", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_113", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_114", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_115", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_116", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_117", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_118", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_119", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_120", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_121", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_122", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_123", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_124", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_125", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_126", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_127", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_128", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_129", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_130", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_131", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_132", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_133", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_134", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_135", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_136", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_137", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_138", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_139", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_140", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_141", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_142", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_143", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_144", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_145", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_146", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_147", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_148", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_149", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_150", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_151", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_152", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_153", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_154", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_155", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_156", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_157", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_158", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_159", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_160", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_161", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_162", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_163", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_164", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_165", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_166", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_167", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_168", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_169", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_170", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_171", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_172", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_173", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_174", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_175", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_176", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_177", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_178", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_179", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_180", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_181", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_182", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_183", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_184", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_185", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_186", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_187", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_188", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_189", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_190", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_191", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_192", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_193", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_194", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_195", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_196", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_197", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_198", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_199", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_200", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_201", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_202", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_203", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_204", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_205", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_206", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_207", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_208", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_209", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_210", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_211", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_212", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_213", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_214", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_215", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_216", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_217", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_218", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_219", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_220", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_221", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_222", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_223", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_224", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_225", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_226", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_227", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_228", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_229", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_230", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_231", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_232", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_233", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_234", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_235", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_236", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_237", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_238", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_239", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_240", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_241", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_242", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_243", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_244", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_245", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_246", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_247", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_248", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_249", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_250", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_251", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_252", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_253", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_254", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}, {"name": "irq_255", "kind": "mos_gpio_in_export", "direction": "input", "stub": true}]}], "connections": [{"source": "reset.rst", "target": "bus.rst"}, {"source": "aon_clk.clk_out", "target": "bus.clk_0"}, {"source": "reset.rst", "target": "ddr.rst"}, {"source": "aon_clk.clk_out", "target": "uart.clk_0"}, {"source": "reset.rst", "target": "mrom.rst"}, {"source": "reset.rst", "target": "gpio.rst"}, {"source": "reset.rst", "target": "qspi0.rst"}, {"source": "reset.rst", "target": "qspi1.rst"}, {"source": "reset.rst", "target": "hole.rst"}, {"source": "reset.rst", "target": "xip.rst"}, {"source": "bus.m_1", "target": "mrom.s_0"}, {"source": "bus.m_4", "target": "qspi0.s_0"}, {"source": "bus.m_6", "target": "hole.s_0"}, {"source": "bus.m_7", "target": "xip.s_0"}, {"source": "bus.m_8", "target": "ddr.s_0"}, {"source": "bus.m_0", "target": "acc0.s_0"}, {"source": "reset.rst", "target": "acc0.rst"}, {"source": "bus.m_9", "target": "acc1.s_0"}, {"source": "aon_clk.clk_out", "target": "acc0.clk_0"}, {"source": "aon_clk.clk_out", "target": "acc1.clk_0"}, {"source": "aon_clk.clk_out", "target": "core_reset.clk_0"}, {"source": "aon_clk.clk_out", "target": "ddr.clk_0"}, {"source": "aon_clk.clk_out", "target": "gpio.clk_0"}, {"source": "aon_clk.clk_out", "target": "hole.clk_0"}, {"source": "aon_clk.clk_out", "target": "mrom.clk_0"}, {"source": "aon_clk.clk_out", "target": "por_reset.clk_0"}, {"source": "aon_clk.clk_out", "target": "qspi0.clk_0"}, {"source": "aon_clk.clk_out", "target": "qspi1.clk_0"}, {"source": "reset.rst", "target": "uart.rst"}, {"source": "aon_clk.clk_out", "target": "reset.clk_0"}, {"source": "bus.m_5", "target": "qspi1.s_0"}, {"source": "aon_clk.clk_out", "target": "xip.clk_0"}, {"source": "reset.rst", "target": "acc1.rst"}, {"source": "reset.rst", "target": "nice_remote_adapter.rst"}, {"source": "aon_clk.clk_out", "target": "nice_remote_adapter.clk_0"}, {"source": "nice_remote_adapter.m_0", "target": "bus.s_1"}, {"source": "bus.m_2", "target": "gpio.s_0"}, {"source": "bus.m_3", "target": "uart.s_0"}, {"source": "n900_nice.mem__128_axi_m", "target": "bus.s_0"}, {"source": "por_reset.rst", "target": "n900_nice.por_rst"}, {"source": "core_reset.rst", "target": "n900_nice.core_rst"}, {"source": "reset.rst", "target": "n900_nice.rst"}, {"source": "aon_clk.clk_out", "target": "n900_nice.aon_clk"}, {"source": "timer_clk.clk_out", "target": "n900_nice.mtime_toggle_a_clk"}, {"source": "bus.m_10", "target": "n900_nice.backdoor"}, {"source": "n900_nice.nice_m__32", "target": "nice_remote_adapter.nice"}]}