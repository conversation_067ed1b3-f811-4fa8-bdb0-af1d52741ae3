[07-01 14:41:14.685] [0 s] MOSIM_ASSERT mosim_cli_common.cpp:490 load_from_json_file: std::filesystem::exists(this->lib_file)
[07-01 14:41:14.685] [0 s] MOSIM_ERROR_CODE: 711 - 
[07-01 14:41:14.685] [0 s] lib file /home/<USER>/mosim_workspace/mosim_bin/mosim/lib/libn900_nice.so doesn't exist
[07-01 14:41:14.706] [0 s] MOSIM_ASSERT mosim_cli_common.cpp:490 load_from_json_file: std::filesystem::exists(this->lib_file)
[07-01 14:41:14.706] [0 s] MOSIM_ERROR_CODE: 711 - 
[07-01 14:41:14.706] [0 s] lib file /home/<USER>/mosim_workspace/mosim_bin/mosim/lib/libn900_nice.so doesn't exist
[07-01 14:41:31.955] [0 s] MOSIM_ASSERT mosim_cli_common.cpp:490 load_from_json_file: std::filesystem::exists(this->lib_file)
[07-01 14:41:31.955] [0 s] MOSIM_ERROR_CODE: 711 - 
[07-01 14:41:31.955] [0 s] lib file /home/<USER>/mosim_workspace/mosim_bin/mosim/lib/libn900_nice.so doesn't exist
[07-01 14:41:31.983] [0 s] MOSIM_ASSERT mosim_cli_common.cpp:490 load_from_json_file: std::filesystem::exists(this->lib_file)
[07-01 14:41:31.983] [0 s] MOSIM_ERROR_CODE: 711 - 
[07-01 14:41:31.983] [0 s] lib file /home/<USER>/mosim_workspace/mosim_bin/mosim/lib/libn900_nice.so doesn't exist
[07-01 14:42:45.588] [0 s] MOSIM_ASSERT mosim_cli_common.cpp:490 load_from_json_file: std::filesystem::exists(this->lib_file)
[07-01 14:42:45.588] [0 s] MOSIM_ERROR_CODE: 711 - 
[07-01 14:42:45.588] [0 s] lib file /home/<USER>/mosim_workspace/mosim_bin/mosim/lib/libn900_nice.so doesn't exist
[07-01 14:42:45.604] [0 s] MOSIM_ASSERT mosim_cli_common.cpp:490 load_from_json_file: std::filesystem::exists(this->lib_file)
[07-01 14:42:45.604] [0 s] MOSIM_ERROR_CODE: 711 - 
[07-01 14:42:45.604] [0 s] lib file /home/<USER>/mosim_workspace/mosim_bin/mosim/lib/libn900_nice.so doesn't exist
