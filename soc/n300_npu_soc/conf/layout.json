{"modules": {"bus": {"width": 1060, "height": 80, "x": 235, "y": -100.00000000000006}, "aon_clk": {"width": 90, "height": 80, "x": -35, "y": 49.99999999999993}, "reset": {"width": 90, "height": 80, "x": -35, "y": -230}, "ddr": {"width": 90, "height": 80, "x": 1095, "y": 210}, "timer_clk": {"width": 90, "height": 80, "x": -35, "y": -100.00000000000006}, "mrom": {"width": 90, "height": 80, "x": 344.9999999999999, "y": 210}, "gpio": {"width": 90, "height": 80, "x": 449.9999999999999, "y": 210}, "uart": {"width": 90, "height": 80, "x": 562.9999999999999, "y": 210}, "qspi0": {"width": 90, "height": 80, "x": 669.9999999999999, "y": 210}, "qspi1": {"width": 90, "height": 80, "x": 774.9999999999999, "y": 210}, "hole": {"width": 90, "height": 80, "x": 884.9999999999999, "y": 210}, "xip": {"width": 90, "height": 80, "x": 990.9999999999999, "y": 210}, "por_reset": {"width": 90, "height": 80, "x": 620, "y": -580}, "core_reset": {"width": 90, "height": 80, "x": 774.9999999999999, "y": -580}, "acc0": {"width": 90, "height": 80, "x": 234.9999999999999, "y": 210}, "acc1": {"width": 90, "height": 80, "x": 1205, "y": 210}, "nice_remote_adapter": {"width": 180, "height": 80, "x": 1160, "y": -434}, "mosim_n300_hybrid_6b62": {"width": 180, "height": 80, "x": 420, "y": -400}}, "ports": {"bus.m_9": {"args": {"dx": -4.000030517578068}}, "reset.rst": {"args": {"dy": -33.12495422363284}}}, "edges": {"reset.rst:ddr.rst": [{"x": 560, "y": -205}, {"x": 1118, "y": -90}], "aon_clk.clk_out:nice_remote_adapter.clk_0": [{"x": 1036, "y": -320}]}, "numeration": {"mosim_n300_hybrid_6b62:cci_parameters:reset_vector": true}, "launchConfig": {"prelaunch": "cd ../../python\npython3 npu_demo.py", "launch": "rm -rf ./logs/*; $MOSIM_HOME/bin/mosim-top instance -d ./conf -g ./conf/log_config.toml -n n300_npu_soc", "postlaunchBackground": true, "postlaunch": "pid=$(ps aux| grep npu_demo|grep -v grep | awk '{print $2}') && kill -9 $pid"}, "setting": {"merge_clk_and_rst": true}}