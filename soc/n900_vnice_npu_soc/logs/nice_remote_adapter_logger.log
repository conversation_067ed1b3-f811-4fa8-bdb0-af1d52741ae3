[08-01 09:56:30.357] [0 s] n900_vnice_npu_soc.nice_remote_adapter running....
[08-01 09:56:34.922] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:56:34.922] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:34.923] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30131
[08-01 09:56:34.923] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:34.923] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:34.923] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:34.923] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:56:34.933] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:56:34.933] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:34.934] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30184
[08-01 09:56:34.934] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:34.934] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:34.934] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:34.934] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:56:34.936] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:56:34.936] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:34.936] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30195
[08-01 09:56:34.937] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:34.937] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:34.937] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:34.937] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:56:34.939] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:56:34.939] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:34.939] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30206
[08-01 09:56:34.939] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:34.939] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:34.939] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:34.939] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:56:34.941] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[08-01 09:56:34.941] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:34.942] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30218
[08-01 09:56:34.942] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:34.942] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30218, check_time is: 60436000
[08-01 09:56:34.942] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:34.942] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[08-01 09:56:34.942] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 09:56:34.942] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[08-01 09:56:34.942] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60436000
[08-01 09:56:34.942] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60436000
[08-01 09:56:34.947] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:34.947] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30218, check_time is: 60436000
[08-01 09:56:34.947] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:34.947] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:56:34.947] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[08-01 09:56:34.947] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60436000
[08-01 09:56:34.947] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60436000
[08-01 09:56:34.948] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:34.948] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30219, check_time is: 60438000
[08-01 09:56:34.948] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:34.948] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:56:34.948] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[08-01 09:56:34.948] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60438000
[08-01 09:56:34.948] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60438000
[08-01 09:56:34.948] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:34.948] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30235, check_time is: 60470000
[08-01 09:56:34.948] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:34.948] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:56:34.948] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60438000
[08-01 09:56:34.951] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60470000
[08-01 09:56:34.951] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60470000
[08-01 09:56:34.952] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:34.952] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30235, check_time is: 60470000
[08-01 09:56:34.952] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:34.952] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:56:34.952] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60470000
[08-01 09:56:34.952] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60470000
[08-01 09:56:34.952] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60470000
[08-01 09:56:34.954] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[08-01 09:56:34.954] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:34.954] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[08-01 09:56:34.977] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:56:34.977] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:34.977] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30363
[08-01 09:56:34.977] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:34.977] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:34.977] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:34.977] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:56:34.986] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:56:34.986] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:34.986] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30416
[08-01 09:56:34.987] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:34.987] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:34.987] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:34.987] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:56:34.989] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:56:34.989] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:34.989] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30427
[08-01 09:56:34.989] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:34.989] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:34.989] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:34.989] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:56:34.991] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:56:34.991] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:34.992] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30438
[08-01 09:56:34.992] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:34.992] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:34.992] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:34.992] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:56:34.994] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[08-01 09:56:34.994] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:34.995] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30450
[08-01 09:56:34.995] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:34.995] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30450, check_time is: 60900000
[08-01 09:56:34.995] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:34.995] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[08-01 09:56:34.995] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 09:56:34.995] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[08-01 09:56:34.995] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60900000
[08-01 09:56:34.995] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60900000
[08-01 09:56:34.997] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:34.997] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30450, check_time is: 60900000
[08-01 09:56:34.997] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:34.997] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:56:34.997] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[08-01 09:56:34.997] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60900000
[08-01 09:56:34.997] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60900000
[08-01 09:56:34.998] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:34.998] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30451, check_time is: 60902000
[08-01 09:56:34.998] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:34.998] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:56:34.998] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[08-01 09:56:34.998] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60902000
[08-01 09:56:34.998] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60902000
[08-01 09:56:34.998] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:34.998] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30467, check_time is: 60934000
[08-01 09:56:34.998] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:34.998] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:56:34.998] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60902000
[08-01 09:56:35.001] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60934000
[08-01 09:56:35.001] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60934000
[08-01 09:56:35.001] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.001] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30467, check_time is: 60934000
[08-01 09:56:35.001] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.001] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:56:35.001] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60934000
[08-01 09:56:35.001] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60934000
[08-01 09:56:35.001] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60934000
[08-01 09:56:35.004] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[08-01 09:56:35.004] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:35.004] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[08-01 09:56:35.024] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:56:35.024] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:35.024] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30586
[08-01 09:56:35.024] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.024] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:35.024] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.024] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:56:35.033] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:56:35.033] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:35.033] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30639
[08-01 09:56:35.034] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.034] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:35.034] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.034] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:56:35.036] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:56:35.036] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:35.036] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30650
[08-01 09:56:35.036] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.036] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:35.036] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.036] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:56:35.038] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:56:35.038] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:35.039] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30661
[08-01 09:56:35.039] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.039] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:35.039] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.039] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:56:35.041] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[08-01 09:56:35.041] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:35.042] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30673
[08-01 09:56:35.042] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.042] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30673, check_time is: 61346000
[08-01 09:56:35.042] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.042] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[08-01 09:56:35.042] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 09:56:35.042] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[08-01 09:56:35.042] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61346000
[08-01 09:56:35.042] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61346000
[08-01 09:56:35.044] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.044] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30673, check_time is: 61346000
[08-01 09:56:35.044] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.044] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:56:35.044] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[08-01 09:56:35.044] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61346000
[08-01 09:56:35.044] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61346000
[08-01 09:56:35.044] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.044] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30674, check_time is: 61348000
[08-01 09:56:35.044] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.044] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:56:35.044] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[08-01 09:56:35.044] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61348000
[08-01 09:56:35.044] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61348000
[08-01 09:56:35.045] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.045] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30690, check_time is: 61380000
[08-01 09:56:35.045] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.045] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:56:35.045] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61348000
[08-01 09:56:35.047] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61380000
[08-01 09:56:35.047] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61380000
[08-01 09:56:35.048] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.048] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30690, check_time is: 61380000
[08-01 09:56:35.048] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.048] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:56:35.048] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61380000
[08-01 09:56:35.048] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61380000
[08-01 09:56:35.048] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61380000
[08-01 09:56:35.050] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[08-01 09:56:35.050] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:35.050] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[08-01 09:56:35.071] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:56:35.071] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:35.071] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30809
[08-01 09:56:35.071] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.071] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:35.071] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.071] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:56:35.080] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:56:35.080] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:35.081] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30862
[08-01 09:56:35.081] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.081] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:35.081] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.081] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:56:35.083] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:56:35.083] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:35.083] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30873
[08-01 09:56:35.084] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.084] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:35.084] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.084] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:56:35.086] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:56:35.086] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:35.086] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30884
[08-01 09:56:35.086] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.086] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:35.086] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.086] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:56:35.088] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[08-01 09:56:35.088] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:35.089] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30896
[08-01 09:56:35.089] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.089] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30896, check_time is: 61792000
[08-01 09:56:35.089] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.089] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[08-01 09:56:35.089] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 09:56:35.089] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[08-01 09:56:35.089] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61792000
[08-01 09:56:35.089] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61792000
[08-01 09:56:35.091] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.091] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30896, check_time is: 61792000
[08-01 09:56:35.091] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.091] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:56:35.091] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[08-01 09:56:35.091] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61792000
[08-01 09:56:35.091] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61792000
[08-01 09:56:35.091] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.091] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30897, check_time is: 61794000
[08-01 09:56:35.091] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.091] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:56:35.091] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[08-01 09:56:35.092] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61794000
[08-01 09:56:35.092] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61794000
[08-01 09:56:35.092] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.092] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30913, check_time is: 61826000
[08-01 09:56:35.092] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.092] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:56:35.092] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61794000
[08-01 09:56:35.094] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61826000
[08-01 09:56:35.094] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61826000
[08-01 09:56:35.095] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:35.095] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30913, check_time is: 61826000
[08-01 09:56:35.095] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:35.095] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:56:35.095] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61826000
[08-01 09:56:35.095] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61826000
[08-01 09:56:35.095] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61826000
[08-01 09:56:35.097] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[08-01 09:56:35.097] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:35.097] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[08-01 09:56:40.577] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:56:40.577] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:40.578] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa00478b to npu, cycles 65309
[08-01 09:56:40.578] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:40.578] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 65309, check_time is: 130618000
[08-01 09:56:40.578] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:40.578] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[08-01 09:56:40.578] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 09:56:40.578] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 130618000
[08-01 09:56:40.578] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 130618000
[08-01 09:56:40.578] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 130618000
[08-01 09:56:40.579] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:40.579] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 1410130717, check_time is: 2820261434000
[08-01 09:56:40.579] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:40.579] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:56:40.579] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 130618000
[08-01 09:56:40.581] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:56:40.581] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:56:40.582] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xfe00000b to npu, cycles 65321
[08-01 09:56:40.583] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:56:40.583] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:56:40.583] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:56:40.583] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:56:40.584] [130644 ns] n900_vnice_npu_soc.nice_remote_adapter ---check time updated to 0, over! at systemc_time ps: 130644000
