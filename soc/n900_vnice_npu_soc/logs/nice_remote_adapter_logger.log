[07-31 09:36:39.069] [0 s] n900_vnice_npu_soc.nice_remote_adapter running....
[07-31 09:36:42.456] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:36:42.456] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.457] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30131
[07-31 09:36:42.458] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.458] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.458] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.458] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:36:42.465] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:36:42.465] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.467] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30184
[07-31 09:36:42.467] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.467] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.467] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.467] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:36:42.469] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:36:42.469] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.469] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30195
[07-31 09:36:42.469] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.469] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.469] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.469] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:36:42.471] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:36:42.471] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.471] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30206
[07-31 09:36:42.472] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.472] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.472] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.472] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:36:42.474] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[07-31 09:36:42.474] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.474] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30218
[07-31 09:36:42.474] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.474] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30218, check_time is: 60436000
[07-31 09:36:42.474] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.474] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[07-31 09:36:42.474] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:36:42.474] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[07-31 09:36:42.474] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60436000
[07-31 09:36:42.474] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60436000
[07-31 09:36:42.477] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.477] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30218, check_time is: 60436000
[07-31 09:36:42.477] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.477] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:36:42.477] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[07-31 09:36:42.477] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60436000
[07-31 09:36:42.477] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60436000
[07-31 09:36:42.477] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.477] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30219, check_time is: 60438000
[07-31 09:36:42.477] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.477] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:36:42.477] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[07-31 09:36:42.477] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60438000
[07-31 09:36:42.477] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60438000
[07-31 09:36:42.477] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.477] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30235, check_time is: 60470000
[07-31 09:36:42.477] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.477] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:36:42.477] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60438000
[07-31 09:36:42.480] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60470000
[07-31 09:36:42.480] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60470000
[07-31 09:36:42.480] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.480] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30235, check_time is: 60470000
[07-31 09:36:42.480] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.480] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:36:42.480] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60470000
[07-31 09:36:42.480] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60470000
[07-31 09:36:42.480] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60470000
[07-31 09:36:42.483] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[07-31 09:36:42.483] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.483] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[07-31 09:36:42.503] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:36:42.503] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.504] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30363
[07-31 09:36:42.504] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.504] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.504] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.504] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:36:42.512] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:36:42.512] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.513] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30416
[07-31 09:36:42.513] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.513] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.513] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.513] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:36:42.515] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:36:42.515] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.515] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30427
[07-31 09:36:42.515] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.515] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.515] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.515] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:36:42.517] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:36:42.517] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.517] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30438
[07-31 09:36:42.518] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.518] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.518] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.518] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:36:42.520] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[07-31 09:36:42.520] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.520] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30450
[07-31 09:36:42.520] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.520] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30450, check_time is: 60900000
[07-31 09:36:42.520] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.520] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[07-31 09:36:42.520] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:36:42.520] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[07-31 09:36:42.520] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60900000
[07-31 09:36:42.520] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60900000
[07-31 09:36:42.522] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.522] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30450, check_time is: 60900000
[07-31 09:36:42.522] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.522] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:36:42.522] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[07-31 09:36:42.522] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60900000
[07-31 09:36:42.522] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60900000
[07-31 09:36:42.523] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.523] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30451, check_time is: 60902000
[07-31 09:36:42.523] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.523] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:36:42.523] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[07-31 09:36:42.523] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60902000
[07-31 09:36:42.523] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60902000
[07-31 09:36:42.523] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.523] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30467, check_time is: 60934000
[07-31 09:36:42.523] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.523] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:36:42.523] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60902000
[07-31 09:36:42.526] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60934000
[07-31 09:36:42.526] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60934000
[07-31 09:36:42.526] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.526] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30467, check_time is: 60934000
[07-31 09:36:42.526] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.526] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:36:42.526] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60934000
[07-31 09:36:42.526] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60934000
[07-31 09:36:42.526] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60934000
[07-31 09:36:42.528] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[07-31 09:36:42.528] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.528] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[07-31 09:36:42.546] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:36:42.546] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.547] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30586
[07-31 09:36:42.547] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.547] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.547] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.547] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:36:42.555] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:36:42.555] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.555] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30639
[07-31 09:36:42.556] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.556] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.556] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.556] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:36:42.557] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:36:42.557] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.558] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30650
[07-31 09:36:42.558] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.558] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.558] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.558] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:36:42.560] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:36:42.560] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.560] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30661
[07-31 09:36:42.560] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.560] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.560] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.560] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:36:42.562] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[07-31 09:36:42.562] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.562] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30673
[07-31 09:36:42.563] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.563] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30673, check_time is: 61346000
[07-31 09:36:42.563] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.563] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[07-31 09:36:42.563] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:36:42.563] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[07-31 09:36:42.563] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61346000
[07-31 09:36:42.563] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61346000
[07-31 09:36:42.565] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.565] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30673, check_time is: 61346000
[07-31 09:36:42.565] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.565] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:36:42.565] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[07-31 09:36:42.565] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61346000
[07-31 09:36:42.565] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61346000
[07-31 09:36:42.566] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.566] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30674, check_time is: 61348000
[07-31 09:36:42.566] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.566] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:36:42.566] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[07-31 09:36:42.566] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61348000
[07-31 09:36:42.566] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61348000
[07-31 09:36:42.566] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.566] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30690, check_time is: 61380000
[07-31 09:36:42.566] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.566] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:36:42.566] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61348000
[07-31 09:36:42.572] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61380000
[07-31 09:36:42.572] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61380000
[07-31 09:36:42.572] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.572] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30690, check_time is: 61380000
[07-31 09:36:42.572] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.572] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:36:42.572] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61380000
[07-31 09:36:42.572] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61380000
[07-31 09:36:42.572] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61380000
[07-31 09:36:42.574] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[07-31 09:36:42.574] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.574] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[07-31 09:36:42.593] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:36:42.593] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.593] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30809
[07-31 09:36:42.593] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.593] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.593] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.593] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:36:42.601] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:36:42.601] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.602] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30862
[07-31 09:36:42.603] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.603] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.603] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.603] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:36:42.604] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:36:42.604] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.605] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30873
[07-31 09:36:42.605] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.605] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.605] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.605] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:36:42.606] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:36:42.606] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.607] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30884
[07-31 09:36:42.607] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.607] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.607] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.607] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:36:42.609] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[07-31 09:36:42.609] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:42.609] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30896
[07-31 09:36:42.610] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.610] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30896, check_time is: 61792000
[07-31 09:36:42.610] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.610] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[07-31 09:36:42.610] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:36:42.610] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[07-31 09:36:42.610] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61792000
[07-31 09:36:42.610] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61792000
[07-31 09:36:42.612] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.612] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30896, check_time is: 61792000
[07-31 09:36:42.612] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.612] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:36:42.612] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[07-31 09:36:42.612] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61792000
[07-31 09:36:42.612] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61792000
[07-31 09:36:42.613] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.613] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30897, check_time is: 61794000
[07-31 09:36:42.613] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.613] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:36:42.613] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[07-31 09:36:42.613] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61794000
[07-31 09:36:42.613] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61794000
[07-31 09:36:42.613] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.613] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30913, check_time is: 61826000
[07-31 09:36:42.613] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.613] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:36:42.613] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61794000
[07-31 09:36:42.616] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61826000
[07-31 09:36:42.616] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61826000
[07-31 09:36:42.616] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:42.616] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30913, check_time is: 61826000
[07-31 09:36:42.616] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:42.616] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:36:42.616] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61826000
[07-31 09:36:42.616] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61826000
[07-31 09:36:42.616] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61826000
[07-31 09:36:42.618] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[07-31 09:36:42.618] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:42.618] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[07-31 09:36:47.563] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:36:47.563] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:47.563] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa00478b to npu, cycles 65309
[07-31 09:36:47.564] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:47.564] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 65309, check_time is: 130618000
[07-31 09:36:47.564] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:47.564] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:36:47.564] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:36:47.564] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 130618000
[07-31 09:36:47.564] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 130618000
[07-31 09:36:47.564] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 130618000
[07-31 09:36:47.564] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:47.564] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 1410130717, check_time is: 2820261434000
[07-31 09:36:47.564] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:47.564] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:36:47.564] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 130618000
[07-31 09:36:47.568] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:36:47.568] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:36:47.568] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xfe00000b to npu, cycles 65321
[07-31 09:36:47.569] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:36:47.569] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:36:47.569] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:36:47.569] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:36:47.569] [130644 ns] n900_vnice_npu_soc.nice_remote_adapter ---check time updated to 0, over! at systemc_time ps: 130644000
