[08-01 09:48:04.263] [0 s] n900_vnice_npu_soc.nice_remote_adapter running....
[08-01 09:48:07.933] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:48:07.933] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:07.934] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30131
[08-01 09:48:07.955] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:07.955] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:07.955] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:07.955] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:48:07.963] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:48:07.963] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:07.964] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30184
[08-01 09:48:07.965] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:07.965] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:07.965] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:07.965] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:48:07.966] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:48:07.966] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:07.967] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30195
[08-01 09:48:07.967] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:07.967] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:07.967] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:07.967] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:48:07.969] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:48:07.969] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:07.970] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30206
[08-01 09:48:07.970] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:07.970] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:07.970] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:07.970] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:48:07.972] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[08-01 09:48:07.972] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:07.972] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30218
[08-01 09:48:07.972] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:07.972] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30218, check_time is: 60436000
[08-01 09:48:07.972] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:07.972] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[08-01 09:48:07.972] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 09:48:07.972] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[08-01 09:48:07.972] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60436000
[08-01 09:48:07.972] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60436000
[08-01 09:48:07.975] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:07.975] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30218, check_time is: 60436000
[08-01 09:48:07.975] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:07.975] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:48:07.975] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[08-01 09:48:07.975] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60436000
[08-01 09:48:07.975] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60436000
[08-01 09:48:07.975] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:07.975] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30219, check_time is: 60438000
[08-01 09:48:07.975] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:07.975] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:48:07.975] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[08-01 09:48:07.975] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60438000
[08-01 09:48:07.975] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60438000
[08-01 09:48:07.975] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:07.975] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30235, check_time is: 60470000
[08-01 09:48:07.975] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:07.975] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:48:07.975] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60438000
[08-01 09:48:07.978] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60470000
[08-01 09:48:07.978] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60470000
[08-01 09:48:07.978] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:07.978] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30235, check_time is: 60470000
[08-01 09:48:07.978] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:07.978] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:48:07.978] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60470000
[08-01 09:48:07.978] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60470000
[08-01 09:48:07.978] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60470000
[08-01 09:48:07.981] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[08-01 09:48:07.981] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:07.981] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[08-01 09:48:08.001] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:48:08.001] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:08.001] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30363
[08-01 09:48:08.001] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.001] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:08.001] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.001] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:48:08.009] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:48:08.009] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:08.010] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30416
[08-01 09:48:08.010] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.010] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:08.010] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.010] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:48:08.012] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:48:08.012] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:08.013] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30427
[08-01 09:48:08.013] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.013] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:08.013] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.013] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:48:08.015] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:48:08.015] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:08.015] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30438
[08-01 09:48:08.015] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.015] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:08.015] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.015] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:48:08.017] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[08-01 09:48:08.017] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:08.018] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30450
[08-01 09:48:08.018] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.018] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30450, check_time is: 60900000
[08-01 09:48:08.018] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.018] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[08-01 09:48:08.018] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 09:48:08.018] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[08-01 09:48:08.018] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60900000
[08-01 09:48:08.018] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60900000
[08-01 09:48:08.020] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.020] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30450, check_time is: 60900000
[08-01 09:48:08.020] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.020] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:48:08.020] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[08-01 09:48:08.020] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60900000
[08-01 09:48:08.020] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60900000
[08-01 09:48:08.021] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.021] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30451, check_time is: 60902000
[08-01 09:48:08.021] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.021] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:48:08.021] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[08-01 09:48:08.021] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60902000
[08-01 09:48:08.021] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60902000
[08-01 09:48:08.021] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.021] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30467, check_time is: 60934000
[08-01 09:48:08.021] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.021] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:48:08.021] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60902000
[08-01 09:48:08.024] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60934000
[08-01 09:48:08.024] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60934000
[08-01 09:48:08.024] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.024] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30467, check_time is: 60934000
[08-01 09:48:08.024] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.024] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:48:08.024] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60934000
[08-01 09:48:08.024] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60934000
[08-01 09:48:08.024] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60934000
[08-01 09:48:08.026] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[08-01 09:48:08.026] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:08.026] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[08-01 09:48:08.045] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:48:08.045] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:08.045] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30586
[08-01 09:48:08.045] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.045] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:08.045] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.045] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:48:08.053] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:48:08.053] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:08.053] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30639
[08-01 09:48:08.054] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.054] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:08.054] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.054] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:48:08.055] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:48:08.055] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:08.056] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30650
[08-01 09:48:08.056] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.056] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:08.056] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.056] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:48:08.058] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:48:08.058] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:08.058] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30661
[08-01 09:48:08.059] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.059] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:08.059] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.059] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:48:08.061] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[08-01 09:48:08.061] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:08.061] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30673
[08-01 09:48:08.062] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.062] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30673, check_time is: 61346000
[08-01 09:48:08.062] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.062] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[08-01 09:48:08.062] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 09:48:08.062] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[08-01 09:48:08.062] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61346000
[08-01 09:48:08.062] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61346000
[08-01 09:48:08.063] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.063] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30673, check_time is: 61346000
[08-01 09:48:08.063] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.063] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:48:08.063] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[08-01 09:48:08.064] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61346000
[08-01 09:48:08.064] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61346000
[08-01 09:48:08.064] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.064] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30674, check_time is: 61348000
[08-01 09:48:08.064] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.064] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:48:08.064] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[08-01 09:48:08.064] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61348000
[08-01 09:48:08.064] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61348000
[08-01 09:48:08.064] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.064] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30690, check_time is: 61380000
[08-01 09:48:08.064] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.064] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:48:08.064] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61348000
[08-01 09:48:08.067] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61380000
[08-01 09:48:08.067] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61380000
[08-01 09:48:08.067] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.067] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30690, check_time is: 61380000
[08-01 09:48:08.067] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.067] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:48:08.067] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61380000
[08-01 09:48:08.067] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61380000
[08-01 09:48:08.067] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61380000
[08-01 09:48:08.069] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[08-01 09:48:08.069] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:08.069] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[08-01 09:48:08.088] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:48:08.088] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:08.088] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30809
[08-01 09:48:08.088] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.088] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:08.088] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.088] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:48:08.097] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:48:08.097] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:08.097] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30862
[08-01 09:48:08.097] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.097] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:08.097] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.097] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:48:08.099] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:48:08.099] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:08.100] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30873
[08-01 09:48:08.100] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.100] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:08.100] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.100] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:48:08.102] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:48:08.102] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:08.102] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30884
[08-01 09:48:08.103] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.103] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:08.103] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.103] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:48:08.105] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[08-01 09:48:08.105] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:08.105] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30896
[08-01 09:48:08.105] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.105] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30896, check_time is: 61792000
[08-01 09:48:08.105] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.105] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[08-01 09:48:08.105] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 09:48:08.105] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[08-01 09:48:08.105] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61792000
[08-01 09:48:08.105] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61792000
[08-01 09:48:08.107] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.107] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30896, check_time is: 61792000
[08-01 09:48:08.107] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.107] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:48:08.107] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[08-01 09:48:08.107] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61792000
[08-01 09:48:08.107] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61792000
[08-01 09:48:08.108] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.108] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30897, check_time is: 61794000
[08-01 09:48:08.108] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.108] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:48:08.108] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[08-01 09:48:08.108] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61794000
[08-01 09:48:08.108] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61794000
[08-01 09:48:08.108] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.108] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30913, check_time is: 61826000
[08-01 09:48:08.108] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.108] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:48:08.108] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61794000
[08-01 09:48:08.111] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61826000
[08-01 09:48:08.111] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61826000
[08-01 09:48:08.111] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:08.111] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30913, check_time is: 61826000
[08-01 09:48:08.111] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:08.111] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:48:08.111] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61826000
[08-01 09:48:08.111] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61826000
[08-01 09:48:08.111] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61826000
[08-01 09:48:08.114] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[08-01 09:48:08.114] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:08.114] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[08-01 09:48:13.436] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:48:13.436] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:13.436] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa00478b to npu, cycles 65309
[08-01 09:48:13.437] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:13.437] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 65309, check_time is: 130618000
[08-01 09:48:13.437] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:13.437] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[08-01 09:48:13.437] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 09:48:13.437] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 130618000
[08-01 09:48:13.437] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 130618000
[08-01 09:48:13.437] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 130618000
[08-01 09:48:13.438] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:13.438] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 1410130717, check_time is: 2820261434000
[08-01 09:48:13.438] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:13.438] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 09:48:13.438] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 130618000
[08-01 09:48:13.440] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 09:48:13.440] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 09:48:13.440] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xfe00000b to npu, cycles 65321
[08-01 09:48:13.442] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 09:48:13.442] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 09:48:13.442] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 09:48:13.442] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 09:48:13.443] [130644 ns] n900_vnice_npu_soc.nice_remote_adapter ---check time updated to 0, over! at systemc_time ps: 130644000
