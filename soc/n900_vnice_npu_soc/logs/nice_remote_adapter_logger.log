[07-29 14:34:20.526] [0 s] n900_vnice_npu_soc.nice_remote_adapter running....
[07-29 14:34:24.022] [60308 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.022] [60308 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-29 14:34:24.024] [60308 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30154
[07-29 14:34:24.024] [60308 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-29 14:34:24.024] [60308 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-29 14:34:24.024] [60308 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-29 14:34:24.024] [60308 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-29 14:34:24.025] [60330 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.025] [60330 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-29 14:34:24.027] [60330 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30165
[07-29 14:34:24.027] [60330 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-29 14:34:24.027] [60330 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-29 14:34:24.027] [60330 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-29 14:34:24.027] [60330 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-29 14:34:24.029] [60352 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.029] [60352 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-29 14:34:24.029] [60352 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30176
[07-29 14:34:24.029] [60352 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-29 14:34:24.029] [60352 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-29 14:34:24.029] [60352 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-29 14:34:24.029] [60352 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-29 14:34:24.031] [60374 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.031] [60374 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-29 14:34:24.031] [60374 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30187
[07-29 14:34:24.031] [60374 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-29 14:34:24.031] [60374 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-29 14:34:24.031] [60374 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-29 14:34:24.031] [60374 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-29 14:34:24.033] [60396 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.033] [60396 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-29 14:34:24.033] [60396 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30198
[07-29 14:34:24.033] [60396 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-29 14:34:24.033] [60396 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-29 14:34:24.033] [60396 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-29 14:34:24.033] [60396 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-29 14:34:24.046] [60610 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.046] [60610 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-29 14:34:24.046] [60610 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30305
[07-29 14:34:24.047] [60610 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-29 14:34:24.047] [60610 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-29 14:34:24.047] [60610 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-29 14:34:24.047] [60610 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-29 14:34:24.048] [60632 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.048] [60632 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-29 14:34:24.049] [60632 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30316
[07-29 14:34:24.049] [60632 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-29 14:34:24.049] [60632 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-29 14:34:24.049] [60632 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-29 14:34:24.049] [60632 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-29 14:34:24.050] [60654 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.050] [60654 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-29 14:34:24.051] [60654 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30327
[07-29 14:34:24.051] [60654 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-29 14:34:24.051] [60654 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-29 14:34:24.051] [60654 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-29 14:34:24.051] [60654 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-29 14:34:24.052] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.052] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-29 14:34:24.053] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30338
[07-29 14:34:24.053] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-29 14:34:24.053] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-29 14:34:24.053] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-29 14:34:24.053] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-29 14:34:24.054] [60698 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.054] [60698 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-29 14:34:24.055] [60698 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30349
[07-29 14:34:24.055] [60698 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-29 14:34:24.055] [60698 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-29 14:34:24.055] [60698 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-29 14:34:24.055] [60698 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-29 14:34:24.074] [61028 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.074] [61028 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-29 14:34:24.074] [61028 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30514
[07-29 14:34:24.075] [61028 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-29 14:34:24.075] [61028 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-29 14:34:24.075] [61028 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-29 14:34:24.075] [61028 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-29 14:34:24.076] [61054 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.076] [61054 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-29 14:34:24.077] [61054 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa4e7b00b to npu, cycles 30527
[07-29 14:34:24.077] [61054 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-29 14:34:24.077] [61054 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-29 14:34:24.077] [61054 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-29 14:34:24.077] [61054 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-29 14:34:24.089] [61268 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.089] [61268 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-29 14:34:24.090] [61268 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30634
[07-29 14:34:24.090] [61268 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-29 14:34:24.090] [61268 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-29 14:34:24.090] [61268 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-29 14:34:24.090] [61268 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-29 14:34:24.091] [61294 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.091] [61294 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-29 14:34:24.092] [61294 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa4e7b00b to npu, cycles 30647
[07-29 14:34:24.092] [61294 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-29 14:34:24.092] [61294 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-29 14:34:24.092] [61294 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-29 14:34:24.092] [61294 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-29 14:34:24.103] [61468 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.103] [61468 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-29 14:34:24.103] [61468 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30734
[07-29 14:34:24.103] [61468 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-29 14:34:24.103] [61468 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-29 14:34:24.103] [61468 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-29 14:34:24.103] [61468 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-29 14:34:24.105] [61490 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.105] [61490 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-29 14:34:24.105] [61490 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa607a00b to npu, cycles 30745
[07-29 14:34:24.106] [61490 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-29 14:34:24.106] [61490 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-29 14:34:24.106] [61490 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-29 14:34:24.106] [61490 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-29 14:34:24.118] [61704 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.118] [61704 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-29 14:34:24.121] [61704 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30852
[07-29 14:34:24.121] [61704 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-29 14:34:24.121] [61704 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-29 14:34:24.121] [61704 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-29 14:34:24.121] [61704 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-29 14:34:24.123] [61726 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.123] [61726 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-29 14:34:24.123] [61726 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa607a00b to npu, cycles 30863
[07-29 14:34:24.124] [61726 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-29 14:34:24.124] [61726 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-29 14:34:24.124] [61726 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-29 14:34:24.124] [61726 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-29 14:34:24.137] [61940 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-29 14:34:24.137] [61940 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
