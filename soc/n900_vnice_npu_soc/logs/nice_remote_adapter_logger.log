[08-01 10:05:38.112] [0 s] n900_vnice_npu_soc.nice_remote_adapter running....
[08-01 10:05:41.888] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:05:41.888] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:41.888] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30131
[08-01 10:05:41.889] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.889] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:41.889] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.889] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:05:41.896] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:05:41.896] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:41.897] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30184
[08-01 10:05:41.897] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.897] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:41.897] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.897] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:05:41.899] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:05:41.899] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:41.899] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30195
[08-01 10:05:41.900] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.900] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:41.900] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.900] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:05:41.901] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:05:41.901] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:41.902] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30206
[08-01 10:05:41.902] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.902] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:41.902] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.902] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:05:41.904] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[08-01 10:05:41.904] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:41.904] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30218
[08-01 10:05:41.904] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.904] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30218, check_time is: 60436000
[08-01 10:05:41.904] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.904] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[08-01 10:05:41.904] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 10:05:41.904] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[08-01 10:05:41.904] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60436000
[08-01 10:05:41.904] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60436000
[08-01 10:05:41.907] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.907] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30218, check_time is: 60436000
[08-01 10:05:41.907] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.907] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:05:41.907] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[08-01 10:05:41.907] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60436000
[08-01 10:05:41.907] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60436000
[08-01 10:05:41.907] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.907] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30219, check_time is: 60438000
[08-01 10:05:41.907] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.907] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:05:41.907] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[08-01 10:05:41.907] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60438000
[08-01 10:05:41.907] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60438000
[08-01 10:05:41.908] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.908] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30235, check_time is: 60470000
[08-01 10:05:41.908] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.908] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:05:41.908] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60438000
[08-01 10:05:41.910] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60470000
[08-01 10:05:41.910] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60470000
[08-01 10:05:41.910] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.910] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30235, check_time is: 60470000
[08-01 10:05:41.910] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.910] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:05:41.910] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60470000
[08-01 10:05:41.910] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60470000
[08-01 10:05:41.910] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60470000
[08-01 10:05:41.913] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[08-01 10:05:41.913] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:41.913] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[08-01 10:05:41.932] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:05:41.932] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:41.933] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30363
[08-01 10:05:41.933] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.933] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:41.933] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.933] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:05:41.940] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:05:41.940] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:41.941] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30416
[08-01 10:05:41.941] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.941] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:41.941] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.941] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:05:41.943] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:05:41.943] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:41.944] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30427
[08-01 10:05:41.944] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.944] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:41.944] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.944] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:05:41.945] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:05:41.945] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:41.946] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30438
[08-01 10:05:41.946] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.946] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:41.946] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.946] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:05:41.948] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[08-01 10:05:41.948] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:41.948] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30450
[08-01 10:05:41.949] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.949] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30450, check_time is: 60900000
[08-01 10:05:41.949] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.949] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[08-01 10:05:41.949] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 10:05:41.949] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[08-01 10:05:41.949] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60900000
[08-01 10:05:41.949] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60900000
[08-01 10:05:41.951] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.951] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30450, check_time is: 60900000
[08-01 10:05:41.951] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.951] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:05:41.951] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[08-01 10:05:41.951] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60900000
[08-01 10:05:41.951] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60900000
[08-01 10:05:41.951] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.951] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30451, check_time is: 60902000
[08-01 10:05:41.951] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.951] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:05:41.951] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[08-01 10:05:41.951] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60902000
[08-01 10:05:41.951] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60902000
[08-01 10:05:41.952] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.952] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30467, check_time is: 60934000
[08-01 10:05:41.952] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.952] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:05:41.952] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60902000
[08-01 10:05:41.954] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60934000
[08-01 10:05:41.954] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60934000
[08-01 10:05:41.954] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.954] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30467, check_time is: 60934000
[08-01 10:05:41.954] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.954] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:05:41.954] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60934000
[08-01 10:05:41.954] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60934000
[08-01 10:05:41.954] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60934000
[08-01 10:05:41.956] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[08-01 10:05:41.956] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:41.956] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[08-01 10:05:41.974] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:05:41.974] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:41.974] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30586
[08-01 10:05:41.974] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.974] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:41.974] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.974] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:05:41.981] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:05:41.981] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:41.982] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30639
[08-01 10:05:41.982] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.982] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:41.982] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.982] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:05:41.984] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:05:41.984] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:41.984] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30650
[08-01 10:05:41.984] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.984] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:41.984] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.984] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:05:41.986] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:05:41.986] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:41.986] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30661
[08-01 10:05:41.986] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.986] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:41.986] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.986] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:05:41.988] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[08-01 10:05:41.988] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:41.988] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30673
[08-01 10:05:41.989] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.989] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30673, check_time is: 61346000
[08-01 10:05:41.989] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.989] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[08-01 10:05:41.989] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 10:05:41.989] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[08-01 10:05:41.989] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61346000
[08-01 10:05:41.989] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61346000
[08-01 10:05:41.992] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.992] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30673, check_time is: 61346000
[08-01 10:05:41.992] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.992] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:05:41.992] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[08-01 10:05:41.992] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61346000
[08-01 10:05:41.992] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61346000
[08-01 10:05:41.992] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.992] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30674, check_time is: 61348000
[08-01 10:05:41.992] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.992] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:05:41.992] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[08-01 10:05:41.992] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61348000
[08-01 10:05:41.993] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61348000
[08-01 10:05:41.993] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.993] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30690, check_time is: 61380000
[08-01 10:05:41.993] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.993] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:05:41.993] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61348000
[08-01 10:05:41.995] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61380000
[08-01 10:05:41.995] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61380000
[08-01 10:05:41.995] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:41.995] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30690, check_time is: 61380000
[08-01 10:05:41.995] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:41.995] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:05:41.995] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61380000
[08-01 10:05:41.995] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61380000
[08-01 10:05:41.995] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61380000
[08-01 10:05:41.997] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[08-01 10:05:41.997] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:41.997] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[08-01 10:05:42.015] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:05:42.015] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:42.015] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30809
[08-01 10:05:42.015] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:42.015] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:42.015] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:42.015] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:05:42.022] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:05:42.022] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:42.023] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30862
[08-01 10:05:42.023] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:42.023] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:42.023] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:42.023] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:05:42.025] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:05:42.025] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:42.025] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30873
[08-01 10:05:42.025] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:42.025] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:42.025] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:42.025] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:05:42.027] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:05:42.027] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:42.027] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30884
[08-01 10:05:42.028] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:42.028] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:42.028] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:42.028] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:05:42.029] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[08-01 10:05:42.029] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:42.030] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30896
[08-01 10:05:42.030] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:42.030] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30896, check_time is: 61792000
[08-01 10:05:42.030] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:42.030] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[08-01 10:05:42.030] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 10:05:42.030] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[08-01 10:05:42.030] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61792000
[08-01 10:05:42.030] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61792000
[08-01 10:05:42.032] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:42.032] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30896, check_time is: 61792000
[08-01 10:05:42.032] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:42.032] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:05:42.032] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[08-01 10:05:42.032] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61792000
[08-01 10:05:42.032] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61792000
[08-01 10:05:42.033] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:42.033] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30897, check_time is: 61794000
[08-01 10:05:42.033] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:42.033] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:05:42.033] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[08-01 10:05:42.033] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61794000
[08-01 10:05:42.033] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61794000
[08-01 10:05:42.033] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:42.033] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30913, check_time is: 61826000
[08-01 10:05:42.033] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:42.033] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:05:42.033] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61794000
[08-01 10:05:42.036] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61826000
[08-01 10:05:42.036] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61826000
[08-01 10:05:42.036] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:42.036] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30913, check_time is: 61826000
[08-01 10:05:42.036] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:42.036] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:05:42.036] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61826000
[08-01 10:05:42.036] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61826000
[08-01 10:05:42.036] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61826000
[08-01 10:05:42.038] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[08-01 10:05:42.038] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:42.038] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[08-01 10:05:47.041] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:05:47.041] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:47.041] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa00478b to npu, cycles 65309
[08-01 10:05:47.042] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:47.042] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 65309, check_time is: 130618000
[08-01 10:05:47.042] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:47.042] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[08-01 10:05:47.042] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 10:05:47.042] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 130618000
[08-01 10:05:47.042] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 130618000
[08-01 10:05:47.042] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 130618000
[08-01 10:05:47.042] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:47.042] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 1410130717, check_time is: 2820261434000
[08-01 10:05:47.042] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:47.042] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:05:47.042] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 130618000
[08-01 10:05:47.044] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:05:47.044] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:05:47.045] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xfe00000b to npu, cycles 65321
[08-01 10:05:47.046] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:05:47.046] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:05:47.046] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:05:47.046] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:05:47.047] [130644 ns] n900_vnice_npu_soc.nice_remote_adapter ---check time updated to 0, over! at systemc_time ps: 130644000
