[07-31 09:49:13.028] [0 s] n900_vnice_npu_soc.nice_remote_adapter running....
[07-31 09:49:16.095] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:49:16.095] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.095] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30131
[07-31 09:49:16.096] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.096] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.096] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.096] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:49:16.103] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:49:16.103] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.104] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30184
[07-31 09:49:16.104] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.104] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.104] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.104] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:49:16.106] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:49:16.106] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.106] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30195
[07-31 09:49:16.106] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.106] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.106] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.106] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:49:16.108] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:49:16.108] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.108] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30206
[07-31 09:49:16.109] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.109] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.109] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.109] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:49:16.110] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[07-31 09:49:16.110] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.111] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30218
[07-31 09:49:16.111] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.111] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30218, check_time is: 60436000
[07-31 09:49:16.111] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.111] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[07-31 09:49:16.111] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:49:16.111] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[07-31 09:49:16.111] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60436000
[07-31 09:49:16.111] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60436000
[07-31 09:49:16.113] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.113] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30218, check_time is: 60436000
[07-31 09:49:16.113] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.113] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:49:16.113] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[07-31 09:49:16.113] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60436000
[07-31 09:49:16.113] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60436000
[07-31 09:49:16.114] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.114] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30219, check_time is: 60438000
[07-31 09:49:16.114] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.114] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:49:16.114] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[07-31 09:49:16.114] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60438000
[07-31 09:49:16.114] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60438000
[07-31 09:49:16.114] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.114] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30235, check_time is: 60470000
[07-31 09:49:16.114] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.114] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:49:16.114] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60438000
[07-31 09:49:16.116] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60470000
[07-31 09:49:16.116] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60470000
[07-31 09:49:16.116] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.116] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30235, check_time is: 60470000
[07-31 09:49:16.116] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.116] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:49:16.116] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60470000
[07-31 09:49:16.116] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60470000
[07-31 09:49:16.116] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60470000
[07-31 09:49:16.119] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[07-31 09:49:16.119] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.119] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[07-31 09:49:16.137] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:49:16.137] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.137] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30363
[07-31 09:49:16.137] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.137] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.137] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.137] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:49:16.144] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:49:16.144] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.145] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30416
[07-31 09:49:16.145] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.145] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.145] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.145] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:49:16.147] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:49:16.147] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.147] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30427
[07-31 09:49:16.147] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.147] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.147] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.147] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:49:16.148] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:49:16.148] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.149] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30438
[07-31 09:49:16.149] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.149] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.149] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.149] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:49:16.151] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[07-31 09:49:16.151] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.151] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30450
[07-31 09:49:16.152] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.152] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30450, check_time is: 60900000
[07-31 09:49:16.152] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.152] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[07-31 09:49:16.152] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:49:16.152] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[07-31 09:49:16.152] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60900000
[07-31 09:49:16.152] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60900000
[07-31 09:49:16.155] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.155] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30450, check_time is: 60900000
[07-31 09:49:16.155] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.155] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:49:16.155] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[07-31 09:49:16.155] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60900000
[07-31 09:49:16.155] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60900000
[07-31 09:49:16.156] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.156] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30451, check_time is: 60902000
[07-31 09:49:16.156] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.156] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:49:16.156] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[07-31 09:49:16.156] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60902000
[07-31 09:49:16.156] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60902000
[07-31 09:49:16.156] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.156] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30467, check_time is: 60934000
[07-31 09:49:16.156] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.156] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:49:16.156] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60902000
[07-31 09:49:16.158] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60934000
[07-31 09:49:16.158] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60934000
[07-31 09:49:16.159] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.159] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30467, check_time is: 60934000
[07-31 09:49:16.159] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.159] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:49:16.159] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60934000
[07-31 09:49:16.159] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60934000
[07-31 09:49:16.159] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60934000
[07-31 09:49:16.161] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[07-31 09:49:16.161] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.161] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[07-31 09:49:16.177] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:49:16.177] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.178] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30586
[07-31 09:49:16.178] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.178] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.178] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.178] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:49:16.185] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:49:16.185] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.185] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30639
[07-31 09:49:16.185] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.185] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.185] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.185] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:49:16.187] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:49:16.187] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.187] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30650
[07-31 09:49:16.188] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.188] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.188] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.188] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:49:16.189] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:49:16.189] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.190] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30661
[07-31 09:49:16.190] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.190] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.190] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.190] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:49:16.192] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[07-31 09:49:16.192] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.192] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30673
[07-31 09:49:16.192] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.192] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30673, check_time is: 61346000
[07-31 09:49:16.192] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.192] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[07-31 09:49:16.192] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:49:16.192] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[07-31 09:49:16.192] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61346000
[07-31 09:49:16.192] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61346000
[07-31 09:49:16.196] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.196] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30673, check_time is: 61346000
[07-31 09:49:16.196] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.196] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:49:16.196] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[07-31 09:49:16.196] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61346000
[07-31 09:49:16.196] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61346000
[07-31 09:49:16.196] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.196] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30674, check_time is: 61348000
[07-31 09:49:16.196] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.196] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:49:16.196] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[07-31 09:49:16.196] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61348000
[07-31 09:49:16.196] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61348000
[07-31 09:49:16.197] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.197] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30690, check_time is: 61380000
[07-31 09:49:16.197] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.197] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:49:16.197] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61348000
[07-31 09:49:16.199] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61380000
[07-31 09:49:16.199] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61380000
[07-31 09:49:16.199] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.199] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30690, check_time is: 61380000
[07-31 09:49:16.199] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.199] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:49:16.199] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61380000
[07-31 09:49:16.199] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61380000
[07-31 09:49:16.199] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61380000
[07-31 09:49:16.201] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[07-31 09:49:16.201] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.201] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[07-31 09:49:16.219] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:49:16.219] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.219] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30809
[07-31 09:49:16.220] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.220] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.220] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.220] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:49:16.226] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:49:16.226] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.228] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30862
[07-31 09:49:16.228] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.228] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.228] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.228] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:49:16.229] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:49:16.229] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.230] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30873
[07-31 09:49:16.230] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.230] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.230] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.230] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:49:16.231] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:49:16.231] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.232] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30884
[07-31 09:49:16.232] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.232] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.232] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.232] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:49:16.234] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[07-31 09:49:16.234] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:16.234] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30896
[07-31 09:49:16.234] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.234] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30896, check_time is: 61792000
[07-31 09:49:16.234] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.234] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[07-31 09:49:16.234] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:49:16.234] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[07-31 09:49:16.234] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61792000
[07-31 09:49:16.234] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61792000
[07-31 09:49:16.237] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.237] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30896, check_time is: 61792000
[07-31 09:49:16.237] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.237] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:49:16.237] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[07-31 09:49:16.237] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61792000
[07-31 09:49:16.237] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61792000
[07-31 09:49:16.237] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.237] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30897, check_time is: 61794000
[07-31 09:49:16.237] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.237] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:49:16.237] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[07-31 09:49:16.238] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61794000
[07-31 09:49:16.238] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61794000
[07-31 09:49:16.238] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.238] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30913, check_time is: 61826000
[07-31 09:49:16.238] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.238] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:49:16.238] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61794000
[07-31 09:49:16.240] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61826000
[07-31 09:49:16.240] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61826000
[07-31 09:49:16.240] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:16.240] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30913, check_time is: 61826000
[07-31 09:49:16.240] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:16.240] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:49:16.240] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61826000
[07-31 09:49:16.240] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61826000
[07-31 09:49:16.240] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61826000
[07-31 09:49:16.243] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[07-31 09:49:16.243] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:16.243] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[07-31 09:49:20.991] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:49:20.991] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:20.992] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa00478b to npu, cycles 65309
[07-31 09:49:20.992] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:20.992] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 65309, check_time is: 130618000
[07-31 09:49:20.992] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:20.992] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:49:20.992] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:49:20.992] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 130618000
[07-31 09:49:20.992] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 130618000
[07-31 09:49:20.992] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 130618000
[07-31 09:49:20.993] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:20.993] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 1410130717, check_time is: 2820261434000
[07-31 09:49:20.993] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:20.993] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:49:20.993] [130618 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 130618000
[07-31 09:49:20.995] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 09:49:20.995] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:49:20.996] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xfe00000b to npu, cycles 65321
[07-31 09:49:20.997] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:49:20.997] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:49:20.997] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:49:20.997] [130642 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:49:20.998] [130644 ns] n900_vnice_npu_soc.nice_remote_adapter ---check time updated to 0, over! at systemc_time ps: 130644000
