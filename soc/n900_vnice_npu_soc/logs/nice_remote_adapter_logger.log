[08-01 10:18:10.066] [0 s] n900_vnice_npu_soc.nice_remote_adapter running....
[08-01 10:18:13.503] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:18:13.503] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.504] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30131
[08-01 10:18:13.504] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.504] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.504] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.504] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:18:13.509] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:18:13.509] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.510] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30184
[08-01 10:18:13.511] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.511] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.511] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.511] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:18:13.512] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:18:13.512] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.512] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30195
[08-01 10:18:13.512] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.512] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.512] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.512] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:18:13.513] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:18:13.513] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.513] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30206
[08-01 10:18:13.514] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.514] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.514] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.514] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:18:13.515] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[08-01 10:18:13.515] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.515] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30218
[08-01 10:18:13.515] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.515] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30218, check_time is: 60436000
[08-01 10:18:13.515] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.515] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[08-01 10:18:13.515] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 10:18:13.515] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[08-01 10:18:13.515] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60436000
[08-01 10:18:13.515] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60436000
[08-01 10:18:13.518] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.518] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30218, check_time is: 60436000
[08-01 10:18:13.518] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.518] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:18:13.518] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[08-01 10:18:13.518] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60436000
[08-01 10:18:13.518] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60436000
[08-01 10:18:13.518] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.518] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30219, check_time is: 60438000
[08-01 10:18:13.518] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.518] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:18:13.518] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[08-01 10:18:13.518] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60438000
[08-01 10:18:13.518] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60438000
[08-01 10:18:13.519] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.519] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30235, check_time is: 60470000
[08-01 10:18:13.519] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.519] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:18:13.519] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60438000
[08-01 10:18:13.520] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60470000
[08-01 10:18:13.520] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60470000
[08-01 10:18:13.521] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.521] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30235, check_time is: 60470000
[08-01 10:18:13.521] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.521] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:18:13.521] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60470000
[08-01 10:18:13.521] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60470000
[08-01 10:18:13.521] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60470000
[08-01 10:18:13.526] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[08-01 10:18:13.526] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.526] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[08-01 10:18:13.542] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:18:13.542] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.543] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30363
[08-01 10:18:13.543] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.543] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.543] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.543] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:18:13.548] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:18:13.548] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.549] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30416
[08-01 10:18:13.549] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.549] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.549] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.549] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:18:13.550] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:18:13.550] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.551] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30427
[08-01 10:18:13.551] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.551] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.551] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.551] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:18:13.552] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:18:13.552] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.552] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30438
[08-01 10:18:13.552] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.552] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.552] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.552] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:18:13.554] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[08-01 10:18:13.554] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.554] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30450
[08-01 10:18:13.554] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.554] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30450, check_time is: 60900000
[08-01 10:18:13.554] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.554] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[08-01 10:18:13.554] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 10:18:13.554] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[08-01 10:18:13.554] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60900000
[08-01 10:18:13.554] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60900000
[08-01 10:18:13.557] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.557] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30450, check_time is: 60900000
[08-01 10:18:13.557] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.557] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:18:13.557] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[08-01 10:18:13.557] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60900000
[08-01 10:18:13.557] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60900000
[08-01 10:18:13.557] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.557] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30451, check_time is: 60902000
[08-01 10:18:13.557] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.557] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:18:13.557] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[08-01 10:18:13.557] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60902000
[08-01 10:18:13.557] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60902000
[08-01 10:18:13.558] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.558] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30467, check_time is: 60934000
[08-01 10:18:13.558] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.558] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:18:13.558] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60902000
[08-01 10:18:13.559] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60934000
[08-01 10:18:13.559] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60934000
[08-01 10:18:13.559] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.559] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30467, check_time is: 60934000
[08-01 10:18:13.559] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.559] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:18:13.559] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60934000
[08-01 10:18:13.559] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60934000
[08-01 10:18:13.559] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60934000
[08-01 10:18:13.562] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[08-01 10:18:13.562] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.562] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[08-01 10:18:13.574] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:18:13.574] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.575] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30586
[08-01 10:18:13.575] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.575] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.575] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.575] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:18:13.580] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:18:13.580] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.581] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30639
[08-01 10:18:13.581] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.581] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.581] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.581] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:18:13.582] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:18:13.582] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.582] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30650
[08-01 10:18:13.583] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.583] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.583] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.583] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:18:13.584] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:18:13.584] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.584] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30661
[08-01 10:18:13.584] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.584] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.584] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.584] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:18:13.585] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[08-01 10:18:13.585] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.586] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30673
[08-01 10:18:13.586] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.586] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30673, check_time is: 61346000
[08-01 10:18:13.586] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.586] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[08-01 10:18:13.586] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 10:18:13.586] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[08-01 10:18:13.586] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61346000
[08-01 10:18:13.586] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61346000
[08-01 10:18:13.588] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.588] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30673, check_time is: 61346000
[08-01 10:18:13.588] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.588] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:18:13.588] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[08-01 10:18:13.588] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61346000
[08-01 10:18:13.588] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61346000
[08-01 10:18:13.589] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.589] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30674, check_time is: 61348000
[08-01 10:18:13.589] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.589] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:18:13.589] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[08-01 10:18:13.589] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61348000
[08-01 10:18:13.589] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61348000
[08-01 10:18:13.589] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.589] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30690, check_time is: 61380000
[08-01 10:18:13.589] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.589] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:18:13.589] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61348000
[08-01 10:18:13.591] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61380000
[08-01 10:18:13.591] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61380000
[08-01 10:18:13.591] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.591] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30690, check_time is: 61380000
[08-01 10:18:13.591] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.591] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:18:13.591] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61380000
[08-01 10:18:13.591] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61380000
[08-01 10:18:13.591] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61380000
[08-01 10:18:13.594] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[08-01 10:18:13.594] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.594] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[08-01 10:18:13.606] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:18:13.606] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.606] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30809
[08-01 10:18:13.607] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.607] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.607] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.607] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:18:13.612] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:18:13.612] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.613] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30862
[08-01 10:18:13.613] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.613] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.613] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.613] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:18:13.614] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:18:13.614] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.615] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30873
[08-01 10:18:13.615] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.615] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.615] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.615] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:18:13.616] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:18:13.616] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.616] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30884
[08-01 10:18:13.616] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.616] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.616] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.616] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:18:13.617] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[08-01 10:18:13.617] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:13.618] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30896
[08-01 10:18:13.618] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.618] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30896, check_time is: 61792000
[08-01 10:18:13.618] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.618] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[08-01 10:18:13.618] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 10:18:13.618] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[08-01 10:18:13.618] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61792000
[08-01 10:18:13.618] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61792000
[08-01 10:18:13.620] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.620] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30896, check_time is: 61792000
[08-01 10:18:13.620] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.620] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:18:13.620] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[08-01 10:18:13.620] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61792000
[08-01 10:18:13.620] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61792000
[08-01 10:18:13.620] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.620] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30897, check_time is: 61794000
[08-01 10:18:13.620] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.620] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:18:13.620] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[08-01 10:18:13.621] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61794000
[08-01 10:18:13.621] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61794000
[08-01 10:18:13.621] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.621] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30913, check_time is: 61826000
[08-01 10:18:13.621] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.621] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:18:13.621] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61794000
[08-01 10:18:13.622] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61826000
[08-01 10:18:13.622] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61826000
[08-01 10:18:13.622] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:13.622] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30913, check_time is: 61826000
[08-01 10:18:13.622] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:13.622] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:18:13.622] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61826000
[08-01 10:18:13.622] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61826000
[08-01 10:18:13.622] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61826000
[08-01 10:18:13.625] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[08-01 10:18:13.625] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:13.625] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[08-01 10:18:16.700] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:18:16.700] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:16.701] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa00478b to npu, cycles 65204
[08-01 10:18:16.701] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:16.701] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 65204, check_time is: 130408000
[08-01 10:18:16.701] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:16.701] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[08-01 10:18:16.701] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[08-01 10:18:16.701] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 130408000
[08-01 10:18:16.701] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 130408000
[08-01 10:18:16.701] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 130408000
[08-01 10:18:16.702] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:16.702] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 1410130612, check_time is: 2820261224000
[08-01 10:18:16.702] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:16.702] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[08-01 10:18:16.702] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 130408000
[08-01 10:18:16.703] [130432 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[08-01 10:18:16.703] [130432 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[08-01 10:18:16.704] [130432 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xfe00000b to npu, cycles 65216
[08-01 10:18:16.705] [130432 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[08-01 10:18:16.705] [130432 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[08-01 10:18:16.705] [130432 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[08-01 10:18:16.705] [130432 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[08-01 10:18:16.705] [130434 ns] n900_vnice_npu_soc.nice_remote_adapter ---check time updated to 0, over! at systemc_time ps: 130434000
