[07-31 10:00:45.681] [0 s] n900_vnice_npu_soc.nice_remote_adapter running....
[07-31 10:00:48.890] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 10:00:48.890] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:48.891] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30131
[07-31 10:00:48.892] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.892] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:48.892] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.892] [60262 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 10:00:48.903] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 10:00:48.903] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:48.904] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30184
[07-31 10:00:48.904] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.904] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:48.904] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.904] [60368 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 10:00:48.906] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 10:00:48.906] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:48.907] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30195
[07-31 10:00:48.907] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.907] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:48.907] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.907] [60390 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 10:00:48.909] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 10:00:48.909] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:48.909] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30206
[07-31 10:00:48.910] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.910] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:48.910] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.910] [60412 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 10:00:48.912] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[07-31 10:00:48.912] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:48.912] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30218
[07-31 10:00:48.912] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.912] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30218, check_time is: 60436000
[07-31 10:00:48.912] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.912] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[07-31 10:00:48.912] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 10:00:48.912] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[07-31 10:00:48.912] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60436000
[07-31 10:00:48.912] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60436000
[07-31 10:00:48.915] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.915] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30218, check_time is: 60436000
[07-31 10:00:48.915] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.915] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 10:00:48.915] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[07-31 10:00:48.915] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60436000
[07-31 10:00:48.915] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60436000
[07-31 10:00:48.915] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.915] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30219, check_time is: 60438000
[07-31 10:00:48.915] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.915] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 10:00:48.915] [60436 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60436000
[07-31 10:00:48.915] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60438000
[07-31 10:00:48.915] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60438000
[07-31 10:00:48.916] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.916] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30235, check_time is: 60470000
[07-31 10:00:48.916] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.916] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 10:00:48.916] [60438 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60438000
[07-31 10:00:48.918] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60470000
[07-31 10:00:48.918] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60470000
[07-31 10:00:48.919] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.919] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30235, check_time is: 60470000
[07-31 10:00:48.919] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.919] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 10:00:48.919] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60470000
[07-31 10:00:48.919] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60470000
[07-31 10:00:48.919] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60470000
[07-31 10:00:48.922] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[07-31 10:00:48.922] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:48.922] [60470 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[07-31 10:00:48.943] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 10:00:48.943] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:48.943] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30363
[07-31 10:00:48.944] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.944] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:48.944] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.944] [60726 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 10:00:48.952] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 10:00:48.952] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:48.953] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30416
[07-31 10:00:48.954] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.954] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:48.954] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.954] [60832 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 10:00:48.956] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 10:00:48.956] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:48.956] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30427
[07-31 10:00:48.956] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.956] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:48.956] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.956] [60854 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 10:00:48.958] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 10:00:48.958] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:48.959] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30438
[07-31 10:00:48.959] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.959] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:48.959] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.959] [60876 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 10:00:48.961] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[07-31 10:00:48.961] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:48.962] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30450
[07-31 10:00:48.962] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.962] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30450, check_time is: 60900000
[07-31 10:00:48.962] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.962] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[07-31 10:00:48.962] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 10:00:48.962] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[07-31 10:00:48.962] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60900000
[07-31 10:00:48.962] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60900000
[07-31 10:00:48.965] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.965] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30450, check_time is: 60900000
[07-31 10:00:48.965] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.965] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 10:00:48.965] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[07-31 10:00:48.965] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60900000
[07-31 10:00:48.965] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60900000
[07-31 10:00:48.965] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.965] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30451, check_time is: 60902000
[07-31 10:00:48.965] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.965] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 10:00:48.965] [60900 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60900000
[07-31 10:00:48.965] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60902000
[07-31 10:00:48.965] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60902000
[07-31 10:00:48.966] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.966] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30467, check_time is: 60934000
[07-31 10:00:48.966] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.966] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 10:00:48.966] [60902 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60902000
[07-31 10:00:48.968] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60934000
[07-31 10:00:48.968] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60934000
[07-31 10:00:48.968] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.968] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30467, check_time is: 60934000
[07-31 10:00:48.968] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.968] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 10:00:48.968] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60934000
[07-31 10:00:48.968] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60934000
[07-31 10:00:48.968] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60934000
[07-31 10:00:48.971] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[07-31 10:00:48.971] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:48.971] [60934 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[07-31 10:00:48.992] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 10:00:48.993] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:48.993] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30586
[07-31 10:00:48.993] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:48.993] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:48.993] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:48.993] [61172 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 10:00:49.002] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 10:00:49.002] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:49.003] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30639
[07-31 10:00:49.003] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:49.003] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:49.003] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:49.003] [61278 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 10:00:49.005] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 10:00:49.005] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:49.005] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30650
[07-31 10:00:49.006] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:49.006] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:49.006] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:49.006] [61300 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 10:00:49.009] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 10:00:49.009] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:49.009] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30661
[07-31 10:00:49.009] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:49.009] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:49.009] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:49.009] [61322 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 10:00:49.012] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[07-31 10:00:49.012] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:49.013] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30673
[07-31 10:00:49.013] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:49.013] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30673, check_time is: 61346000
[07-31 10:00:49.013] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:49.013] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[07-31 10:00:49.013] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 10:00:49.013] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[07-31 10:00:49.013] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61346000
[07-31 10:00:49.013] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61346000
[07-31 10:00:49.016] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:49.016] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30673, check_time is: 61346000
[07-31 10:00:49.016] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:49.016] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 10:00:49.016] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[07-31 10:00:49.016] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61346000
[07-31 10:00:49.016] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61346000
[07-31 10:00:49.016] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:49.016] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30674, check_time is: 61348000
[07-31 10:00:49.016] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:49.016] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 10:00:49.016] [61346 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61346000
[07-31 10:00:49.017] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61348000
[07-31 10:00:49.017] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61348000
[07-31 10:00:49.017] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:49.017] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30690, check_time is: 61380000
[07-31 10:00:49.017] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:49.017] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 10:00:49.017] [61348 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61348000
[07-31 10:00:49.019] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61380000
[07-31 10:00:49.019] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61380000
[07-31 10:00:49.019] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:49.019] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30690, check_time is: 61380000
[07-31 10:00:49.019] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:49.019] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 10:00:49.019] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61380000
[07-31 10:00:49.019] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61380000
[07-31 10:00:49.019] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61380000
[07-31 10:00:49.023] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[07-31 10:00:49.023] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:49.023] [61380 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[07-31 10:00:49.042] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 10:00:49.042] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:49.043] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xf7300b to npu, cycles 30809
[07-31 10:00:49.043] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:49.043] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:49.043] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:49.043] [61618 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 10:00:49.051] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 10:00:49.051] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:49.052] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30862
[07-31 10:00:49.052] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:49.052] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:49.052] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:49.052] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 10:00:49.054] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 10:00:49.054] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:49.054] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30873
[07-31 10:00:49.054] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:49.054] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:49.054] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:49.054] [61746 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 10:00:49.056] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 10:00:49.056] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:49.057] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2f7300b to npu, cycles 30884
[07-31 10:00:49.057] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:49.057] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:49.057] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:49.057] [61768 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 10:00:49.059] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter slave 1 nb_transport_fw: phase 100 cmd 1 addr 0x0 len 0x7c mode 0
[07-31 10:00:49.059] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:49.060] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send vnice instr: 0x7407e0ab to npu, cycles 30896
[07-31 10:00:49.060] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:49.060] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30896, check_time is: 61792000
[07-31 10:00:49.060] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:49.060] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by vnice_instr
[07-31 10:00:49.060] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 10:00:49.060] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[07-31 10:00:49.060] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61792000
[07-31 10:00:49.060] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61792000
[07-31 10:00:49.062] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:49.062] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30896, check_time is: 61792000
[07-31 10:00:49.062] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:49.062] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 10:00:49.062] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[07-31 10:00:49.062] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61792000
[07-31 10:00:49.062] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61792000
[07-31 10:00:49.062] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:49.062] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30897, check_time is: 61794000
[07-31 10:00:49.062] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:49.062] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 10:00:49.062] [61792 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61792000
[07-31 10:00:49.063] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61794000
[07-31 10:00:49.063] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61794000
[07-31 10:00:49.063] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:49.063] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30913, check_time is: 61826000
[07-31 10:00:49.063] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:49.063] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 10:00:49.063] [61794 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61794000
[07-31 10:00:49.065] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61826000
[07-31 10:00:49.065] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61826000
[07-31 10:00:49.065] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:49.065] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30913, check_time is: 61826000
[07-31 10:00:49.065] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:49.065] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 10:00:49.065] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61826000
[07-31 10:00:49.065] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61826000
[07-31 10:00:49.065] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61826000
[07-31 10:00:49.069] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 30
[07-31 10:00:49.069] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:49.069] [61826 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 30, type: VNICE INSTR_RSP
[07-31 10:00:54.766] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 10:00:54.767] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:54.767] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa00478b to npu, cycles 65204
[07-31 10:00:54.767] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:54.767] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 65204, check_time is: 130408000
[07-31 10:00:54.767] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:54.767] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 10:00:54.767] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 10:00:54.767] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 130408000
[07-31 10:00:54.767] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 130408000
[07-31 10:00:54.767] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 130408000
[07-31 10:00:54.768] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:54.768] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 1410130612, check_time is: 2820261224000
[07-31 10:00:54.768] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:54.768] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 10:00:54.768] [130408 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 130408000
[07-31 10:00:54.770] [130432 ns] n900_vnice_npu_soc.nice_remote_adapter slave 0 nb_transport_fw: phase 0 cmd 1 addr 0x0 len 0x40 mode 0
[07-31 10:00:54.770] [130432 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 10:00:54.771] [130432 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xfe00000b to npu, cycles 65216
[07-31 10:00:54.772] [130432 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 10:00:54.772] [130432 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 10:00:54.772] [130432 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 10:00:54.772] [130432 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 10:00:54.772] [130434 ns] n900_vnice_npu_soc.nice_remote_adapter ---check time updated to 0, over! at systemc_time ps: 130434000
