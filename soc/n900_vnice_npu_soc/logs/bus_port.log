[07-29 14:34:19.124] [0 s] axi-master n900_vnice_npu_soc.n900_vnice.mem__128_axi_m binding device n900_vnice_npu_soc.bus.s_0
[07-29 14:34:19.126] [0 s] nice-master n900_vnice_npu_soc.n900_vnice.nice_m__32 binding dev n900_vnice_npu_soc.nice_remote_adapter.nice
[07-29 14:34:19.128] [0 s] vnice-master n900_vnice_npu_soc.n900_vnice.vnice_m__32 binding dev n900_vnice_npu_soc.nice_remote_adapter.vnice
[07-29 14:34:19.131] [0 s] axi-slave stub n900_vnice_npu_soc.n900_vnice.Vn900_core_rams port n900_vnice_npu_soc.n900_vnice.slv__64_axi_s
[07-29 14:34:19.132] [0 s] ------- load n900_vnice_npu_soc.bus route rules: -------
[07-29 14:34:19.132] [0 s] n900_vnice_npu_soc.bus rule_0: slave0 to master0 n900_vnice_npu_soc.acc0 addr_range: start 0x100000 -- end 0x1fffff, length 0x100000
[07-29 14:34:19.132] [0 s] n900_vnice_npu_soc.bus rule_1: slave0 to master1 n900_vnice_npu_soc.mrom addr_range: start 0x0 -- end 0xfffff, length 0x100000
[07-29 14:34:19.132] [0 s] n900_vnice_npu_soc.bus rule_2: slave0 to master2 n900_vnice_npu_soc.gpio addr_range: start 0x10012000 -- end 0x10012fff, length 0x1000
[07-29 14:34:19.132] [0 s] n900_vnice_npu_soc.bus rule_3: slave0 to master3 n900_vnice_npu_soc.uart addr_range: start 0x10013000 -- end 0x10013fff, length 0x1000
[07-29 14:34:19.132] [0 s] n900_vnice_npu_soc.bus rule_4: slave0 to master4 n900_vnice_npu_soc.qspi0 addr_range: start 0x10014000 -- end 0x10023fff, length 0x10000
[07-29 14:34:19.132] [0 s] n900_vnice_npu_soc.bus rule_5: slave0 to master5 n900_vnice_npu_soc.qspi1 addr_range: start 0x60000000 -- end 0x6effffff, length 0xf000000
[07-29 14:34:19.132] [0 s] n900_vnice_npu_soc.bus rule_6: slave0 to master6 n900_vnice_npu_soc.hole addr_range: start 0x50000000 -- end 0x5fedffff, length 0xfee0000
[07-29 14:34:19.132] [0 s] n900_vnice_npu_soc.bus rule_7: slave0 to master7 n900_vnice_npu_soc.xip addr_range: start 0x20000000 -- end 0x3fffffff, length 0x20000000
[07-29 14:34:19.132] [0 s] n900_vnice_npu_soc.bus rule_8: slave0 to master8 n900_vnice_npu_soc.ddr addr_range: start 0x70180000 -- end 0x9017ffff, length 0x20000000
[07-29 14:34:19.132] [0 s] n900_vnice_npu_soc.bus rule_9: slave0 to master9 n900_vnice_npu_soc.acc1 addr_range: start 0x10033000 -- end 0x10033fff, length 0x1000
[07-29 14:34:19.132] [0 s] n900_vnice_npu_soc.bus rule_10: slave0 to master10 n900_vnice_npu_soc.n900_vnice addr_range: start 0x70000000 -- end 0x7017ffff, length 0x180000
[07-29 14:34:19.132] [0 s] n900_vnice_npu_soc.bus rule_11: slave1 to master9 n900_vnice_npu_soc.acc1 addr_range: start 0x10033000 -- end 0x10033fff, length 0x1000
[07-29 14:34:20.526] [0 s] acc0 ------ load_images -------
[07-29 14:34:20.526] [0 s] acc1 ------ load_images -------
[07-29 14:34:20.526] [0 s] bus ------ load_images -------
[07-29 14:34:20.526] [0 s] ddr ------ load_images -------
[07-29 14:34:20.526] [0 s] gpio ------ load_images -------
[07-29 14:34:20.526] [0 s] hole ------ load_images -------
[07-29 14:34:20.526] [0 s] mrom ------ load_images -------
[07-29 14:34:20.526] [0 s] n900_vnice ------ load_images -------
[07-29 14:34:20.526] [0 s] nice_remote_adapter ------ load_images -------
[07-29 14:34:20.526] [0 s] qspi0 ------ load_images -------
[07-29 14:34:20.526] [0 s] qspi1 ------ load_images -------
[07-29 14:34:20.526] [0 s] xip ------ load_images -------
[07-29 14:34:20.531] [40 ns] mem__128_axi_m_0_bridge ------ load_images -------
[07-29 14:34:20.531] [40 ns] load_images: ./fw/operator_work.elf offset 0x0
[07-29 14:34:20.531] [40 ns] load_elf: file ./fw/operator_work.elf with 3 segments
[07-29 14:34:20.531] [40 ns] load_elf: segment 0x70000000         .. 0x7000213f        
[07-29 14:34:20.531] [40 ns] load_elf: segment 0x70100000         .. 0x70100e4f        
[07-29 14:34:20.531] [40 ns] load_elf: segment 0x70100e50         .. 0x7010164f        
[07-29 14:34:20.532] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:20.869] [6018 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a4403b0
[07-29 14:34:20.869] [6018 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:20.869] [6018 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:20.869] [6018 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:20.869] [6018 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013018, tr: 0x7f526a0c4ed0, reqid: 0
[07-29 14:34:20.869] [6018 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013018, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f526a4403b0
[07-29 14:34:20.869] [6018 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x18 data 0x0 len 0x4
[07-29 14:34:20.869] [6018 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x18 len 0x4  in pv mode
[07-29 14:34:20.870] [6018 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013018 data 0x0 len 0x4
[07-29 14:34:20.870] [6018 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 0
[07-29 14:34:20.870] [6018 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:20.870] [6020 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:20.870] [6020 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:20.871] [6044 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013008, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 0
[07-29 14:34:20.871] [6044 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:20.871] [6044 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x8 data 0x6a0005c0 len 0x4
[07-29 14:34:20.871] [6044 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x8 len 0x4  in pv mode
[07-29 14:34:20.871] [6044 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013008 data 0x0 len 0x4
[07-29 14:34:20.871] [6044 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 0
[07-29 14:34:20.871] [6044 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:20.871] [6044 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:20.871] [6044 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:20.871] [6044 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:20.871] [6044 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:20.871] [6044 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:20.873] [6072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013008, tr: 0x7f526a0c4ed0, reqid: 1
[07-29 14:34:20.873] [6072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:20.873] [6072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:20.873] [6072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:20.873] [6072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:20.873] [6072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013008, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:20.873] [6072 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x8 data 0x1 len 0x4
[07-29 14:34:20.873] [6072 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x8 len 0x4  in pv mode
[07-29 14:34:20.873] [6072 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013008 data 0x1 len 0x4
[07-29 14:34:20.873] [6072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 1
[07-29 14:34:20.873] [6072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:20.873] [6074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:20.873] [6074 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:20.874] [6100 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x1001300c, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 1
[07-29 14:34:20.874] [6100 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:20.874] [6100 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0xc data 0x6a000d50 len 0x4
[07-29 14:34:20.874] [6100 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0xc len 0x4  in pv mode
[07-29 14:34:20.874] [6100 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x1001300c data 0x0 len 0x4
[07-29 14:34:20.874] [6100 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 1
[07-29 14:34:20.874] [6100 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:20.874] [6100 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:20.874] [6100 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:20.874] [6100 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:20.874] [6100 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:20.874] [6100 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:20.876] [6128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 1001300c, tr: 0x7f526a0c4ed0, reqid: 2
[07-29 14:34:20.876] [6128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:20.876] [6128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:20.876] [6128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:20.876] [6128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:20.876] [6128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x1001300c, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:20.876] [6128 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0xc data 0x1 len 0x4
[07-29 14:34:20.876] [6128 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0xc len 0x4  in pv mode
[07-29 14:34:20.876] [6128 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x1001300c data 0x1 len 0x4
[07-29 14:34:20.876] [6128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 2
[07-29 14:34:20.876] [6128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:20.876] [6130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:20.876] [6130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.523] [17400 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 2
[07-29 14:34:21.523] [17400 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.523] [17400 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69fea590 len 0x4
[07-29 14:34:21.523] [17400 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.523] [17400 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.523] [17400 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 2
[07-29 14:34:21.523] [17400 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.523] [17400 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.523] [17400 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.523] [17400 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.523] [17400 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.523] [17400 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.524] [17428 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 3
[07-29 14:34:21.524] [17428 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.524] [17428 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.524] [17428 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.524] [17428 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.524] [17428 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.524] [17428 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4e len 0x4
[07-29 14:34:21.524] [17428 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.524] [17428 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4e len 0x4
[07-29 14:34:21.524] [17428 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 3
[07-29 14:34:21.524] [17428 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.525] [17430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.525] [17430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.536] [17632 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 3
[07-29 14:34:21.536] [17632 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.536] [17632 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69fec290 len 0x4
[07-29 14:34:21.536] [17632 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.536] [17632 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.536] [17632 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 3
[07-29 14:34:21.536] [17632 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.536] [17632 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.536] [17632 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.536] [17632 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.536] [17632 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.536] [17632 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.538] [17660 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 4
[07-29 14:34:21.538] [17660 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.538] [17660 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.538] [17660 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.538] [17660 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.538] [17660 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.538] [17660 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[07-29 14:34:21.538] [17660 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.538] [17660 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[07-29 14:34:21.538] [17660 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 4
[07-29 14:34:21.538] [17660 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.538] [17662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.538] [17662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.550] [17864 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 4
[07-29 14:34:21.550] [17864 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.550] [17864 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69fedf90 len 0x4
[07-29 14:34:21.550] [17864 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.550] [17864 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.550] [17864 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 4
[07-29 14:34:21.550] [17864 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.550] [17864 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.550] [17864 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.550] [17864 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.550] [17864 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.550] [17864 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.552] [17892 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 5
[07-29 14:34:21.552] [17892 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.552] [17892 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.552] [17892 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.552] [17892 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.552] [17892 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.552] [17892 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x63 len 0x4
[07-29 14:34:21.552] [17892 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.552] [17892 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x63 len 0x4
[07-29 14:34:21.552] [17892 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 5
[07-29 14:34:21.552] [17892 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.552] [17894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.552] [17894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.563] [18096 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 5
[07-29 14:34:21.563] [18096 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.563] [18096 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a122620 len 0x4
[07-29 14:34:21.563] [18096 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.563] [18096 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.563] [18096 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 5
[07-29 14:34:21.563] [18096 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.563] [18096 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.563] [18096 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.563] [18096 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.563] [18096 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.563] [18096 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.565] [18124 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 6
[07-29 14:34:21.565] [18124 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.565] [18124 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.565] [18124 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.565] [18124 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.565] [18124 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.565] [18124 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[07-29 14:34:21.565] [18124 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.565] [18124 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[07-29 14:34:21.565] [18124 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 6
[07-29 14:34:21.565] [18124 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.565] [18126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.565] [18126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.577] [18328 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 6
[07-29 14:34:21.577] [18328 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.577] [18328 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a124320 len 0x4
[07-29 14:34:21.577] [18328 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.577] [18328 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.577] [18328 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 6
[07-29 14:34:21.577] [18328 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.577] [18328 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.577] [18328 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.577] [18328 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.577] [18328 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.577] [18328 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.578] [18356 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 7
[07-29 14:34:21.578] [18356 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.578] [18356 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.578] [18356 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.578] [18356 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.578] [18356 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.578] [18356 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[07-29 14:34:21.578] [18356 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.578] [18356 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[07-29 14:34:21.578] [18356 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 7
[07-29 14:34:21.578] [18356 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.579] [18358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.579] [18358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.590] [18560 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 7
[07-29 14:34:21.590] [18560 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.590] [18560 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a126020 len 0x4
[07-29 14:34:21.590] [18560 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.590] [18560 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.590] [18560 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 7
[07-29 14:34:21.590] [18560 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.590] [18560 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.590] [18560 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.590] [18560 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.590] [18560 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.590] [18560 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.592] [18588 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 8
[07-29 14:34:21.592] [18588 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.592] [18588 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.592] [18588 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.592] [18588 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.592] [18588 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.592] [18588 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[07-29 14:34:21.592] [18588 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.592] [18588 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[07-29 14:34:21.592] [18588 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 8
[07-29 14:34:21.592] [18588 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.592] [18590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.592] [18590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.603] [18792 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 8
[07-29 14:34:21.603] [18792 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.603] [18792 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a127d20 len 0x4
[07-29 14:34:21.603] [18792 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.603] [18792 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.603] [18792 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 8
[07-29 14:34:21.603] [18792 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.603] [18792 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.603] [18792 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.603] [18792 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.603] [18792 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.603] [18792 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.605] [18820 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 9
[07-29 14:34:21.605] [18820 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.605] [18820 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.605] [18820 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.605] [18820 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.605] [18820 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.605] [18820 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[07-29 14:34:21.605] [18820 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.605] [18820 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[07-29 14:34:21.605] [18820 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 9
[07-29 14:34:21.605] [18820 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.605] [18822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.605] [18822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.617] [19024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 9
[07-29 14:34:21.617] [19024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.617] [19024 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a129a20 len 0x4
[07-29 14:34:21.617] [19024 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.617] [19024 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.617] [19024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 9
[07-29 14:34:21.617] [19024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.617] [19024 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.617] [19024 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.617] [19024 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.617] [19024 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.617] [19024 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.618] [19052 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 10
[07-29 14:34:21.618] [19052 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.618] [19052 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.618] [19052 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.618] [19052 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.618] [19052 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.618] [19052 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x53 len 0x4
[07-29 14:34:21.618] [19052 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.618] [19052 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x53 len 0x4
[07-29 14:34:21.618] [19052 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 10
[07-29 14:34:21.618] [19052 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.618] [19054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.618] [19054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.630] [19256 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 10
[07-29 14:34:21.630] [19256 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.630] [19256 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a12b720 len 0x4
[07-29 14:34:21.630] [19256 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.630] [19256 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.630] [19256 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 10
[07-29 14:34:21.630] [19256 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.630] [19256 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.630] [19256 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.630] [19256 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.630] [19256 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.630] [19256 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.632] [19284 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 11
[07-29 14:34:21.632] [19284 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.632] [19284 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.632] [19284 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.632] [19284 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.632] [19284 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.632] [19284 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x44 len 0x4
[07-29 14:34:21.632] [19284 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.632] [19284 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x44 len 0x4
[07-29 14:34:21.632] [19284 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 11
[07-29 14:34:21.632] [19284 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.632] [19286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.632] [19286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.643] [19488 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 11
[07-29 14:34:21.643] [19488 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.643] [19488 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a12d420 len 0x4
[07-29 14:34:21.643] [19488 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.643] [19488 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.643] [19488 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 11
[07-29 14:34:21.643] [19488 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.643] [19488 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.643] [19488 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.643] [19488 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.643] [19488 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.643] [19488 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.645] [19516 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 12
[07-29 14:34:21.645] [19516 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.645] [19516 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.645] [19516 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.645] [19516 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.645] [19516 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.645] [19516 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4b len 0x4
[07-29 14:34:21.645] [19516 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.645] [19516 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4b len 0x4
[07-29 14:34:21.645] [19516 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 12
[07-29 14:34:21.645] [19516 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.645] [19518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.645] [19518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.657] [19720 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 12
[07-29 14:34:21.657] [19720 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.657] [19720 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a12f120 len 0x4
[07-29 14:34:21.657] [19720 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.657] [19720 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.657] [19720 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 12
[07-29 14:34:21.657] [19720 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.657] [19720 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.657] [19720 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.657] [19720 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.657] [19720 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.657] [19720 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.658] [19748 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 13
[07-29 14:34:21.658] [19748 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.658] [19748 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.658] [19748 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.658] [19748 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.658] [19748 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.658] [19748 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[07-29 14:34:21.658] [19748 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.658] [19748 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[07-29 14:34:21.658] [19748 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 13
[07-29 14:34:21.658] [19748 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.659] [19750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.659] [19750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.670] [19952 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 13
[07-29 14:34:21.670] [19952 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.670] [19952 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a130e20 len 0x4
[07-29 14:34:21.670] [19952 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.670] [19952 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.670] [19952 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 13
[07-29 14:34:21.670] [19952 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.670] [19952 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.670] [19952 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.670] [19952 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.670] [19952 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.670] [19952 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.672] [19980 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 14
[07-29 14:34:21.672] [19980 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.672] [19980 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.672] [19980 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.672] [19980 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.672] [19980 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.672] [19980 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x42 len 0x4
[07-29 14:34:21.672] [19980 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.672] [19980 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x42 len 0x4
[07-29 14:34:21.672] [19980 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 14
[07-29 14:34:21.672] [19980 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.672] [19982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.672] [19982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.683] [20184 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 14
[07-29 14:34:21.683] [20184 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.683] [20184 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a132b20 len 0x4
[07-29 14:34:21.683] [20184 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.683] [20184 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.683] [20184 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 14
[07-29 14:34:21.683] [20184 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.683] [20184 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.683] [20184 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.683] [20184 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.683] [20184 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.683] [20184 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.685] [20212 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 15
[07-29 14:34:21.685] [20212 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.685] [20212 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.685] [20212 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.685] [20212 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.685] [20212 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.685] [20212 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[07-29 14:34:21.685] [20212 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.685] [20212 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[07-29 14:34:21.685] [20212 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 15
[07-29 14:34:21.685] [20212 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.685] [20214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.685] [20214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.697] [20416 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 15
[07-29 14:34:21.697] [20416 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.697] [20416 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a134820 len 0x4
[07-29 14:34:21.697] [20416 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.697] [20416 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.697] [20416 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 15
[07-29 14:34:21.697] [20416 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.697] [20416 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.697] [20416 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.697] [20416 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.697] [20416 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.697] [20416 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.698] [20444 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 16
[07-29 14:34:21.698] [20444 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.698] [20444 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.698] [20444 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.698] [20444 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.698] [20444 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.698] [20444 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[07-29 14:34:21.698] [20444 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.698] [20444 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[07-29 14:34:21.698] [20444 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 16
[07-29 14:34:21.698] [20444 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.698] [20446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.698] [20446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.710] [20648 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 16
[07-29 14:34:21.710] [20648 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.710] [20648 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a136520 len 0x4
[07-29 14:34:21.710] [20648 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.710] [20648 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.710] [20648 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 16
[07-29 14:34:21.710] [20648 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.710] [20648 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.710] [20648 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.710] [20648 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.710] [20648 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.710] [20648 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.712] [20676 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 17
[07-29 14:34:21.712] [20676 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.712] [20676 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.712] [20676 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.712] [20676 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.712] [20676 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.712] [20676 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[07-29 14:34:21.712] [20676 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.712] [20676 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[07-29 14:34:21.712] [20676 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 17
[07-29 14:34:21.712] [20676 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.712] [20678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.712] [20678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.723] [20880 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 17
[07-29 14:34:21.723] [20880 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.723] [20880 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a138220 len 0x4
[07-29 14:34:21.723] [20880 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.723] [20880 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.723] [20880 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 17
[07-29 14:34:21.723] [20880 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.723] [20880 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.723] [20880 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.723] [20880 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.723] [20880 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.723] [20880 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.725] [20908 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 18
[07-29 14:34:21.725] [20908 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.725] [20908 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.725] [20908 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.725] [20908 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.725] [20908 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.725] [20908 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[07-29 14:34:21.725] [20908 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.725] [20908 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[07-29 14:34:21.725] [20908 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 18
[07-29 14:34:21.725] [20908 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.725] [20910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.725] [20910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.737] [21112 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 18
[07-29 14:34:21.737] [21112 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.737] [21112 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a139f20 len 0x4
[07-29 14:34:21.737] [21112 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.737] [21112 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.737] [21112 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 18
[07-29 14:34:21.737] [21112 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.737] [21112 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.737] [21112 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.737] [21112 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.737] [21112 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.737] [21112 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.738] [21140 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 19
[07-29 14:34:21.738] [21140 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.738] [21140 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.738] [21140 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.738] [21140 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.738] [21140 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.738] [21140 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[07-29 14:34:21.738] [21140 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.738] [21140 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[07-29 14:34:21.738] [21140 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 19
[07-29 14:34:21.738] [21140 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.738] [21142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.738] [21142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.750] [21344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 19
[07-29 14:34:21.750] [21344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.750] [21344 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a13bc20 len 0x4
[07-29 14:34:21.750] [21344 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.750] [21344 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.750] [21344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 19
[07-29 14:34:21.750] [21344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.750] [21344 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.750] [21344 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.750] [21344 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.750] [21344 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.750] [21344 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.752] [21372 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 20
[07-29 14:34:21.752] [21372 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.752] [21372 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.752] [21372 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.752] [21372 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.752] [21372 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.752] [21372 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x54 len 0x4
[07-29 14:34:21.752] [21372 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.752] [21372 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x54 len 0x4
[07-29 14:34:21.752] [21372 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 20
[07-29 14:34:21.752] [21372 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.752] [21374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.752] [21374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.763] [21576 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 20
[07-29 14:34:21.763] [21576 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.763] [21576 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a13d920 len 0x4
[07-29 14:34:21.764] [21576 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.764] [21576 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.764] [21576 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 20
[07-29 14:34:21.764] [21576 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.764] [21576 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.764] [21576 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.764] [21576 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.764] [21576 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.764] [21576 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.765] [21604 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 21
[07-29 14:34:21.765] [21604 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.765] [21604 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.765] [21604 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.765] [21604 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.765] [21604 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.765] [21604 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69 len 0x4
[07-29 14:34:21.765] [21604 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.765] [21604 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x69 len 0x4
[07-29 14:34:21.765] [21604 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 21
[07-29 14:34:21.765] [21604 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.765] [21606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.765] [21606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.777] [21808 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 21
[07-29 14:34:21.777] [21808 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.777] [21808 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a13f620 len 0x4
[07-29 14:34:21.777] [21808 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.777] [21808 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.777] [21808 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 21
[07-29 14:34:21.777] [21808 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.777] [21808 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.777] [21808 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.777] [21808 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.777] [21808 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.777] [21808 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.778] [21836 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 22
[07-29 14:34:21.778] [21836 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.778] [21836 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.778] [21836 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.778] [21836 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.778] [21836 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.778] [21836 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6d len 0x4
[07-29 14:34:21.778] [21836 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.779] [21836 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6d len 0x4
[07-29 14:34:21.779] [21836 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 22
[07-29 14:34:21.779] [21836 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.779] [21838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.779] [21838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.790] [22040 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 22
[07-29 14:34:21.790] [22040 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.790] [22040 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a141320 len 0x4
[07-29 14:34:21.790] [22040 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.790] [22040 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.790] [22040 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 22
[07-29 14:34:21.790] [22040 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.790] [22040 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.790] [22040 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.790] [22040 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.790] [22040 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.790] [22040 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.792] [22068 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 23
[07-29 14:34:21.792] [22068 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.792] [22068 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.792] [22068 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.792] [22068 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.792] [22068 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.792] [22068 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[07-29 14:34:21.792] [22068 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.792] [22068 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[07-29 14:34:21.792] [22068 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 23
[07-29 14:34:21.792] [22068 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.792] [22070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.792] [22070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.803] [22272 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 23
[07-29 14:34:21.803] [22272 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.803] [22272 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0374e0 len 0x4
[07-29 14:34:21.803] [22272 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.803] [22272 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.803] [22272 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 23
[07-29 14:34:21.803] [22272 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.803] [22272 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.803] [22272 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.803] [22272 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.803] [22272 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.803] [22272 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.805] [22300 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 24
[07-29 14:34:21.805] [22300 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.805] [22300 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.805] [22300 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.805] [22300 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.805] [22300 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.805] [22300 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[07-29 14:34:21.805] [22300 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.805] [22300 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[07-29 14:34:21.805] [22300 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 24
[07-29 14:34:21.805] [22300 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.805] [22302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.805] [22302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.817] [22504 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 24
[07-29 14:34:21.817] [22504 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.817] [22504 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0391e0 len 0x4
[07-29 14:34:21.817] [22504 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.817] [22504 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.817] [22504 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 24
[07-29 14:34:21.817] [22504 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.817] [22504 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.817] [22504 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.817] [22504 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.817] [22504 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.817] [22504 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.818] [22532 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 25
[07-29 14:34:21.818] [22532 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.818] [22532 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.818] [22532 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.818] [22532 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.818] [22532 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.818] [22532 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[07-29 14:34:21.818] [22532 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.818] [22532 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[07-29 14:34:21.818] [22532 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 25
[07-29 14:34:21.818] [22532 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.818] [22534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.818] [22534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.830] [22736 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 25
[07-29 14:34:21.830] [22736 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.830] [22736 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a03aee0 len 0x4
[07-29 14:34:21.830] [22736 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.830] [22736 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.830] [22736 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 25
[07-29 14:34:21.830] [22736 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.830] [22736 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.830] [22736 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.830] [22736 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.830] [22736 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.830] [22736 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.831] [22764 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 26
[07-29 14:34:21.831] [22764 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.831] [22764 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.831] [22764 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.831] [22764 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.831] [22764 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.831] [22764 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4a len 0x4
[07-29 14:34:21.831] [22764 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.831] [22764 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4a len 0x4
[07-29 14:34:21.831] [22764 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 26
[07-29 14:34:21.831] [22764 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.831] [22766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.831] [22766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.843] [22968 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 26
[07-29 14:34:21.843] [22968 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.843] [22968 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a03cbe0 len 0x4
[07-29 14:34:21.843] [22968 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.843] [22968 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.843] [22968 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 26
[07-29 14:34:21.843] [22968 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.843] [22968 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.843] [22968 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.843] [22968 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.843] [22968 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.843] [22968 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.845] [22996 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 27
[07-29 14:34:21.845] [22996 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.845] [22996 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.845] [22996 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.845] [22996 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.845] [22996 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.845] [22996 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[07-29 14:34:21.845] [22996 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.845] [22996 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[07-29 14:34:21.845] [22996 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 27
[07-29 14:34:21.845] [22996 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.845] [22998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.845] [22998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.857] [23200 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 27
[07-29 14:34:21.857] [23200 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.857] [23200 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a03e8e0 len 0x4
[07-29 14:34:21.857] [23200 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.857] [23200 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.857] [23200 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 27
[07-29 14:34:21.857] [23200 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.857] [23200 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.857] [23200 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.857] [23200 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.857] [23200 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.857] [23200 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.858] [23228 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 28
[07-29 14:34:21.858] [23228 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.858] [23228 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.858] [23228 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.858] [23228 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.858] [23228 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.858] [23228 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[07-29 14:34:21.858] [23228 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.858] [23228 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[07-29 14:34:21.858] [23228 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 28
[07-29 14:34:21.858] [23228 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.858] [23230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.858] [23230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.870] [23432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 28
[07-29 14:34:21.870] [23432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.870] [23432 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0405e0 len 0x4
[07-29 14:34:21.870] [23432 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.870] [23432 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.870] [23432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 28
[07-29 14:34:21.870] [23432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.870] [23432 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.870] [23432 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.870] [23432 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.870] [23432 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.870] [23432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.872] [23460 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 29
[07-29 14:34:21.872] [23460 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.872] [23460 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.872] [23460 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.872] [23460 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.872] [23460 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.872] [23460 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[07-29 14:34:21.872] [23460 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.872] [23460 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[07-29 14:34:21.872] [23460 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 29
[07-29 14:34:21.872] [23460 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.872] [23462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.872] [23462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.883] [23664 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 29
[07-29 14:34:21.883] [23664 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.883] [23664 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0422e0 len 0x4
[07-29 14:34:21.883] [23664 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.883] [23664 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.883] [23664 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 29
[07-29 14:34:21.883] [23664 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.883] [23664 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.883] [23664 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.883] [23664 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.883] [23664 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.883] [23664 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.885] [23692 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 30
[07-29 14:34:21.885] [23692 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.885] [23692 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.885] [23692 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.885] [23692 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.885] [23692 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.885] [23692 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[07-29 14:34:21.885] [23692 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.885] [23692 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[07-29 14:34:21.885] [23692 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 30
[07-29 14:34:21.885] [23692 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.885] [23694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.885] [23694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.897] [23896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 30
[07-29 14:34:21.897] [23896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.897] [23896 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a043fe0 len 0x4
[07-29 14:34:21.897] [23896 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.897] [23896 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.897] [23896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 30
[07-29 14:34:21.897] [23896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.897] [23896 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.897] [23896 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.897] [23896 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.897] [23896 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.897] [23896 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.898] [23924 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 31
[07-29 14:34:21.898] [23924 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.898] [23924 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.898] [23924 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.898] [23924 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.898] [23924 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.898] [23924 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x38 len 0x4
[07-29 14:34:21.898] [23924 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.898] [23924 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x38 len 0x4
[07-29 14:34:21.898] [23924 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 31
[07-29 14:34:21.898] [23924 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.899] [23926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.899] [23926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.915] [24128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 31
[07-29 14:34:21.915] [24128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.915] [24128 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a045ce0 len 0x4
[07-29 14:34:21.915] [24128 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.915] [24128 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.915] [24128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 31
[07-29 14:34:21.915] [24128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.915] [24128 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.915] [24128 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.915] [24128 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.915] [24128 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.915] [24128 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.916] [24156 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 32
[07-29 14:34:21.916] [24156 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.916] [24156 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.916] [24156 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.916] [24156 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.916] [24156 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.916] [24156 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[07-29 14:34:21.916] [24156 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.916] [24156 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[07-29 14:34:21.916] [24156 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 32
[07-29 14:34:21.916] [24156 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.917] [24158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.917] [24158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.928] [24360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 32
[07-29 14:34:21.928] [24360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.928] [24360 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0479e0 len 0x4
[07-29 14:34:21.928] [24360 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.928] [24360 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.928] [24360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 32
[07-29 14:34:21.928] [24360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.928] [24360 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.928] [24360 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.928] [24360 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.928] [24360 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.928] [24360 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.930] [24388 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 33
[07-29 14:34:21.930] [24388 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.930] [24388 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.930] [24388 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.930] [24388 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.930] [24388 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.930] [24388 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[07-29 14:34:21.930] [24388 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.930] [24388 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[07-29 14:34:21.930] [24388 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 33
[07-29 14:34:21.930] [24388 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.930] [24390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.930] [24390 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.942] [24592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 33
[07-29 14:34:21.942] [24592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.942] [24592 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0496e0 len 0x4
[07-29 14:34:21.942] [24592 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.942] [24592 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.942] [24592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 33
[07-29 14:34:21.942] [24592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.942] [24592 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.942] [24592 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.942] [24592 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.942] [24592 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.942] [24592 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.943] [24620 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 34
[07-29 14:34:21.943] [24620 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.943] [24620 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.943] [24620 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.943] [24620 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.943] [24620 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.943] [24620 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x30 len 0x4
[07-29 14:34:21.943] [24620 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.943] [24620 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x30 len 0x4
[07-29 14:34:21.943] [24620 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 34
[07-29 14:34:21.943] [24620 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.944] [24622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.944] [24622 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.955] [24824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 34
[07-29 14:34:21.955] [24824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.955] [24824 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a04b3e0 len 0x4
[07-29 14:34:21.955] [24824 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.955] [24824 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.955] [24824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 34
[07-29 14:34:21.955] [24824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.955] [24824 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.955] [24824 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.955] [24824 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.955] [24824 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.955] [24824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.957] [24852 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 35
[07-29 14:34:21.957] [24852 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.957] [24852 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.957] [24852 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.957] [24852 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.957] [24852 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.957] [24852 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[07-29 14:34:21.957] [24852 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.957] [24852 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[07-29 14:34:21.957] [24852 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 35
[07-29 14:34:21.957] [24852 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.957] [24854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.957] [24854 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.969] [25056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 35
[07-29 14:34:21.969] [25056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.969] [25056 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a04d0e0 len 0x4
[07-29 14:34:21.969] [25056 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.969] [25056 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.969] [25056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 35
[07-29 14:34:21.969] [25056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.969] [25056 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.969] [25056 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.969] [25056 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.969] [25056 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.969] [25056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.970] [25084 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 36
[07-29 14:34:21.970] [25084 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.970] [25084 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.970] [25084 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.970] [25084 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.970] [25084 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.970] [25084 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x35 len 0x4
[07-29 14:34:21.970] [25084 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.970] [25084 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x35 len 0x4
[07-29 14:34:21.970] [25084 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 36
[07-29 14:34:21.970] [25084 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.970] [25086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.970] [25086 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.982] [25288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 36
[07-29 14:34:21.982] [25288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.982] [25288 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a04ede0 len 0x4
[07-29 14:34:21.982] [25288 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.982] [25288 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.982] [25288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 36
[07-29 14:34:21.982] [25288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.982] [25288 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.982] [25288 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.982] [25288 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.982] [25288 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.982] [25288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.983] [25316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 37
[07-29 14:34:21.983] [25316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.983] [25316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.983] [25316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.983] [25316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.983] [25316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.983] [25316 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x2c len 0x4
[07-29 14:34:21.983] [25316 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.983] [25316 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x2c len 0x4
[07-29 14:34:21.983] [25316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 37
[07-29 14:34:21.983] [25316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.984] [25318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.984] [25318 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:21.995] [25520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 37
[07-29 14:34:21.995] [25520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:21.995] [25520 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a050ae0 len 0x4
[07-29 14:34:21.995] [25520 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.995] [25520 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:21.995] [25520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 37
[07-29 14:34:21.995] [25520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:21.995] [25520 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.995] [25520 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.995] [25520 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.995] [25520 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:21.995] [25520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:21.997] [25548 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 38
[07-29 14:34:21.997] [25548 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:21.997] [25548 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:21.997] [25548 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:21.997] [25548 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:21.997] [25548 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:21.997] [25548 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[07-29 14:34:21.997] [25548 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:21.997] [25548 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[07-29 14:34:21.997] [25548 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 38
[07-29 14:34:21.997] [25548 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:21.997] [25550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:21.997] [25550 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.008] [25752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 38
[07-29 14:34:22.008] [25752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.008] [25752 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0527e0 len 0x4
[07-29 14:34:22.008] [25752 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.008] [25752 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.008] [25752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 38
[07-29 14:34:22.008] [25752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.008] [25752 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.008] [25752 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.008] [25752 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.008] [25752 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.008] [25752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.010] [25780 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 39
[07-29 14:34:22.010] [25780 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.010] [25780 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.010] [25780 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.010] [25780 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.010] [25780 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.010] [25780 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[07-29 14:34:22.010] [25780 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.010] [25780 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[07-29 14:34:22.010] [25780 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 39
[07-29 14:34:22.010] [25780 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.010] [25782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.010] [25782 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.022] [25984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 39
[07-29 14:34:22.022] [25984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.022] [25984 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0544e0 len 0x4
[07-29 14:34:22.022] [25984 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.022] [25984 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.022] [25984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 39
[07-29 14:34:22.022] [25984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.022] [25984 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.022] [25984 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.022] [25984 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.022] [25984 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.022] [25984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.023] [26012 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 40
[07-29 14:34:22.023] [26012 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.023] [26012 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.023] [26012 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.023] [26012 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.023] [26012 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.023] [26012 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x35 len 0x4
[07-29 14:34:22.023] [26012 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.023] [26012 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x35 len 0x4
[07-29 14:34:22.023] [26012 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 40
[07-29 14:34:22.023] [26012 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.023] [26014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.023] [26014 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.035] [26216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 40
[07-29 14:34:22.035] [26216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.035] [26216 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0561e0 len 0x4
[07-29 14:34:22.035] [26216 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.035] [26216 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.035] [26216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 40
[07-29 14:34:22.035] [26216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.035] [26216 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.035] [26216 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.035] [26216 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.035] [26216 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.035] [26216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.036] [26244 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 41
[07-29 14:34:22.036] [26244 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.036] [26244 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.036] [26244 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.036] [26244 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.036] [26244 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.036] [26244 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[07-29 14:34:22.036] [26244 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.036] [26244 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[07-29 14:34:22.036] [26244 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 41
[07-29 14:34:22.036] [26244 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.036] [26246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.036] [26246 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.048] [26448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 41
[07-29 14:34:22.048] [26448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.048] [26448 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a057ee0 len 0x4
[07-29 14:34:22.048] [26448 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.048] [26448 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.048] [26448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 41
[07-29 14:34:22.048] [26448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.048] [26448 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.048] [26448 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.048] [26448 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.048] [26448 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.048] [26448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.050] [26476 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 42
[07-29 14:34:22.050] [26476 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.050] [26476 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.050] [26476 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.050] [26476 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.050] [26476 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.050] [26476 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x35 len 0x4
[07-29 14:34:22.050] [26476 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.050] [26476 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x35 len 0x4
[07-29 14:34:22.050] [26476 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 42
[07-29 14:34:22.050] [26476 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.050] [26478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.050] [26478 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.061] [26680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 42
[07-29 14:34:22.061] [26680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.061] [26680 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a059be0 len 0x4
[07-29 14:34:22.061] [26680 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.061] [26680 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.061] [26680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 42
[07-29 14:34:22.061] [26680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.061] [26680 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.061] [26680 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.061] [26680 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.061] [26680 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.061] [26680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.063] [26708 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 43
[07-29 14:34:22.063] [26708 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.063] [26708 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.063] [26708 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.063] [26708 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.063] [26708 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.063] [26708 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x35 len 0x4
[07-29 14:34:22.063] [26708 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.063] [26708 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x35 len 0x4
[07-29 14:34:22.063] [26708 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 43
[07-29 14:34:22.063] [26708 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.063] [26710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.063] [26710 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.075] [26912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 43
[07-29 14:34:22.075] [26912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.075] [26912 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a05b8e0 len 0x4
[07-29 14:34:22.075] [26912 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.075] [26912 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.075] [26912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 43
[07-29 14:34:22.075] [26912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.075] [26912 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.075] [26912 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.075] [26912 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.075] [26912 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.075] [26912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.076] [26940 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 44
[07-29 14:34:22.076] [26940 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.076] [26940 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.076] [26940 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.076] [26940 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.076] [26940 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.076] [26940 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[07-29 14:34:22.076] [26940 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.076] [26940 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[07-29 14:34:22.076] [26940 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 44
[07-29 14:34:22.076] [26940 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.076] [26942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.076] [26942 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.088] [27144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 44
[07-29 14:34:22.088] [27144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.088] [27144 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a05d5e0 len 0x4
[07-29 14:34:22.088] [27144 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.088] [27144 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.088] [27144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 44
[07-29 14:34:22.088] [27144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.088] [27144 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.088] [27144 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.088] [27144 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.088] [27144 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.088] [27144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.089] [27172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 45
[07-29 14:34:22.089] [27172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.089] [27172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.089] [27172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.089] [27172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.089] [27172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.089] [27172 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x34 len 0x4
[07-29 14:34:22.089] [27172 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.090] [27172 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x34 len 0x4
[07-29 14:34:22.090] [27172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 45
[07-29 14:34:22.090] [27172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.090] [27174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.090] [27174 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.114] [27376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 45
[07-29 14:34:22.114] [27376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.114] [27376 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a15bce0 len 0x4
[07-29 14:34:22.114] [27376 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.114] [27376 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.114] [27376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 45
[07-29 14:34:22.114] [27376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.114] [27376 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.114] [27376 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.114] [27376 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.114] [27376 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.114] [27376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.116] [27404 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 46
[07-29 14:34:22.116] [27404 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.116] [27404 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.116] [27404 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.116] [27404 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.116] [27404 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.116] [27404 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x30 len 0x4
[07-29 14:34:22.116] [27404 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.116] [27404 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x30 len 0x4
[07-29 14:34:22.116] [27404 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 46
[07-29 14:34:22.116] [27404 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.116] [27406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.116] [27406 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.128] [27608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 46
[07-29 14:34:22.128] [27608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.128] [27608 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0c6680 len 0x4
[07-29 14:34:22.128] [27608 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.128] [27608 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.128] [27608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 46
[07-29 14:34:22.128] [27608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.128] [27608 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.128] [27608 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.128] [27608 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.128] [27608 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.128] [27608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.130] [27636 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 47
[07-29 14:34:22.130] [27636 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.130] [27636 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.130] [27636 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.130] [27636 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.130] [27636 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.130] [27636 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[07-29 14:34:22.130] [27636 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.130] [27636 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[07-29 14:34:22.130] [27636 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 47
[07-29 14:34:22.130] [27636 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.130] [27638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.130] [27638 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.140] [27814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 47
[07-29 14:34:22.140] [27814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.140] [27814 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a05dbc0 len 0x4
[07-29 14:34:22.140] [27814 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.140] [27814 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.140] [27814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 47
[07-29 14:34:22.140] [27814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.140] [27814 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.140] [27814 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.140] [27814 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.140] [27814 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.140] [27814 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.142] [27842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:22.142] [27842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.142] [27842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.142] [27842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.142] [27842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 48
[07-29 14:34:22.142] [27842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:22.142] [27842 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[07-29 14:34:22.142] [27842 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.142] [27842 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[07-29 14:34:22.142] [27842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 48
[07-29 14:34:22.142] [27842 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.142] [27844 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.142] [27844 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:22.146] [27920 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 48
[07-29 14:34:22.146] [27920 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.146] [27920 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a15a3e0 len 0x4
[07-29 14:34:22.146] [27920 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.146] [27920 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.146] [27920 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 48
[07-29 14:34:22.146] [27920 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.146] [27920 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.146] [27920 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.146] [27920 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.146] [27920 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.146] [27920 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.148] [27948 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 49
[07-29 14:34:22.148] [27948 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:22.148] [27948 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.148] [27948 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.148] [27948 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.148] [27948 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:22.148] [27948 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[07-29 14:34:22.148] [27948 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.148] [27948 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[07-29 14:34:22.148] [27948 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 49
[07-29 14:34:22.148] [27948 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.148] [27950 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.148] [27950 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:22.450] [33198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 49
[07-29 14:34:22.450] [33198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.450] [33198 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69fe2b90 len 0x4
[07-29 14:34:22.450] [33198 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.450] [33198 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.450] [33198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 49
[07-29 14:34:22.450] [33198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.450] [33198 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.450] [33198 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.450] [33198 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.450] [33198 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.450] [33198 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.452] [33226 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.452] [33226 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.452] [33226 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.452] [33226 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.452] [33226 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 50
[07-29 14:34:22.452] [33226 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.452] [33226 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x44 len 0x4
[07-29 14:34:22.452] [33226 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.452] [33226 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x44 len 0x4
[07-29 14:34:22.452] [33226 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 50
[07-29 14:34:22.452] [33226 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.452] [33228 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.452] [33228 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.464] [33430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 50
[07-29 14:34:22.464] [33430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.464] [33430 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69fe4890 len 0x4
[07-29 14:34:22.464] [33430 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.464] [33430 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.464] [33430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 50
[07-29 14:34:22.464] [33430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.464] [33430 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.464] [33430 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.464] [33430 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.464] [33430 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.464] [33430 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.465] [33458 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:22.465] [33458 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.465] [33458 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.465] [33458 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.465] [33458 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 51
[07-29 14:34:22.465] [33458 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:22.465] [33458 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6f len 0x4
[07-29 14:34:22.465] [33458 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.465] [33458 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6f len 0x4
[07-29 14:34:22.465] [33458 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 51
[07-29 14:34:22.465] [33458 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.466] [33460 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.466] [33460 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:22.477] [33662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 51
[07-29 14:34:22.477] [33662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.477] [33662 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69fe6590 len 0x4
[07-29 14:34:22.477] [33662 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.477] [33662 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.477] [33662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 51
[07-29 14:34:22.477] [33662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.477] [33662 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.477] [33662 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.477] [33662 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.477] [33662 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.477] [33662 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.479] [33690 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.479] [33690 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.479] [33690 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.479] [33690 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.479] [33690 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 52
[07-29 14:34:22.479] [33690 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.479] [33690 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x77 len 0x4
[07-29 14:34:22.479] [33690 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.479] [33690 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x77 len 0x4
[07-29 14:34:22.479] [33690 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 52
[07-29 14:34:22.479] [33690 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.479] [33692 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.479] [33692 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.490] [33894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 52
[07-29 14:34:22.490] [33894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.490] [33894 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69fe8290 len 0x4
[07-29 14:34:22.490] [33894 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.490] [33894 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.490] [33894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 52
[07-29 14:34:22.490] [33894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.490] [33894 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.490] [33894 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.490] [33894 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.490] [33894 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.490] [33894 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.492] [33922 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:22.492] [33922 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.492] [33922 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.492] [33922 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.492] [33922 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 53
[07-29 14:34:22.492] [33922 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:22.492] [33922 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6e len 0x4
[07-29 14:34:22.492] [33922 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.492] [33922 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6e len 0x4
[07-29 14:34:22.492] [33922 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 53
[07-29 14:34:22.492] [33922 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.492] [33924 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.492] [33924 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:22.504] [34126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 53
[07-29 14:34:22.504] [34126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.504] [34126 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69fe9f90 len 0x4
[07-29 14:34:22.504] [34126 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.504] [34126 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.504] [34126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 53
[07-29 14:34:22.504] [34126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.504] [34126 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.504] [34126 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.504] [34126 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.504] [34126 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.504] [34126 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.505] [34154 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.505] [34154 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.505] [34154 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.505] [34154 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.505] [34154 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 54
[07-29 14:34:22.505] [34154 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.505] [34154 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6c len 0x4
[07-29 14:34:22.505] [34154 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.505] [34154 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6c len 0x4
[07-29 14:34:22.505] [34154 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 54
[07-29 14:34:22.505] [34154 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.505] [34156 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.505] [34156 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.517] [34358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 54
[07-29 14:34:22.517] [34358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.517] [34358 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69febc90 len 0x4
[07-29 14:34:22.517] [34358 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.517] [34358 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.517] [34358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 54
[07-29 14:34:22.517] [34358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.517] [34358 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.517] [34358 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.517] [34358 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.517] [34358 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.517] [34358 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.518] [34386 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:22.518] [34386 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.518] [34386 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.518] [34386 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.518] [34386 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 55
[07-29 14:34:22.518] [34386 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:22.518] [34386 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6f len 0x4
[07-29 14:34:22.518] [34386 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.518] [34386 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6f len 0x4
[07-29 14:34:22.518] [34386 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 55
[07-29 14:34:22.518] [34386 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.519] [34388 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.519] [34388 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:22.530] [34590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 55
[07-29 14:34:22.530] [34590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.530] [34590 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69fed990 len 0x4
[07-29 14:34:22.530] [34590 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.530] [34590 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.530] [34590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 55
[07-29 14:34:22.530] [34590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.530] [34590 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.530] [34590 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.530] [34590 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.530] [34590 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.530] [34590 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.532] [34618 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.532] [34618 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.532] [34618 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.532] [34618 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.532] [34618 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 56
[07-29 14:34:22.532] [34618 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.532] [34618 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x61 len 0x4
[07-29 14:34:22.532] [34618 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.532] [34618 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x61 len 0x4
[07-29 14:34:22.532] [34618 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 56
[07-29 14:34:22.532] [34618 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.532] [34620 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.532] [34620 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.543] [34822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 56
[07-29 14:34:22.543] [34822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.543] [34822 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a05eda0 len 0x4
[07-29 14:34:22.543] [34822 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.543] [34822 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.543] [34822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 56
[07-29 14:34:22.543] [34822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.543] [34822 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.543] [34822 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.543] [34822 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.543] [34822 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.543] [34822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.545] [34850 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:22.545] [34850 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.545] [34850 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.545] [34850 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.545] [34850 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 57
[07-29 14:34:22.545] [34850 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:22.545] [34850 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[07-29 14:34:22.545] [34850 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.545] [34850 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[07-29 14:34:22.545] [34850 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 57
[07-29 14:34:22.545] [34850 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.545] [34852 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.545] [34852 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:22.557] [35054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 57
[07-29 14:34:22.557] [35054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.557] [35054 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a060aa0 len 0x4
[07-29 14:34:22.557] [35054 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.557] [35054 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.557] [35054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 57
[07-29 14:34:22.557] [35054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.557] [35054 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.557] [35054 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.557] [35054 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.557] [35054 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.557] [35054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.558] [35082 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.558] [35082 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.558] [35082 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.558] [35082 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.558] [35082 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 58
[07-29 14:34:22.558] [35082 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.558] [35082 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[07-29 14:34:22.558] [35082 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.558] [35082 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[07-29 14:34:22.558] [35082 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 58
[07-29 14:34:22.558] [35082 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.558] [35084 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.558] [35084 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.570] [35286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 58
[07-29 14:34:22.570] [35286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.570] [35286 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0627a0 len 0x4
[07-29 14:34:22.570] [35286 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.570] [35286 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.570] [35286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 58
[07-29 14:34:22.570] [35286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.570] [35286 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.570] [35286 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.570] [35286 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.570] [35286 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.570] [35286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.571] [35314 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:22.571] [35314 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.571] [35314 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.571] [35314 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.571] [35314 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 59
[07-29 14:34:22.571] [35314 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:22.571] [35314 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4d len 0x4
[07-29 14:34:22.571] [35314 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.571] [35314 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4d len 0x4
[07-29 14:34:22.571] [35314 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 59
[07-29 14:34:22.571] [35314 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.572] [35316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.572] [35316 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:22.583] [35518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 59
[07-29 14:34:22.583] [35518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.583] [35518 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a064480 len 0x4
[07-29 14:34:22.583] [35518 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.583] [35518 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.583] [35518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 59
[07-29 14:34:22.583] [35518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.583] [35518 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.583] [35518 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.583] [35518 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.583] [35518 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.583] [35518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.585] [35546 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.585] [35546 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.585] [35546 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.585] [35546 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.585] [35546 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 60
[07-29 14:34:22.585] [35546 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.585] [35546 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6f len 0x4
[07-29 14:34:22.585] [35546 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.585] [35546 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6f len 0x4
[07-29 14:34:22.585] [35546 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 60
[07-29 14:34:22.585] [35546 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.585] [35548 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.585] [35548 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.596] [35750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 60
[07-29 14:34:22.596] [35750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.596] [35750 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a066180 len 0x4
[07-29 14:34:22.596] [35750 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.596] [35750 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.596] [35750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 60
[07-29 14:34:22.596] [35750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.596] [35750 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.596] [35750 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.596] [35750 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.596] [35750 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.596] [35750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.598] [35778 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:22.598] [35778 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.598] [35778 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.598] [35778 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.598] [35778 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 61
[07-29 14:34:22.598] [35778 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:22.598] [35778 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x64 len 0x4
[07-29 14:34:22.598] [35778 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.598] [35778 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x64 len 0x4
[07-29 14:34:22.598] [35778 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 61
[07-29 14:34:22.598] [35778 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.598] [35780 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.598] [35780 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:22.610] [35982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 61
[07-29 14:34:22.610] [35982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.610] [35982 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a067e80 len 0x4
[07-29 14:34:22.610] [35982 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.610] [35982 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.610] [35982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 61
[07-29 14:34:22.610] [35982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.610] [35982 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.610] [35982 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.610] [35982 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.610] [35982 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.610] [35982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.611] [36010 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.611] [36010 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.611] [36010 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.611] [36010 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.611] [36010 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 62
[07-29 14:34:22.611] [36010 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.611] [36010 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[07-29 14:34:22.611] [36010 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.611] [36010 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[07-29 14:34:22.611] [36010 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 62
[07-29 14:34:22.611] [36010 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.611] [36012 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.611] [36012 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.623] [36214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 62
[07-29 14:34:22.623] [36214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.623] [36214 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a069b80 len 0x4
[07-29 14:34:22.623] [36214 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.623] [36214 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.623] [36214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 62
[07-29 14:34:22.623] [36214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.623] [36214 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.623] [36214 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.623] [36214 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.623] [36214 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.623] [36214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.624] [36242 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:22.624] [36242 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.624] [36242 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.624] [36242 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.624] [36242 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 63
[07-29 14:34:22.624] [36242 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:22.624] [36242 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[07-29 14:34:22.624] [36242 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.624] [36242 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[07-29 14:34:22.624] [36242 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 63
[07-29 14:34:22.624] [36242 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.625] [36244 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.625] [36244 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:22.636] [36446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 63
[07-29 14:34:22.636] [36446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.636] [36446 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a06b880 len 0x4
[07-29 14:34:22.636] [36446 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.636] [36446 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.636] [36446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 63
[07-29 14:34:22.636] [36446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.636] [36446 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.636] [36446 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.636] [36446 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.636] [36446 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.636] [36446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.638] [36474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.638] [36474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.638] [36474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.638] [36474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.638] [36474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 64
[07-29 14:34:22.638] [36474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.638] [36474 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[07-29 14:34:22.638] [36474 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.638] [36474 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[07-29 14:34:22.638] [36474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 64
[07-29 14:34:22.638] [36474 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.638] [36476 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.638] [36476 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.650] [36678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 64
[07-29 14:34:22.650] [36678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.650] [36678 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69fefc20 len 0x4
[07-29 14:34:22.650] [36678 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.650] [36678 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.650] [36678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 64
[07-29 14:34:22.650] [36678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.650] [36678 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.650] [36678 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.650] [36678 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.650] [36678 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.650] [36678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.651] [36706 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:22.651] [36706 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.651] [36706 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.651] [36706 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.651] [36706 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 65
[07-29 14:34:22.651] [36706 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:22.651] [36706 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49 len 0x4
[07-29 14:34:22.651] [36706 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.651] [36706 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x49 len 0x4
[07-29 14:34:22.651] [36706 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 65
[07-29 14:34:22.651] [36706 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.651] [36708 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.651] [36708 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:22.663] [36910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 65
[07-29 14:34:22.663] [36910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.663] [36910 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69ff1920 len 0x4
[07-29 14:34:22.663] [36910 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.663] [36910 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.663] [36910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 65
[07-29 14:34:22.663] [36910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.663] [36910 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.663] [36910 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.663] [36910 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.663] [36910 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.663] [36910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.665] [36938 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.665] [36938 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.665] [36938 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.665] [36938 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.665] [36938 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 66
[07-29 14:34:22.665] [36938 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.665] [36938 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4c len 0x4
[07-29 14:34:22.665] [36938 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.665] [36938 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4c len 0x4
[07-29 14:34:22.665] [36938 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 66
[07-29 14:34:22.665] [36938 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.665] [36940 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.665] [36940 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.677] [37142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 66
[07-29 14:34:22.677] [37142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.677] [37142 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69ff3620 len 0x4
[07-29 14:34:22.677] [37142 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.677] [37142 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.677] [37142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 66
[07-29 14:34:22.677] [37142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.677] [37142 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.677] [37142 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.677] [37142 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.677] [37142 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.677] [37142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.678] [37170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:22.678] [37170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.678] [37170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.678] [37170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.678] [37170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 67
[07-29 14:34:22.678] [37170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:22.678] [37170 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x4d len 0x4
[07-29 14:34:22.678] [37170 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.678] [37170 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x4d len 0x4
[07-29 14:34:22.678] [37170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 67
[07-29 14:34:22.678] [37170 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.678] [37172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.678] [37172 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:22.690] [37374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 67
[07-29 14:34:22.690] [37374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.690] [37374 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69ff5320 len 0x4
[07-29 14:34:22.690] [37374 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.690] [37374 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.690] [37374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 67
[07-29 14:34:22.690] [37374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.690] [37374 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.690] [37374 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.690] [37374 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.690] [37374 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.690] [37374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.691] [37402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.691] [37402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.691] [37402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.691] [37402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.691] [37402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 68
[07-29 14:34:22.691] [37402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.691] [37402 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[07-29 14:34:22.691] [37402 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.691] [37402 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[07-29 14:34:22.691] [37402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 68
[07-29 14:34:22.691] [37402 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.692] [37404 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.692] [37404 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.702] [37580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 68
[07-29 14:34:22.702] [37580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.702] [37580 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69ff6ce0 len 0x4
[07-29 14:34:22.702] [37580 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.702] [37580 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.702] [37580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 68
[07-29 14:34:22.702] [37580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.702] [37580 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.702] [37580 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.702] [37580 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.702] [37580 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.702] [37580 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.703] [37608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 69
[07-29 14:34:22.703] [37608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:22.703] [37608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.703] [37608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.703] [37608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.703] [37608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:22.703] [37608 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[07-29 14:34:22.703] [37608 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.703] [37608 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[07-29 14:34:22.703] [37608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 69
[07-29 14:34:22.703] [37608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.703] [37610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.703] [37610 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:22.708] [37686 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 69
[07-29 14:34:22.708] [37686 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:22.708] [37686 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69ff7a20 len 0x4
[07-29 14:34:22.708] [37686 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.708] [37686 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:22.708] [37686 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 69
[07-29 14:34:22.708] [37686 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:22.708] [37686 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.708] [37686 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.708] [37686 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.708] [37686 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:22.708] [37686 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:22.709] [37714 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:22.709] [37714 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:22.709] [37714 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:22.709] [37714 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:22.709] [37714 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 70
[07-29 14:34:22.709] [37714 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:22.709] [37714 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[07-29 14:34:22.709] [37714 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:22.709] [37714 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[07-29 14:34:22.709] [37714 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 70
[07-29 14:34:22.709] [37714 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:22.710] [37716 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:22.710] [37716 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:23.061] [43794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 70
[07-29 14:34:23.061] [43794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.061] [43794 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a015bb0 len 0x4
[07-29 14:34:23.061] [43794 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.061] [43794 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.061] [43794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 70
[07-29 14:34:23.061] [43794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.061] [43794 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.061] [43794 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.061] [43794 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.061] [43794 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.061] [43794 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.063] [43822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.063] [43822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.063] [43822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.063] [43822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.063] [43822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 71
[07-29 14:34:23.063] [43822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.063] [43822 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x43 len 0x4
[07-29 14:34:23.063] [43822 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.063] [43822 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x43 len 0x4
[07-29 14:34:23.063] [43822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 71
[07-29 14:34:23.063] [43822 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.063] [43824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.063] [43824 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.075] [44026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 71
[07-29 14:34:23.075] [44026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.075] [44026 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0178b0 len 0x4
[07-29 14:34:23.075] [44026 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.075] [44026 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.075] [44026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 71
[07-29 14:34:23.075] [44026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.075] [44026 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.075] [44026 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.075] [44026 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.075] [44026 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.075] [44026 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.077] [44054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:23.077] [44054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.077] [44054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.077] [44054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.077] [44054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 72
[07-29 14:34:23.077] [44054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:23.077] [44054 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x50 len 0x4
[07-29 14:34:23.077] [44054 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.077] [44054 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x50 len 0x4
[07-29 14:34:23.077] [44054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 72
[07-29 14:34:23.077] [44054 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.077] [44056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.077] [44056 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:23.088] [44258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 72
[07-29 14:34:23.088] [44258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.088] [44258 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0195b0 len 0x4
[07-29 14:34:23.088] [44258 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.088] [44258 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.088] [44258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 72
[07-29 14:34:23.088] [44258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.088] [44258 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.088] [44258 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.088] [44258 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.088] [44258 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.088] [44258 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.090] [44286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.090] [44286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.090] [44286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.090] [44286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.090] [44286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 73
[07-29 14:34:23.090] [44286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.090] [44286 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x55 len 0x4
[07-29 14:34:23.090] [44286 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.090] [44286 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x55 len 0x4
[07-29 14:34:23.090] [44286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 73
[07-29 14:34:23.090] [44286 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.090] [44288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.090] [44288 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.101] [44490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 73
[07-29 14:34:23.101] [44490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.101] [44490 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a01b2b0 len 0x4
[07-29 14:34:23.101] [44490 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.101] [44490 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.101] [44490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 73
[07-29 14:34:23.101] [44490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.101] [44490 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.101] [44490 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.101] [44490 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.101] [44490 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.101] [44490 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.103] [44518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:23.103] [44518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.103] [44518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.103] [44518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.103] [44518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 74
[07-29 14:34:23.103] [44518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:23.103] [44518 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[07-29 14:34:23.103] [44518 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.103] [44518 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[07-29 14:34:23.103] [44518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 74
[07-29 14:34:23.103] [44518 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.103] [44520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.103] [44520 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:23.115] [44722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 74
[07-29 14:34:23.115] [44722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.115] [44722 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a01cfb0 len 0x4
[07-29 14:34:23.115] [44722 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.115] [44722 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.115] [44722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 74
[07-29 14:34:23.115] [44722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.115] [44722 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.115] [44722 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.115] [44722 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.115] [44722 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.115] [44722 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.116] [44750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.116] [44750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.116] [44750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.116] [44750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.116] [44750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 75
[07-29 14:34:23.116] [44750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.116] [44750 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x46 len 0x4
[07-29 14:34:23.116] [44750 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.116] [44750 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x46 len 0x4
[07-29 14:34:23.116] [44750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 75
[07-29 14:34:23.116] [44750 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.117] [44752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.117] [44752 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.128] [44954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 75
[07-29 14:34:23.128] [44954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.128] [44954 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a02c0c0 len 0x4
[07-29 14:34:23.128] [44954 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.128] [44954 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.128] [44954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 75
[07-29 14:34:23.128] [44954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.128] [44954 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.128] [44954 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.128] [44954 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.128] [44954 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.128] [44954 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.130] [44982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:23.130] [44982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.130] [44982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.130] [44982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.130] [44982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 76
[07-29 14:34:23.130] [44982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:23.130] [44982 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x72 len 0x4
[07-29 14:34:23.130] [44982 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.130] [44982 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x72 len 0x4
[07-29 14:34:23.130] [44982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 76
[07-29 14:34:23.130] [44982 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.130] [44984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.130] [44984 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:23.141] [45186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 76
[07-29 14:34:23.141] [45186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.141] [45186 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a02ddc0 len 0x4
[07-29 14:34:23.141] [45186 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.141] [45186 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.141] [45186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 76
[07-29 14:34:23.141] [45186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.141] [45186 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.141] [45186 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.141] [45186 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.141] [45186 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.141] [45186 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.143] [45214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.143] [45214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.143] [45214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.143] [45214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.143] [45214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 77
[07-29 14:34:23.143] [45214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.143] [45214 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[07-29 14:34:23.143] [45214 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.143] [45214 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[07-29 14:34:23.143] [45214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 77
[07-29 14:34:23.143] [45214 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.143] [45216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.143] [45216 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.155] [45418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 77
[07-29 14:34:23.155] [45418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.155] [45418 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a02fac0 len 0x4
[07-29 14:34:23.155] [45418 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.155] [45418 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.155] [45418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 77
[07-29 14:34:23.155] [45418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.155] [45418 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.155] [45418 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.155] [45418 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.155] [45418 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.155] [45418 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.156] [45446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:23.156] [45446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.156] [45446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.156] [45446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.156] [45446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 78
[07-29 14:34:23.156] [45446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:23.156] [45446 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x71 len 0x4
[07-29 14:34:23.156] [45446 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.156] [45446 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x71 len 0x4
[07-29 14:34:23.156] [45446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 78
[07-29 14:34:23.156] [45446 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.156] [45448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.156] [45448 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:23.168] [45650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 78
[07-29 14:34:23.168] [45650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.168] [45650 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0317c0 len 0x4
[07-29 14:34:23.168] [45650 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.168] [45650 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.168] [45650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 78
[07-29 14:34:23.168] [45650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.168] [45650 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.168] [45650 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.168] [45650 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.168] [45650 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.168] [45650 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.169] [45678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.169] [45678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.169] [45678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.169] [45678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.169] [45678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 79
[07-29 14:34:23.169] [45678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.169] [45678 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x75 len 0x4
[07-29 14:34:23.169] [45678 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.169] [45678 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x75 len 0x4
[07-29 14:34:23.169] [45678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 79
[07-29 14:34:23.169] [45678 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.170] [45680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.170] [45680 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.181] [45882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 79
[07-29 14:34:23.181] [45882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.181] [45882 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0334c0 len 0x4
[07-29 14:34:23.181] [45882 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.181] [45882 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.181] [45882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 79
[07-29 14:34:23.181] [45882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.181] [45882 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.181] [45882 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.181] [45882 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.181] [45882 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.181] [45882 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.183] [45910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:23.183] [45910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.183] [45910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.183] [45910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.183] [45910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 80
[07-29 14:34:23.183] [45910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:23.183] [45910 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x65 len 0x4
[07-29 14:34:23.183] [45910 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.183] [45910 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x65 len 0x4
[07-29 14:34:23.183] [45910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 80
[07-29 14:34:23.183] [45910 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.183] [45912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.183] [45912 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:23.194] [46114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 80
[07-29 14:34:23.194] [46114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.194] [46114 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0351c0 len 0x4
[07-29 14:34:23.194] [46114 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.194] [46114 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.194] [46114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 80
[07-29 14:34:23.194] [46114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.194] [46114 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.194] [46114 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.194] [46114 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.194] [46114 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.194] [46114 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.196] [46142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.196] [46142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.196] [46142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.196] [46142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.196] [46142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 81
[07-29 14:34:23.196] [46142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.196] [46142 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6e len 0x4
[07-29 14:34:23.196] [46142 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.196] [46142 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x6e len 0x4
[07-29 14:34:23.196] [46142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 81
[07-29 14:34:23.196] [46142 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.196] [46144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.196] [46144 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.208] [46346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 81
[07-29 14:34:23.208] [46346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.208] [46346 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a036ec0 len 0x4
[07-29 14:34:23.208] [46346 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.208] [46346 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.208] [46346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 81
[07-29 14:34:23.208] [46346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.208] [46346 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.208] [46346 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.208] [46346 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.208] [46346 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.208] [46346 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.209] [46374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:23.209] [46374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.209] [46374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.209] [46374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.209] [46374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 82
[07-29 14:34:23.209] [46374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:23.209] [46374 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x63 len 0x4
[07-29 14:34:23.209] [46374 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.209] [46374 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x63 len 0x4
[07-29 14:34:23.209] [46374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 82
[07-29 14:34:23.209] [46374 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.209] [46376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.209] [46376 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:23.221] [46578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 82
[07-29 14:34:23.221] [46578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.221] [46578 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a038bc0 len 0x4
[07-29 14:34:23.221] [46578 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.221] [46578 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.221] [46578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 82
[07-29 14:34:23.221] [46578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.221] [46578 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.221] [46578 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.221] [46578 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.221] [46578 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.221] [46578 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.223] [46606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.223] [46606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.223] [46606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.223] [46606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.223] [46606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 83
[07-29 14:34:23.223] [46606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.223] [46606 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x79 len 0x4
[07-29 14:34:23.223] [46606 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.223] [46606 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x79 len 0x4
[07-29 14:34:23.223] [46606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 83
[07-29 14:34:23.223] [46606 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.223] [46608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.223] [46608 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.234] [46810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 83
[07-29 14:34:23.234] [46810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.234] [46810 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a03a8c0 len 0x4
[07-29 14:34:23.234] [46810 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.234] [46810 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.234] [46810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 83
[07-29 14:34:23.234] [46810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.234] [46810 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.234] [46810 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.234] [46810 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.234] [46810 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.234] [46810 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.236] [46838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:23.236] [46838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.236] [46838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.236] [46838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.236] [46838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 84
[07-29 14:34:23.236] [46838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:23.236] [46838 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[07-29 14:34:23.236] [46838 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.236] [46838 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[07-29 14:34:23.236] [46838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 84
[07-29 14:34:23.236] [46838 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.236] [46840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.236] [46840 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:23.248] [47042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 84
[07-29 14:34:23.248] [47042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.248] [47042 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a03c5c0 len 0x4
[07-29 14:34:23.248] [47042 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.248] [47042 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.248] [47042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 84
[07-29 14:34:23.248] [47042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.248] [47042 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.248] [47042 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.248] [47042 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.248] [47042 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.248] [47042 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.250] [47070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.250] [47070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.250] [47070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.250] [47070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.250] [47070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 85
[07-29 14:34:23.250] [47070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.250] [47070 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[07-29 14:34:23.250] [47070 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.250] [47070 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[07-29 14:34:23.250] [47070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 85
[07-29 14:34:23.250] [47070 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.250] [47072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.250] [47072 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.261] [47274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 85
[07-29 14:34:23.261] [47274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.261] [47274 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a03e2c0 len 0x4
[07-29 14:34:23.261] [47274 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.262] [47274 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.262] [47274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 85
[07-29 14:34:23.262] [47274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.262] [47274 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.262] [47274 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.262] [47274 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.262] [47274 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.262] [47274 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.263] [47302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:23.263] [47302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.263] [47302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.263] [47302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.263] [47302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 86
[07-29 14:34:23.263] [47302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:23.263] [47302 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x36 len 0x4
[07-29 14:34:23.263] [47302 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.263] [47302 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x36 len 0x4
[07-29 14:34:23.263] [47302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 86
[07-29 14:34:23.263] [47302 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.263] [47304 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.263] [47304 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:23.275] [47506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 86
[07-29 14:34:23.275] [47506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.275] [47506 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a03ffc0 len 0x4
[07-29 14:34:23.275] [47506 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.275] [47506 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.275] [47506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 86
[07-29 14:34:23.275] [47506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.275] [47506 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.275] [47506 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.275] [47506 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.275] [47506 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.275] [47506 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.277] [47534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.277] [47534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.277] [47534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.277] [47534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.277] [47534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 87
[07-29 14:34:23.277] [47534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.277] [47534 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[07-29 14:34:23.277] [47534 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.277] [47534 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[07-29 14:34:23.277] [47534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 87
[07-29 14:34:23.277] [47534 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.277] [47536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.277] [47536 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.288] [47738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 87
[07-29 14:34:23.288] [47738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.288] [47738 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a041cc0 len 0x4
[07-29 14:34:23.288] [47738 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.288] [47738 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.288] [47738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 87
[07-29 14:34:23.288] [47738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.288] [47738 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.288] [47738 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.288] [47738 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.288] [47738 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.288] [47738 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.290] [47766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:23.290] [47766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.290] [47766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.290] [47766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.290] [47766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 88
[07-29 14:34:23.290] [47766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:23.290] [47766 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x39 len 0x4
[07-29 14:34:23.290] [47766 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.290] [47766 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x39 len 0x4
[07-29 14:34:23.290] [47766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 88
[07-29 14:34:23.290] [47766 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.290] [47768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.290] [47768 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:23.302] [47970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 88
[07-29 14:34:23.302] [47970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.302] [47970 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0439c0 len 0x4
[07-29 14:34:23.302] [47970 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.302] [47970 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.302] [47970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 88
[07-29 14:34:23.302] [47970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.302] [47970 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.302] [47970 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.302] [47970 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.302] [47970 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.302] [47970 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.303] [47998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.303] [47998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.303] [47998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.303] [47998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.303] [47998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 89
[07-29 14:34:23.303] [47998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.303] [47998 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x32 len 0x4
[07-29 14:34:23.303] [47998 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.303] [47998 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x32 len 0x4
[07-29 14:34:23.303] [47998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 89
[07-29 14:34:23.303] [47998 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.303] [48 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.303] [48 us] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.315] [48202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 89
[07-29 14:34:23.315] [48202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.315] [48202 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0456c0 len 0x4
[07-29 14:34:23.315] [48202 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.315] [48202 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.315] [48202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 89
[07-29 14:34:23.315] [48202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.315] [48202 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.315] [48202 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.315] [48202 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.315] [48202 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.315] [48202 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.317] [48230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:23.317] [48230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.317] [48230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.317] [48230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.317] [48230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 90
[07-29 14:34:23.317] [48230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:23.317] [48230 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x31 len 0x4
[07-29 14:34:23.317] [48230 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.317] [48230 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x31 len 0x4
[07-29 14:34:23.317] [48230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 90
[07-29 14:34:23.317] [48230 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.317] [48232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.317] [48232 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:23.328] [48434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 90
[07-29 14:34:23.328] [48434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.328] [48434 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0473c0 len 0x4
[07-29 14:34:23.328] [48434 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.328] [48434 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.328] [48434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 90
[07-29 14:34:23.328] [48434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.328] [48434 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.328] [48434 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.328] [48434 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.328] [48434 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.328] [48434 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.330] [48462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.330] [48462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.330] [48462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.330] [48462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.330] [48462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 91
[07-29 14:34:23.330] [48462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.330] [48462 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[07-29 14:34:23.330] [48462 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.330] [48462 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[07-29 14:34:23.330] [48462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 91
[07-29 14:34:23.330] [48462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.330] [48464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.330] [48464 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.342] [48666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 91
[07-29 14:34:23.342] [48666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.342] [48666 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0490c0 len 0x4
[07-29 14:34:23.342] [48666 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.342] [48666 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.342] [48666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 91
[07-29 14:34:23.342] [48666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.342] [48666 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.342] [48666 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.342] [48666 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.342] [48666 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.342] [48666 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.343] [48694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:23.343] [48694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.343] [48694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.343] [48694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.343] [48694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 92
[07-29 14:34:23.343] [48694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:23.343] [48694 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x48 len 0x4
[07-29 14:34:23.343] [48694 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.343] [48694 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x48 len 0x4
[07-29 14:34:23.343] [48694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 92
[07-29 14:34:23.343] [48694 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.343] [48696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.344] [48696 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:23.355] [48898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 92
[07-29 14:34:23.355] [48898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.355] [48898 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a121c00 len 0x4
[07-29 14:34:23.355] [48898 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.355] [48898 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.355] [48898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 92
[07-29 14:34:23.355] [48898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.355] [48898 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.355] [48898 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.355] [48898 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.355] [48898 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.355] [48898 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.357] [48926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.357] [48926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.357] [48926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.357] [48926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.357] [48926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 93
[07-29 14:34:23.357] [48926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.357] [48926 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x7a len 0x4
[07-29 14:34:23.357] [48926 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.357] [48926 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x7a len 0x4
[07-29 14:34:23.357] [48926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 93
[07-29 14:34:23.357] [48926 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.357] [48928 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.357] [48928 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.368] [49130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 93
[07-29 14:34:23.368] [49130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.368] [49130 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a123900 len 0x4
[07-29 14:34:23.368] [49130 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.368] [49130 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.368] [49130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 93
[07-29 14:34:23.368] [49130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.368] [49130 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.368] [49130 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.368] [49130 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.368] [49130 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.368] [49130 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.370] [49158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:23.370] [49158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.370] [49158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.370] [49158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.370] [49158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 94
[07-29 14:34:23.370] [49158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:23.370] [49158 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[07-29 14:34:23.370] [49158 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.370] [49158 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[07-29 14:34:23.370] [49158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 94
[07-29 14:34:23.370] [49158 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.370] [49160 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.370] [49160 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:23.380] [49336 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 94
[07-29 14:34:23.380] [49336 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.380] [49336 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a1252c0 len 0x4
[07-29 14:34:23.380] [49336 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.380] [49336 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.380] [49336 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 94
[07-29 14:34:23.380] [49336 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.380] [49336 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.380] [49336 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.380] [49336 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.380] [49336 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.380] [49336 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.382] [49364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 95
[07-29 14:34:23.382] [49364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:23.382] [49364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.382] [49364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.382] [49364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.382] [49364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:23.382] [49364 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[07-29 14:34:23.382] [49364 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.382] [49364 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[07-29 14:34:23.382] [49364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 95
[07-29 14:34:23.382] [49364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.382] [49366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.382] [49366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:23.386] [49442 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 95
[07-29 14:34:23.386] [49442 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.386] [49442 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a126000 len 0x4
[07-29 14:34:23.386] [49442 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.386] [49442 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.386] [49442 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 95
[07-29 14:34:23.386] [49442 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.386] [49442 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.386] [49442 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.386] [49442 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.386] [49442 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.386] [49442 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.388] [49470 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.388] [49470 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.388] [49470 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.388] [49470 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.388] [49470 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 96
[07-29 14:34:23.388] [49470 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.388] [49470 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[07-29 14:34:23.388] [49470 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.388] [49470 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[07-29 14:34:23.388] [49470 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 96
[07-29 14:34:23.388] [49470 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.388] [49472 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.388] [49472 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.669] [54104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 96
[07-29 14:34:23.669] [54104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.669] [54104 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a3c6e50 len 0x4
[07-29 14:34:23.669] [54104 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.669] [54104 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.669] [54104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 96
[07-29 14:34:23.669] [54104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.669] [54104 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.669] [54104 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.669] [54104 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.669] [54104 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.669] [54104 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.670] [54132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 97
[07-29 14:34:23.670] [54132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.670] [54132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.670] [54132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.670] [54132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.670] [54132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.670] [54132 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x43 len 0x4
[07-29 14:34:23.670] [54132 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.670] [54132 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x43 len 0x4
[07-29 14:34:23.670] [54132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 97
[07-29 14:34:23.670] [54132 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.670] [54134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.670] [54134 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.682] [54336 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 97
[07-29 14:34:23.682] [54336 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.682] [54336 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a3c8b50 len 0x4
[07-29 14:34:23.682] [54336 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.682] [54336 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.682] [54336 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 97
[07-29 14:34:23.682] [54336 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.682] [54336 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.682] [54336 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.682] [54336 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.682] [54336 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.682] [54336 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.684] [54364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 98
[07-29 14:34:23.684] [54364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.684] [54364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.684] [54364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.684] [54364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.684] [54364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.684] [54364 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x50 len 0x4
[07-29 14:34:23.684] [54364 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.684] [54364 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x50 len 0x4
[07-29 14:34:23.684] [54364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 98
[07-29 14:34:23.684] [54364 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.684] [54366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.684] [54366 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.695] [54568 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 98
[07-29 14:34:23.695] [54568 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.695] [54568 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a122640 len 0x4
[07-29 14:34:23.695] [54568 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.695] [54568 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.695] [54568 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 98
[07-29 14:34:23.695] [54568 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.695] [54568 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.695] [54568 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.695] [54568 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.695] [54568 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.695] [54568 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.697] [54596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 99
[07-29 14:34:23.697] [54596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.697] [54596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.697] [54596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.697] [54596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.697] [54596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.697] [54596 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x55 len 0x4
[07-29 14:34:23.697] [54596 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.697] [54596 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x55 len 0x4
[07-29 14:34:23.697] [54596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 99
[07-29 14:34:23.697] [54596 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.697] [54598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.697] [54598 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.708] [54800 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 99
[07-29 14:34:23.708] [54800 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.708] [54800 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a124340 len 0x4
[07-29 14:34:23.708] [54800 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.708] [54800 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.708] [54800 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 99
[07-29 14:34:23.708] [54800 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.708] [54800 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.708] [54800 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.708] [54800 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.708] [54800 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.708] [54800 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.710] [54828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 100
[07-29 14:34:23.710] [54828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.710] [54828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.710] [54828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.710] [54828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.710] [54828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.710] [54828 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[07-29 14:34:23.710] [54828 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.710] [54828 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[07-29 14:34:23.710] [54828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 100
[07-29 14:34:23.710] [54828 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.710] [54830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.710] [54830 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.722] [55032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 100
[07-29 14:34:23.722] [55032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.722] [55032 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69f9e7c0 len 0x4
[07-29 14:34:23.722] [55032 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.722] [55032 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.722] [55032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 100
[07-29 14:34:23.722] [55032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.722] [55032 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.722] [55032 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.722] [55032 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.722] [55032 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.722] [55032 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.723] [55060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 101
[07-29 14:34:23.723] [55060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.723] [55060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.723] [55060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.723] [55060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.723] [55060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.723] [55060 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x48 len 0x4
[07-29 14:34:23.723] [55060 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.723] [55060 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x48 len 0x4
[07-29 14:34:23.723] [55060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 101
[07-29 14:34:23.723] [55060 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.723] [55062 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.723] [55062 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.735] [55264 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 101
[07-29 14:34:23.735] [55264 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.735] [55264 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69fa04c0 len 0x4
[07-29 14:34:23.735] [55264 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.735] [55264 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.735] [55264 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 101
[07-29 14:34:23.735] [55264 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.735] [55264 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.735] [55264 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.735] [55264 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.735] [55264 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.735] [55264 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.737] [55292 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 102
[07-29 14:34:23.737] [55292 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.737] [55292 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.737] [55292 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.737] [55292 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.737] [55292 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.737] [55292 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x61 len 0x4
[07-29 14:34:23.737] [55292 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.737] [55292 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x61 len 0x4
[07-29 14:34:23.737] [55292 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 102
[07-29 14:34:23.737] [55292 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.737] [55294 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.737] [55294 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.748] [55496 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 102
[07-29 14:34:23.748] [55496 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.748] [55496 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69f93f20 len 0x4
[07-29 14:34:23.748] [55496 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.748] [55496 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.748] [55496 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 102
[07-29 14:34:23.748] [55496 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.748] [55496 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.748] [55496 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.748] [55496 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.748] [55496 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.748] [55496 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.750] [55524 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 103
[07-29 14:34:23.750] [55524 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.750] [55524 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.750] [55524 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.750] [55524 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.750] [55524 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.750] [55524 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x72 len 0x4
[07-29 14:34:23.750] [55524 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.750] [55524 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x72 len 0x4
[07-29 14:34:23.750] [55524 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 103
[07-29 14:34:23.750] [55524 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.750] [55526 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.750] [55526 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.762] [55728 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 103
[07-29 14:34:23.762] [55728 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.762] [55728 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69f95c20 len 0x4
[07-29 14:34:23.762] [55728 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.762] [55728 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.762] [55728 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 103
[07-29 14:34:23.762] [55728 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.762] [55728 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.762] [55728 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.762] [55728 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.762] [55728 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.762] [55728 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.764] [55756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 104
[07-29 14:34:23.764] [55756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.764] [55756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.764] [55756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.764] [55756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.764] [55756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.764] [55756 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x74 len 0x4
[07-29 14:34:23.764] [55756 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.764] [55756 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x74 len 0x4
[07-29 14:34:23.764] [55756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 104
[07-29 14:34:23.764] [55756 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.764] [55758 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.764] [55758 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.775] [55960 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 104
[07-29 14:34:23.775] [55960 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.775] [55960 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69f97920 len 0x4
[07-29 14:34:23.775] [55960 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.775] [55960 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.775] [55960 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 104
[07-29 14:34:23.775] [55960 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.775] [55960 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.775] [55960 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.775] [55960 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.775] [55960 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.775] [55960 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.777] [55988 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 105
[07-29 14:34:23.777] [55988 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.777] [55988 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.777] [55988 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.777] [55988 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.777] [55988 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.777] [55988 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x49 len 0x4
[07-29 14:34:23.777] [55988 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.777] [55988 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x49 len 0x4
[07-29 14:34:23.777] [55988 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 105
[07-29 14:34:23.777] [55988 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.777] [55990 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.777] [55990 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.789] [56192 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 105
[07-29 14:34:23.789] [56192 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.789] [56192 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69f99ef0 len 0x4
[07-29 14:34:23.789] [56192 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.789] [56192 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.789] [56192 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 105
[07-29 14:34:23.789] [56192 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.789] [56192 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.789] [56192 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.789] [56192 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.789] [56192 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.789] [56192 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.790] [56220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 106
[07-29 14:34:23.790] [56220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.790] [56220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.790] [56220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.790] [56220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.790] [56220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.790] [56220 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x44 len 0x4
[07-29 14:34:23.790] [56220 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.790] [56220 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x44 len 0x4
[07-29 14:34:23.790] [56220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 106
[07-29 14:34:23.790] [56220 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.791] [56222 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.791] [56222 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.802] [56424 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 106
[07-29 14:34:23.802] [56424 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.802] [56424 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x69f9bbd0 len 0x4
[07-29 14:34:23.802] [56424 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.802] [56424 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.802] [56424 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 106
[07-29 14:34:23.802] [56424 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.802] [56424 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.802] [56424 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.802] [56424 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.802] [56424 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.802] [56424 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.804] [56452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 107
[07-29 14:34:23.804] [56452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.804] [56452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.804] [56452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.804] [56452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.804] [56452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.804] [56452 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x3a len 0x4
[07-29 14:34:23.804] [56452 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.804] [56452 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x3a len 0x4
[07-29 14:34:23.804] [56452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 107
[07-29 14:34:23.804] [56452 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.804] [56454 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.804] [56454 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.815] [56656 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 107
[07-29 14:34:23.815] [56656 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.815] [56656 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0bd990 len 0x4
[07-29 14:34:23.815] [56656 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.815] [56656 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.815] [56656 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 107
[07-29 14:34:23.815] [56656 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.815] [56656 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.815] [56656 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.815] [56656 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.815] [56656 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.815] [56656 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.817] [56684 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 108
[07-29 14:34:23.817] [56684 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.817] [56684 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.817] [56684 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.817] [56684 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.817] [56684 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.817] [56684 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x20 len 0x4
[07-29 14:34:23.817] [56684 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.817] [56684 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x20 len 0x4
[07-29 14:34:23.817] [56684 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 108
[07-29 14:34:23.817] [56684 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.817] [56686 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.817] [56686 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.829] [56888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 108
[07-29 14:34:23.829] [56888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.829] [56888 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0bf690 len 0x4
[07-29 14:34:23.829] [56888 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.829] [56888 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.829] [56888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 108
[07-29 14:34:23.829] [56888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.829] [56888 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.829] [56888 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.829] [56888 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.829] [56888 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.829] [56888 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.830] [56916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 109
[07-29 14:34:23.830] [56916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.830] [56916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.830] [56916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.830] [56916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.830] [56916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.830] [56916 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0x30 len 0x4
[07-29 14:34:23.830] [56916 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.830] [56916 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0x30 len 0x4
[07-29 14:34:23.830] [56916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 109
[07-29 14:34:23.830] [56916 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.830] [56918 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.830] [56918 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.842] [57120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 109
[07-29 14:34:23.842] [57120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.842] [57120 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0c1390 len 0x4
[07-29 14:34:23.842] [57120 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.842] [57120 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.842] [57120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 109
[07-29 14:34:23.842] [57120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.842] [57120 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.842] [57120 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.842] [57120 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.842] [57120 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.842] [57120 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.844] [57148 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f526a0c4ed0, reqid: 110
[07-29 14:34:23.844] [57148 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f52681757b0
[07-29 14:34:23.844] [57148 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.844] [57148 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.844] [57148 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.844] [57148 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f526a0c4ed0, wt: 0x7f52681757b0
[07-29 14:34:23.844] [57148 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[07-29 14:34:23.844] [57148 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.844] [57148 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[07-29 14:34:23.844] [57148 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 110
[07-29 14:34:23.844] [57148 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.844] [57150 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.844] [57150 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f526a0c4ed0
[07-29 14:34:23.854] [57326 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f526a0c4ed0, reqid: 110
[07-29 14:34:23.854] [57326 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.854] [57326 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0c2d50 len 0x4
[07-29 14:34:23.854] [57326 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.854] [57326 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.854] [57326 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 110
[07-29 14:34:23.854] [57326 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.854] [57326 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.854] [57326 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.854] [57326 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.854] [57326 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.854] [57326 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.856] [57354 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:23.856] [57354 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.856] [57354 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.856] [57354 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.856] [57354 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 111
[07-29 14:34:23.856] [57354 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:23.856] [57354 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xd len 0x4
[07-29 14:34:23.856] [57354 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.856] [57354 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xd len 0x4
[07-29 14:34:23.856] [57354 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 111
[07-29 14:34:23.856] [57354 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.856] [57356 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.856] [57356 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
[07-29 14:34:23.860] [57432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.read 0x10013000, arburst 1, arlen 0, tr: 0x7f52681757b0, reqid: 111
[07-29 14:34:23.860] [57432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: araddr.tlm, burstLen: 1
[07-29 14:34:23.860] [57432 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 read master3-n900_vnice_npu_soc.uart addr 0x0 data 0x6a0c3a90 len 0x4
[07-29 14:34:23.860] [57432 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 3 cmd 0 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.860] [57432 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart read slave0 addr 0x10013000 data 0x0 len 0x4
[07-29 14:34:23.860] [57432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, READ, tID: 2, phase: 3, rtList size: 1, reqID: 111
[07-29 14:34:23.860] [57432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.tlm
[07-29 14:34:23.860] [57432 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.860] [57432 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.860] [57432 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.860] [57432 ns] axi2tlm: rdata.write 0x0
[07-29 14:34:23.860] [57432 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: rdata.write
[07-29 14:34:23.862] [57460 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: awaddr.read, awid: 2, awaddr: 10013000, tr: 0x7f52681757b0, reqid: 112
[07-29 14:34:23.862] [57460 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read begin, tr: 0x7f526a0c4ed0
[07-29 14:34:23.862] [57460 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wvalid wdata.read
[07-29 14:34:23.862] [57460 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read wlast
[07-29 14:34:23.862] [57460 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: wdata.read
[07-29 14:34:23.862] [57460 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm, id: 2, addr: 0x10013000, run W_PHASE, awt: 0x7f52681757b0, wt: 0x7f526a0c4ed0
[07-29 14:34:23.862] [57460 ns] n900_vnice_npu_soc.bus nb_transport_fw: slave0 write master3-n900_vnice_npu_soc.uart addr 0x0 data 0xa len 0x4
[07-29 14:34:23.862] [57460 ns] n900_vnice_npu_soc.uart slave 0 nb_transport_fw: phase 1 cmd 1 addr 0x0 len 0x4  in pv mode
[07-29 14:34:23.862] [57460 ns] n900_vnice_npu_soc.bus nb_transport_bw: master3-n900_vnice_npu_soc.uart write slave0 addr 0x10013000 data 0xa len 0x4
[07-29 14:34:23.862] [57460 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: nb_transport_bw, WRITE, tID: 2, phase: 1, wtList size: 1, reqID: 112
[07-29 14:34:23.862] [57460 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 1
[07-29 14:34:23.862] [57462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bvalid.write 0
[07-29 14:34:23.862] [57462 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge axi2tlm: bresp, delete wt: 0x7f52681757b0
