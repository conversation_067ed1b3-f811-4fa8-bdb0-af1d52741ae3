[08-01 09:48:02.996] [0 s] loaded library 'libmosim.so'.
[08-01 09:48:03.007] [0 s] loaded library 'libmosim_common_model.so'.
[08-01 09:48:03.038] [0 s] loaded library 'libn900_vnice.so'.
[08-01 09:48:03.069] [0 s] device: uart init_region:
[08-01 09:48:03.069] [0 s] region   addr               size              
[08-01 09:48:03.069] [0 s] 0x0 0x0 0x41c
[08-01 09:48:03.077] [0 s] monitor: ---- start telnet service port 7070 ----
[08-01 09:48:03.078] [0 s] device: acc0 init_region:
[08-01 09:48:03.078] [0 s] region   addr               size              
[08-01 09:48:03.078] [0 s] 0x0 0x0 0x10000
[08-01 09:48:03.078] [0 s] n900_vnice_npu_soc.acc0 end_of_elaboration: suspend owned threads
[08-01 09:48:03.078] [0 s] device: acc1 init_region:
[08-01 09:48:03.078] [0 s] region   addr               size              
[08-01 09:48:03.078] [0 s] 0x0 0x0 0x1000
[08-01 09:48:03.078] [0 s] n900_vnice_npu_soc.acc1 end_of_elaboration: suspend owned threads
[08-01 09:48:03.078] [0 s] device: ddr init_region:
[08-01 09:48:03.078] [0 s] region   addr               size              
[08-01 09:48:03.347] [0 s] monitor: client 0.0.0.0:53450 connected
[08-01 09:48:03.397] [0 s] monitor get_sc_timestamp
[08-01 09:48:03.568] [0 s] 0x0 0x0 0x40000000
[08-01 09:48:03.568] [0 s] n900_vnice_npu_soc.ddr end_of_elaboration: suspend owned threads
[08-01 09:48:03.568] [0 s] device: gpio init_region:
[08-01 09:48:03.569] [0 s] region   addr               size              
[08-01 09:48:03.569] [0 s] 0x0 0x0 0x1000
[08-01 09:48:03.569] [0 s] n900_vnice_npu_soc.gpio end_of_elaboration: suspend owned threads
[08-01 09:48:03.569] [0 s] device: hole init_region:
[08-01 09:48:03.569] [0 s] region   addr               size              
[08-01 09:48:03.734] [0 s] 0x0 0x0 0xfee0000
[08-01 09:48:03.734] [0 s] n900_vnice_npu_soc.hole end_of_elaboration: suspend owned threads
[08-01 09:48:03.734] [0 s] device: mrom init_region:
[08-01 09:48:03.734] [0 s] region   addr               size              
[08-01 09:48:03.734] [0 s] 0x0 0x0 0x20000
[08-01 09:48:03.734] [0 s] n900_vnice_npu_soc.mrom end_of_elaboration: suspend owned threads
[08-01 09:48:03.798] [0 s] n900_vnice_npu_soc.n900_vnice end_of_elaboration: suspend owned threads
[08-01 09:48:03.798] [0 s] device: qspi0 init_region:
[08-01 09:48:03.798] [0 s] region   addr               size              
[08-01 09:48:03.798] [0 s] 0x0 0x0 0x1000
[08-01 09:48:03.798] [0 s] n900_vnice_npu_soc.qspi0 end_of_elaboration: suspend owned threads
[08-01 09:48:03.798] [0 s] device: qspi1 init_region:
[08-01 09:48:03.798] [0 s] region   addr               size              
[08-01 09:48:03.908] [0 s] 0x0 0x0 0xf000000
[08-01 09:48:03.908] [0 s] n900_vnice_npu_soc.qspi1 end_of_elaboration: suspend owned threads
[08-01 09:48:03.908] [0 s] n900_vnice_npu_soc.reset end_of_elaboration active true
[08-01 09:48:03.908] [0 s] n900_vnice_npu_soc.uart end_of_elaboration: suspend owned threads
[08-01 09:48:03.908] [0 s] device: xip init_region:
[08-01 09:48:03.908] [0 s] region   addr               size              
[08-01 09:48:04.142] [0 s] 0x0 0x0 0x20000000
[08-01 09:48:04.142] [0 s] n900_vnice_npu_soc.xip end_of_elaboration: suspend owned threads
[08-01 09:48:04.142] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[08-01 09:48:04.142] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[08-01 09:48:04.142] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[08-01 09:48:04.142] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[08-01 09:48:04.142] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[08-01 09:48:04.142] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[08-01 09:48:04.142] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge end_of_elaboration: suspend owned threads
[08-01 09:48:04.263] [0 s] n900_vnice_npu_soc.acc0 on_reset: reset owned threads
[08-01 09:48:04.263] [0 s] n900_vnice_npu_soc.acc1 on_reset: reset owned threads
[08-01 09:48:04.263] [0 s] n900_vnice_npu_soc.ddr on_reset: reset owned threads
[08-01 09:48:04.263] [0 s] n900_vnice_npu_soc.gpio on_reset: reset owned threads
[08-01 09:48:04.263] [0 s] n900_vnice_npu_soc.hole on_reset: reset owned threads
[08-01 09:48:04.263] [0 s] n900_vnice_npu_soc.mrom on_reset: reset owned threads
[08-01 09:48:04.263] [0 s] n900_vnice_npu_soc.n900_vnice on_reset: reset owned threads
[08-01 09:48:04.263] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.nice_req_thread
[08-01 09:48:04.263] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.check_time_thread
[08-01 09:48:04.263] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.axi_response_thread
[08-01 09:48:04.263] [0 s] n900_vnice_npu_soc.nice_remote_adapter on_reset: reset owned threads
[08-01 09:48:04.263] [0 s] n900_vnice_npu_soc.qspi0 on_reset: reset owned threads
[08-01 09:48:04.263] [0 s] n900_vnice_npu_soc.qspi1 on_reset: reset owned threads
[08-01 09:48:04.263] [0 s] n900_vnice_npu_soc.xip on_reset: reset owned threads
[08-01 09:48:04.268] [40 ns] n900_vnice_npu_soc.por_reset reset_run active true
[08-01 09:48:04.269] [40 ns] n900_vnice transport_dbg: addr: 0x0 ,len: 0x2148
[08-01 09:48:04.269] [40 ns] n900_vnice transport_dbg: addr: 0x100000 ,len: 0xe20
[08-01 09:48:04.269] [40 ns] n900_vnice transport_dbg: addr: 0x100e20 ,len: 0x800
[08-01 09:48:04.269] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[08-01 09:48:04.269] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[08-01 09:48:04.269] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[08-01 09:48:04.269] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[08-01 09:48:04.269] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[08-01 09:48:04.269] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[08-01 09:48:04.269] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge on_reset: reset owned threads
[08-01 09:48:04.271] [60 ns] n900_vnice_npu_soc.core_reset reset_run active true
[08-01 09:48:06.402] [38502 ns] monitor get_sc_timestamp
[08-01 09:48:09.456] [80264 ns] monitor get_sc_timestamp
[08-01 09:48:12.456] [117753 ns] monitor get_sc_timestamp
[08-01 09:48:15.460] [164182 ns] monitor get_sc_timestamp
[08-01 09:48:17.261] [195028 ns] monitor: client 0.0.0.0:53450 connected close!
[08-01 09:48:17.319] [195644 ns] monitor: client 127.0.0.1:56332 connected
[08-01 09:48:17.364] [195644 ns] monitor get_sc_timestamp
[08-01 09:49:25.883] [0 s] loaded library 'libmosim.so'.
[08-01 09:49:25.893] [0 s] loaded library 'libmosim_common_model.so'.
[08-01 09:49:25.925] [0 s] loaded library 'libn900_vnice.so'.
[08-01 09:49:25.954] [0 s] device: uart init_region:
[08-01 09:49:25.954] [0 s] region   addr               size              
[08-01 09:49:25.954] [0 s] 0x0 0x0 0x41c
