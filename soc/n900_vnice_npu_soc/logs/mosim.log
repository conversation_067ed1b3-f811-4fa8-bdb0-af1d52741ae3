[07-31 09:49:11.846] [0 s] loaded library 'libmosim.so'.
[07-31 09:49:11.856] [0 s] loaded library 'libmosim_common_model.so'.
[07-31 09:49:11.887] [0 s] loaded library 'libn900_vnice.so'.
[07-31 09:49:11.922] [0 s] device: uart init_region:
[07-31 09:49:11.922] [0 s] region   addr               size              
[07-31 09:49:11.922] [0 s] 0x0 0x0 0x41c
[07-31 09:49:11.934] [0 s] monitor: ---- start telnet service port 7070 ----
[07-31 09:49:11.936] [0 s] device: acc0 init_region:
[07-31 09:49:11.936] [0 s] region   addr               size              
[07-31 09:49:11.936] [0 s] 0x0 0x0 0x10000
[07-31 09:49:11.936] [0 s] n900_vnice_npu_soc.acc0 end_of_elaboration: suspend owned threads
[07-31 09:49:11.936] [0 s] device: acc1 init_region:
[07-31 09:49:11.936] [0 s] region   addr               size              
[07-31 09:49:11.936] [0 s] 0x0 0x0 0x1000
[07-31 09:49:11.936] [0 s] n900_vnice_npu_soc.acc1 end_of_elaboration: suspend owned threads
[07-31 09:49:11.936] [0 s] device: ddr init_region:
[07-31 09:49:11.936] [0 s] region   addr               size              
[07-31 09:49:12.430] [0 s] 0x0 0x0 0x40000000
[07-31 09:49:12.430] [0 s] n900_vnice_npu_soc.ddr end_of_elaboration: suspend owned threads
[07-31 09:49:12.430] [0 s] device: gpio init_region:
[07-31 09:49:12.430] [0 s] region   addr               size              
[07-31 09:49:12.430] [0 s] 0x0 0x0 0x1000
[07-31 09:49:12.430] [0 s] n900_vnice_npu_soc.gpio end_of_elaboration: suspend owned threads
[07-31 09:49:12.430] [0 s] device: hole init_region:
[07-31 09:49:12.430] [0 s] region   addr               size              
[07-31 09:49:12.543] [0 s] 0x0 0x0 0xfee0000
[07-31 09:49:12.543] [0 s] n900_vnice_npu_soc.hole end_of_elaboration: suspend owned threads
[07-31 09:49:12.543] [0 s] device: mrom init_region:
[07-31 09:49:12.543] [0 s] region   addr               size              
[07-31 09:49:12.543] [0 s] 0x0 0x0 0x20000
[07-31 09:49:12.543] [0 s] n900_vnice_npu_soc.mrom end_of_elaboration: suspend owned threads
[07-31 09:49:12.589] [0 s] n900_vnice_npu_soc.n900_vnice end_of_elaboration: suspend owned threads
[07-31 09:49:12.589] [0 s] device: qspi0 init_region:
[07-31 09:49:12.589] [0 s] region   addr               size              
[07-31 09:49:12.589] [0 s] 0x0 0x0 0x1000
[07-31 09:49:12.589] [0 s] n900_vnice_npu_soc.qspi0 end_of_elaboration: suspend owned threads
[07-31 09:49:12.589] [0 s] device: qspi1 init_region:
[07-31 09:49:12.589] [0 s] region   addr               size              
[07-31 09:49:12.691] [0 s] 0x0 0x0 0xf000000
[07-31 09:49:12.691] [0 s] n900_vnice_npu_soc.qspi1 end_of_elaboration: suspend owned threads
[07-31 09:49:12.691] [0 s] n900_vnice_npu_soc.reset end_of_elaboration active true
[07-31 09:49:12.691] [0 s] n900_vnice_npu_soc.uart end_of_elaboration: suspend owned threads
[07-31 09:49:12.691] [0 s] device: xip init_region:
[07-31 09:49:12.691] [0 s] region   addr               size              
[07-31 09:49:12.911] [0 s] 0x0 0x0 0x20000000
[07-31 09:49:12.911] [0 s] n900_vnice_npu_soc.xip end_of_elaboration: suspend owned threads
[07-31 09:49:12.911] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[07-31 09:49:12.911] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[07-31 09:49:12.911] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[07-31 09:49:12.911] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[07-31 09:49:12.911] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[07-31 09:49:12.911] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[07-31 09:49:12.911] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge end_of_elaboration: suspend owned threads
[07-31 09:49:13.028] [0 s] n900_vnice_npu_soc.acc0 on_reset: reset owned threads
[07-31 09:49:13.028] [0 s] n900_vnice_npu_soc.acc1 on_reset: reset owned threads
[07-31 09:49:13.028] [0 s] n900_vnice_npu_soc.ddr on_reset: reset owned threads
[07-31 09:49:13.028] [0 s] n900_vnice_npu_soc.gpio on_reset: reset owned threads
[07-31 09:49:13.028] [0 s] n900_vnice_npu_soc.hole on_reset: reset owned threads
[07-31 09:49:13.028] [0 s] n900_vnice_npu_soc.mrom on_reset: reset owned threads
[07-31 09:49:13.028] [0 s] n900_vnice_npu_soc.n900_vnice on_reset: reset owned threads
[07-31 09:49:13.028] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.nice_req_thread
[07-31 09:49:13.028] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.check_time_thread
[07-31 09:49:13.028] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.axi_response_thread
[07-31 09:49:13.028] [0 s] n900_vnice_npu_soc.nice_remote_adapter on_reset: reset owned threads
[07-31 09:49:13.028] [0 s] n900_vnice_npu_soc.qspi0 on_reset: reset owned threads
[07-31 09:49:13.028] [0 s] n900_vnice_npu_soc.qspi1 on_reset: reset owned threads
[07-31 09:49:13.028] [0 s] n900_vnice_npu_soc.xip on_reset: reset owned threads
[07-31 09:49:13.033] [40 ns] n900_vnice_npu_soc.por_reset reset_run active true
[07-31 09:49:13.033] [40 ns] n900_vnice transport_dbg: addr: 0x0 ,len: 0x2148
[07-31 09:49:13.033] [40 ns] n900_vnice transport_dbg: addr: 0x100000 ,len: 0xe20
[07-31 09:49:13.033] [40 ns] n900_vnice transport_dbg: addr: 0x100e20 ,len: 0x800
[07-31 09:49:13.033] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[07-31 09:49:13.033] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[07-31 09:49:13.033] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[07-31 09:49:13.033] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[07-31 09:49:13.033] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[07-31 09:49:13.033] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[07-31 09:49:13.033] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge on_reset: reset owned threads
[07-31 09:49:13.035] [60 ns] n900_vnice_npu_soc.core_reset reset_run active true
[07-31 09:49:14.397] [30350 ns] monitor: client 0.0.0.0:60476 connected
[07-31 09:49:14.444] [31398 ns] monitor get_sc_timestamp
[07-31 09:49:17.414] [80194 ns] monitor get_sc_timestamp
[07-31 09:49:20.484] [124120 ns] monitor get_sc_timestamp
[07-31 09:49:23.506] [172110 ns] monitor get_sc_timestamp
[07-31 09:49:26.506] [223748 ns] monitor get_sc_timestamp
[07-31 09:49:29.512] [275524 ns] monitor get_sc_timestamp
[07-31 09:49:32.500] [326580 ns] monitor get_sc_timestamp
[07-31 09:49:35.507] [376880 ns] monitor get_sc_timestamp
[07-31 09:49:38.506] [427134 ns] monitor get_sc_timestamp
[07-31 09:49:41.509] [477560 ns] monitor get_sc_timestamp
[07-31 09:49:44.506] [528096 ns] monitor get_sc_timestamp
[07-31 09:49:47.540] [579344 ns] monitor get_sc_timestamp
[07-31 09:49:50.528] [631429 ns] monitor get_sc_timestamp
[07-31 09:49:53.544] [684098 ns] monitor get_sc_timestamp
[07-31 09:49:56.542] [736722 ns] monitor get_sc_timestamp
[07-31 09:49:59.551] [789502 ns] monitor get_sc_timestamp
[07-31 09:50:02.548] [842022 ns] monitor get_sc_timestamp
[07-31 09:50:05.549] [891990 ns] monitor get_sc_timestamp
[07-31 09:50:08.548] [941738 ns] monitor get_sc_timestamp
[07-31 09:50:11.550] [991382 ns] monitor get_sc_timestamp
[07-31 09:50:14.551] [1041979 ns] monitor get_sc_timestamp
[07-31 09:50:17.539] [1090976 ns] monitor get_sc_timestamp
[07-31 09:50:20.551] [1140905 ns] monitor get_sc_timestamp
[07-31 09:50:23.583] [1191852 ns] monitor get_sc_timestamp
[07-31 09:50:26.590] [1242740 ns] monitor get_sc_timestamp
[07-31 09:50:29.583] [1292674 ns] monitor get_sc_timestamp
[07-31 09:50:32.582] [1342516 ns] monitor get_sc_timestamp
[07-31 09:50:35.580] [1392368 ns] monitor get_sc_timestamp
[07-31 09:50:38.583] [1443485 ns] monitor get_sc_timestamp
[07-31 09:50:41.580] [1498296 ns] monitor get_sc_timestamp
[07-31 09:50:44.584] [1555052 ns] monitor get_sc_timestamp
[07-31 09:50:47.586] [1611136 ns] monitor get_sc_timestamp
[07-31 09:50:50.580] [1664333 ns] monitor get_sc_timestamp
[07-31 09:50:53.582] [1718130 ns] monitor get_sc_timestamp
[07-31 09:50:56.585] [1771901 ns] monitor get_sc_timestamp
[07-31 09:50:59.585] [1825706 ns] monitor get_sc_timestamp
[07-31 09:51:02.584] [1879468 ns] monitor get_sc_timestamp
[07-31 09:51:05.585] [1933395 ns] monitor get_sc_timestamp
[07-31 09:51:08.587] [1987178 ns] monitor get_sc_timestamp
[07-31 09:51:11.581] [2040842 ns] monitor get_sc_timestamp
[07-31 09:51:14.580] [2094462 ns] monitor get_sc_timestamp
[07-31 09:51:17.586] [2148 us] monitor get_sc_timestamp
[07-31 09:51:20.578] [2201308 ns] monitor get_sc_timestamp
[07-31 09:51:23.589] [2253753 ns] monitor get_sc_timestamp
[07-31 09:51:26.586] [2307411 ns] monitor get_sc_timestamp
[07-31 09:51:29.587] [2361203 ns] monitor get_sc_timestamp
[07-31 09:51:32.583] [2414986 ns] monitor get_sc_timestamp
[07-31 09:51:35.585] [2468859 ns] monitor get_sc_timestamp
[07-31 09:51:38.586] [2522636 ns] monitor get_sc_timestamp
[07-31 09:51:41.580] [2576304 ns] monitor get_sc_timestamp
[07-31 09:51:44.585] [2630402 ns] monitor get_sc_timestamp
[07-31 09:51:47.585] [2684686 ns] monitor get_sc_timestamp
