[07-31 10:00:44.413] [0 s] loaded library 'libmosim.so'.
[07-31 10:00:44.421] [0 s] loaded library 'libmosim_common_model.so'.
[07-31 10:00:44.451] [0 s] loaded library 'libn900_vnice.so'.
[07-31 10:00:44.479] [0 s] device: uart init_region:
[07-31 10:00:44.479] [0 s] region   addr               size              
[07-31 10:00:44.479] [0 s] 0x0 0x0 0x41c
[07-31 10:00:44.487] [0 s] monitor: ---- start telnet service port 7070 ----
[07-31 10:00:44.488] [0 s] device: acc0 init_region:
[07-31 10:00:44.488] [0 s] region   addr               size              
[07-31 10:00:44.488] [0 s] 0x0 0x0 0x10000
[07-31 10:00:44.488] [0 s] n900_vnice_npu_soc.acc0 end_of_elaboration: suspend owned threads
[07-31 10:00:44.488] [0 s] device: acc1 init_region:
[07-31 10:00:44.488] [0 s] region   addr               size              
[07-31 10:00:44.488] [0 s] 0x0 0x0 0x1000
[07-31 10:00:44.488] [0 s] n900_vnice_npu_soc.acc1 end_of_elaboration: suspend owned threads
[07-31 10:00:44.488] [0 s] device: ddr init_region:
[07-31 10:00:44.488] [0 s] region   addr               size              
[07-31 10:00:44.982] [0 s] 0x0 0x0 0x40000000
[07-31 10:00:44.982] [0 s] n900_vnice_npu_soc.ddr end_of_elaboration: suspend owned threads
[07-31 10:00:44.982] [0 s] device: gpio init_region:
[07-31 10:00:44.982] [0 s] region   addr               size              
[07-31 10:00:44.982] [0 s] 0x0 0x0 0x1000
[07-31 10:00:44.982] [0 s] n900_vnice_npu_soc.gpio end_of_elaboration: suspend owned threads
[07-31 10:00:44.982] [0 s] device: hole init_region:
[07-31 10:00:44.982] [0 s] region   addr               size              
[07-31 10:00:45.115] [0 s] 0x0 0x0 0xfee0000
[07-31 10:00:45.115] [0 s] n900_vnice_npu_soc.hole end_of_elaboration: suspend owned threads
[07-31 10:00:45.115] [0 s] device: mrom init_region:
[07-31 10:00:45.115] [0 s] region   addr               size              
[07-31 10:00:45.115] [0 s] 0x0 0x0 0x20000
[07-31 10:00:45.115] [0 s] n900_vnice_npu_soc.mrom end_of_elaboration: suspend owned threads
[07-31 10:00:45.186] [0 s] n900_vnice_npu_soc.n900_vnice end_of_elaboration: suspend owned threads
[07-31 10:00:45.186] [0 s] device: qspi0 init_region:
[07-31 10:00:45.186] [0 s] region   addr               size              
[07-31 10:00:45.186] [0 s] 0x0 0x0 0x1000
[07-31 10:00:45.186] [0 s] n900_vnice_npu_soc.qspi0 end_of_elaboration: suspend owned threads
[07-31 10:00:45.186] [0 s] device: qspi1 init_region:
[07-31 10:00:45.186] [0 s] region   addr               size              
[07-31 10:00:45.333] [0 s] 0x0 0x0 0xf000000
[07-31 10:00:45.333] [0 s] n900_vnice_npu_soc.qspi1 end_of_elaboration: suspend owned threads
[07-31 10:00:45.333] [0 s] n900_vnice_npu_soc.reset end_of_elaboration active true
[07-31 10:00:45.333] [0 s] n900_vnice_npu_soc.uart end_of_elaboration: suspend owned threads
[07-31 10:00:45.333] [0 s] device: xip init_region:
[07-31 10:00:45.333] [0 s] region   addr               size              
[07-31 10:00:45.562] [0 s] 0x0 0x0 0x20000000
[07-31 10:00:45.562] [0 s] n900_vnice_npu_soc.xip end_of_elaboration: suspend owned threads
[07-31 10:00:45.562] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[07-31 10:00:45.562] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[07-31 10:00:45.562] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[07-31 10:00:45.562] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[07-31 10:00:45.562] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[07-31 10:00:45.562] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[07-31 10:00:45.562] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge end_of_elaboration: suspend owned threads
[07-31 10:00:45.681] [0 s] n900_vnice_npu_soc.acc0 on_reset: reset owned threads
[07-31 10:00:45.681] [0 s] n900_vnice_npu_soc.acc1 on_reset: reset owned threads
[07-31 10:00:45.681] [0 s] n900_vnice_npu_soc.ddr on_reset: reset owned threads
[07-31 10:00:45.681] [0 s] n900_vnice_npu_soc.gpio on_reset: reset owned threads
[07-31 10:00:45.681] [0 s] n900_vnice_npu_soc.hole on_reset: reset owned threads
[07-31 10:00:45.681] [0 s] n900_vnice_npu_soc.mrom on_reset: reset owned threads
[07-31 10:00:45.681] [0 s] n900_vnice_npu_soc.n900_vnice on_reset: reset owned threads
[07-31 10:00:45.681] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.nice_req_thread
[07-31 10:00:45.681] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.check_time_thread
[07-31 10:00:45.681] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.axi_response_thread
[07-31 10:00:45.681] [0 s] n900_vnice_npu_soc.nice_remote_adapter on_reset: reset owned threads
[07-31 10:00:45.681] [0 s] n900_vnice_npu_soc.qspi0 on_reset: reset owned threads
[07-31 10:00:45.681] [0 s] n900_vnice_npu_soc.qspi1 on_reset: reset owned threads
[07-31 10:00:45.681] [0 s] n900_vnice_npu_soc.xip on_reset: reset owned threads
[07-31 10:00:45.686] [40 ns] n900_vnice_npu_soc.por_reset reset_run active true
[07-31 10:00:45.686] [40 ns] n900_vnice transport_dbg: addr: 0x0 ,len: 0x2148
[07-31 10:00:45.686] [40 ns] n900_vnice transport_dbg: addr: 0x100000 ,len: 0xe20
[07-31 10:00:45.686] [40 ns] n900_vnice transport_dbg: addr: 0x100e20 ,len: 0x800
[07-31 10:00:45.686] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[07-31 10:00:45.686] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[07-31 10:00:45.686] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[07-31 10:00:45.686] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[07-31 10:00:45.686] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[07-31 10:00:45.686] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[07-31 10:00:45.686] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge on_reset: reset owned threads
[07-31 10:00:45.688] [60 ns] n900_vnice_npu_soc.core_reset reset_run active true
[07-31 10:00:47.038] [27330 ns] monitor: client 0.0.0.0:52898 connected
[07-31 10:00:47.080] [27940 ns] monitor get_sc_timestamp
[07-31 10:00:50.042] [74601 ns] monitor get_sc_timestamp
[07-31 10:00:53.033] [111122 ns] monitor get_sc_timestamp
[07-31 10:00:56.042] [148748 ns] monitor get_sc_timestamp
[07-31 10:00:59.045] [193357 ns] monitor get_sc_timestamp
[07-31 10:01:02.028] [235502 ns] monitor get_sc_timestamp
[07-31 10:01:05.032] [281518 ns] monitor get_sc_timestamp
[07-31 10:01:07.283] [315396 ns] monitor: client 0.0.0.0:52898 connected close!
[07-31 10:01:07.353] [315928 ns] monitor: client 127.0.0.1:34632 connected
[07-31 10:01:07.398] [315928 ns] monitor get_sc_timestamp
[07-31 11:13:18.356] [0 s] loaded library 'libmosim.so'.
[07-31 11:13:18.368] [0 s] loaded library 'libmosim_common_model.so'.
[07-31 11:13:18.402] [0 s] loaded library 'libn900_vnice.so'.
[07-31 11:13:18.447] [0 s] device: uart init_region:
[07-31 11:13:18.447] [0 s] region   addr               size              
[07-31 11:13:18.447] [0 s] 0x0 0x0 0x41c
