[07-31 09:56:31.346] [0 s] loaded library 'libmosim.so'.
[07-31 09:56:31.355] [0 s] loaded library 'libmosim_common_model.so'.
[07-31 09:56:31.385] [0 s] loaded library 'libn900_vnice.so'.
[07-31 09:56:31.414] [0 s] device: uart init_region:
[07-31 09:56:31.414] [0 s] region   addr               size              
[07-31 09:56:31.414] [0 s] 0x0 0x0 0x41c
[07-31 09:56:31.422] [0 s] monitor: ---- start telnet service port 7070 ----
[07-31 09:56:31.423] [0 s] device: acc0 init_region:
[07-31 09:56:31.423] [0 s] region   addr               size              
[07-31 09:56:31.423] [0 s] 0x0 0x0 0x10000
[07-31 09:56:31.423] [0 s] n900_vnice_npu_soc.acc0 end_of_elaboration: suspend owned threads
[07-31 09:56:31.423] [0 s] device: acc1 init_region:
[07-31 09:56:31.423] [0 s] region   addr               size              
[07-31 09:56:31.423] [0 s] 0x0 0x0 0x1000
[07-31 09:56:31.423] [0 s] n900_vnice_npu_soc.acc1 end_of_elaboration: suspend owned threads
[07-31 09:56:31.423] [0 s] device: ddr init_region:
[07-31 09:56:31.423] [0 s] region   addr               size              
[07-31 09:56:31.867] [0 s] 0x0 0x0 0x40000000
[07-31 09:56:31.867] [0 s] n900_vnice_npu_soc.ddr end_of_elaboration: suspend owned threads
[07-31 09:56:31.867] [0 s] device: gpio init_region:
[07-31 09:56:31.867] [0 s] region   addr               size              
[07-31 09:56:31.867] [0 s] 0x0 0x0 0x1000
[07-31 09:56:31.867] [0 s] n900_vnice_npu_soc.gpio end_of_elaboration: suspend owned threads
[07-31 09:56:31.867] [0 s] device: hole init_region:
[07-31 09:56:31.867] [0 s] region   addr               size              
[07-31 09:56:31.980] [0 s] 0x0 0x0 0xfee0000
[07-31 09:56:31.980] [0 s] n900_vnice_npu_soc.hole end_of_elaboration: suspend owned threads
[07-31 09:56:31.980] [0 s] device: mrom init_region:
[07-31 09:56:31.980] [0 s] region   addr               size              
[07-31 09:56:31.980] [0 s] 0x0 0x0 0x20000
[07-31 09:56:31.980] [0 s] n900_vnice_npu_soc.mrom end_of_elaboration: suspend owned threads
[07-31 09:56:32.025] [0 s] n900_vnice_npu_soc.n900_vnice end_of_elaboration: suspend owned threads
[07-31 09:56:32.026] [0 s] device: qspi0 init_region:
[07-31 09:56:32.026] [0 s] region   addr               size              
[07-31 09:56:32.026] [0 s] 0x0 0x0 0x1000
[07-31 09:56:32.026] [0 s] n900_vnice_npu_soc.qspi0 end_of_elaboration: suspend owned threads
[07-31 09:56:32.026] [0 s] device: qspi1 init_region:
[07-31 09:56:32.026] [0 s] region   addr               size              
[07-31 09:56:32.130] [0 s] 0x0 0x0 0xf000000
[07-31 09:56:32.130] [0 s] n900_vnice_npu_soc.qspi1 end_of_elaboration: suspend owned threads
[07-31 09:56:32.130] [0 s] n900_vnice_npu_soc.reset end_of_elaboration active true
[07-31 09:56:32.130] [0 s] n900_vnice_npu_soc.uart end_of_elaboration: suspend owned threads
[07-31 09:56:32.130] [0 s] device: xip init_region:
[07-31 09:56:32.130] [0 s] region   addr               size              
[07-31 09:56:32.358] [0 s] 0x0 0x0 0x20000000
[07-31 09:56:32.358] [0 s] n900_vnice_npu_soc.xip end_of_elaboration: suspend owned threads
[07-31 09:56:32.358] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[07-31 09:56:32.358] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[07-31 09:56:32.358] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[07-31 09:56:32.358] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[07-31 09:56:32.358] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[07-31 09:56:32.358] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[07-31 09:56:32.358] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge end_of_elaboration: suspend owned threads
[07-31 09:56:32.479] [0 s] n900_vnice_npu_soc.acc0 on_reset: reset owned threads
[07-31 09:56:32.479] [0 s] n900_vnice_npu_soc.acc1 on_reset: reset owned threads
[07-31 09:56:32.479] [0 s] n900_vnice_npu_soc.ddr on_reset: reset owned threads
[07-31 09:56:32.479] [0 s] n900_vnice_npu_soc.gpio on_reset: reset owned threads
[07-31 09:56:32.479] [0 s] n900_vnice_npu_soc.hole on_reset: reset owned threads
[07-31 09:56:32.479] [0 s] n900_vnice_npu_soc.mrom on_reset: reset owned threads
[07-31 09:56:32.479] [0 s] n900_vnice_npu_soc.n900_vnice on_reset: reset owned threads
[07-31 09:56:32.479] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.nice_req_thread
[07-31 09:56:32.480] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.check_time_thread
[07-31 09:56:32.480] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.axi_response_thread
[07-31 09:56:32.480] [0 s] n900_vnice_npu_soc.nice_remote_adapter on_reset: reset owned threads
[07-31 09:56:32.480] [0 s] n900_vnice_npu_soc.qspi0 on_reset: reset owned threads
[07-31 09:56:32.480] [0 s] n900_vnice_npu_soc.qspi1 on_reset: reset owned threads
[07-31 09:56:32.480] [0 s] n900_vnice_npu_soc.xip on_reset: reset owned threads
[07-31 09:56:32.484] [40 ns] n900_vnice_npu_soc.por_reset reset_run active true
[07-31 09:56:32.485] [40 ns] n900_vnice transport_dbg: addr: 0x0 ,len: 0x2148
[07-31 09:56:32.485] [40 ns] n900_vnice transport_dbg: addr: 0x100000 ,len: 0xe20
[07-31 09:56:32.485] [40 ns] n900_vnice transport_dbg: addr: 0x100e20 ,len: 0x800
[07-31 09:56:32.485] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[07-31 09:56:32.485] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[07-31 09:56:32.485] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[07-31 09:56:32.485] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[07-31 09:56:32.485] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[07-31 09:56:32.485] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[07-31 09:56:32.485] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge on_reset: reset owned threads
[07-31 09:56:32.486] [60 ns] n900_vnice_npu_soc.core_reset reset_run active true
[07-31 09:56:33.872] [21264 ns] monitor: client 0.0.0.0:35992 connected
[07-31 09:56:33.916] [21838 ns] monitor get_sc_timestamp
[07-31 09:56:36.866] [58768 ns] monitor get_sc_timestamp
[07-31 09:56:39.885] [94900 ns] monitor get_sc_timestamp
[07-31 09:56:42.881] [131196 ns] monitor get_sc_timestamp
[07-31 09:56:45.948] [178512 ns] monitor get_sc_timestamp
[07-31 09:56:48.877] [224758 ns] monitor get_sc_timestamp
[07-31 09:56:51.878] [272146 ns] monitor get_sc_timestamp
[07-31 09:56:54.878] [320076 ns] monitor get_sc_timestamp
[07-31 09:56:57.878] [368386 ns] monitor get_sc_timestamp
[07-31 09:57:00.879] [416648 ns] monitor get_sc_timestamp
[07-31 09:57:03.879] [465099 ns] monitor get_sc_timestamp
[07-31 09:57:06.886] [513706 ns] monitor get_sc_timestamp
[07-31 09:57:09.883] [562086 ns] monitor get_sc_timestamp
[07-31 09:57:12.885] [610593 ns] monitor get_sc_timestamp
[07-31 09:57:15.951] [659864 ns] monitor get_sc_timestamp
[07-31 09:57:18.883] [707244 ns] monitor get_sc_timestamp
[07-31 09:57:21.888] [755636 ns] monitor get_sc_timestamp
[07-31 09:57:24.881] [803914 ns] monitor get_sc_timestamp
[07-31 09:57:27.886] [852366 ns] monitor get_sc_timestamp
[07-31 09:57:30.880] [901039 ns] monitor get_sc_timestamp
[07-31 09:57:33.880] [949858 ns] monitor get_sc_timestamp
[07-31 09:57:36.881] [998614 ns] monitor get_sc_timestamp
[07-31 09:57:39.881] [1047126 ns] monitor get_sc_timestamp
[07-31 09:57:42.885] [1095454 ns] monitor get_sc_timestamp
[07-31 09:57:45.966] [1146364 ns] monitor get_sc_timestamp
[07-31 09:57:48.885] [1195034 ns] monitor get_sc_timestamp
[07-31 09:57:51.886] [1245646 ns] monitor get_sc_timestamp
[07-31 09:57:54.889] [1296229 ns] monitor get_sc_timestamp
[07-31 09:57:57.891] [1346788 ns] monitor get_sc_timestamp
[07-31 09:58:00.882] [1397128 ns] monitor get_sc_timestamp
[07-31 09:58:03.885] [1447722 ns] monitor get_sc_timestamp
[07-31 09:58:06.880] [1496506 ns] monitor get_sc_timestamp
[07-31 09:58:09.875] [1544780 ns] monitor get_sc_timestamp
[07-31 09:58:12.882] [1595466 ns] monitor get_sc_timestamp
[07-31 09:58:15.962] [1645550 ns] monitor get_sc_timestamp
[07-31 09:58:18.896] [1692184 ns] monitor get_sc_timestamp
[07-31 09:58:21.897] [1740024 ns] monitor get_sc_timestamp
[07-31 09:58:24.896] [1791590 ns] monitor get_sc_timestamp
[07-31 09:58:27.902] [1845476 ns] monitor get_sc_timestamp
