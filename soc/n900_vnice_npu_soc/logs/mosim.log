[08-01 10:18:08.836] [0 s] loaded library 'libmosim.so'.
[08-01 10:18:08.847] [0 s] loaded library 'libmosim_common_model.so'.
[08-01 10:18:08.878] [0 s] loaded library 'libn900_vnice.so'.
[08-01 10:18:08.908] [0 s] device: uart init_region:
[08-01 10:18:08.908] [0 s] region   addr               size              
[08-01 10:18:08.908] [0 s] 0x0 0x0 0x41c
[08-01 10:18:08.917] [0 s] monitor: ---- start telnet service port 7070 ----
[08-01 10:18:08.918] [0 s] device: acc0 init_region:
[08-01 10:18:08.918] [0 s] region   addr               size              
[08-01 10:18:08.918] [0 s] 0x0 0x0 0x10000
[08-01 10:18:08.918] [0 s] n900_vnice_npu_soc.acc0 end_of_elaboration: suspend owned threads
[08-01 10:18:08.918] [0 s] device: acc1 init_region:
[08-01 10:18:08.918] [0 s] region   addr               size              
[08-01 10:18:08.918] [0 s] 0x0 0x0 0x1000
[08-01 10:18:08.918] [0 s] n900_vnice_npu_soc.acc1 end_of_elaboration: suspend owned threads
[08-01 10:18:08.918] [0 s] device: ddr init_region:
[08-01 10:18:08.918] [0 s] region   addr               size              
[08-01 10:18:09.405] [0 s] 0x0 0x0 0x40000000
[08-01 10:18:09.405] [0 s] n900_vnice_npu_soc.ddr end_of_elaboration: suspend owned threads
[08-01 10:18:09.405] [0 s] device: gpio init_region:
[08-01 10:18:09.405] [0 s] region   addr               size              
[08-01 10:18:09.405] [0 s] 0x0 0x0 0x1000
[08-01 10:18:09.405] [0 s] n900_vnice_npu_soc.gpio end_of_elaboration: suspend owned threads
[08-01 10:18:09.405] [0 s] device: hole init_region:
[08-01 10:18:09.405] [0 s] region   addr               size              
[08-01 10:18:09.532] [0 s] 0x0 0x0 0xfee0000
[08-01 10:18:09.532] [0 s] n900_vnice_npu_soc.hole end_of_elaboration: suspend owned threads
[08-01 10:18:09.533] [0 s] device: mrom init_region:
[08-01 10:18:09.533] [0 s] region   addr               size              
[08-01 10:18:09.533] [0 s] 0x0 0x0 0x20000
[08-01 10:18:09.533] [0 s] n900_vnice_npu_soc.mrom end_of_elaboration: suspend owned threads
[08-01 10:18:09.582] [0 s] n900_vnice_npu_soc.n900_vnice end_of_elaboration: suspend owned threads
[08-01 10:18:09.582] [0 s] device: qspi0 init_region:
[08-01 10:18:09.582] [0 s] region   addr               size              
[08-01 10:18:09.582] [0 s] 0x0 0x0 0x1000
[08-01 10:18:09.582] [0 s] n900_vnice_npu_soc.qspi0 end_of_elaboration: suspend owned threads
[08-01 10:18:09.582] [0 s] device: qspi1 init_region:
[08-01 10:18:09.582] [0 s] region   addr               size              
[08-01 10:18:09.697] [0 s] 0x0 0x0 0xf000000
[08-01 10:18:09.697] [0 s] n900_vnice_npu_soc.qspi1 end_of_elaboration: suspend owned threads
[08-01 10:18:09.697] [0 s] n900_vnice_npu_soc.reset end_of_elaboration active true
[08-01 10:18:09.697] [0 s] n900_vnice_npu_soc.uart end_of_elaboration: suspend owned threads
[08-01 10:18:09.697] [0 s] device: xip init_region:
[08-01 10:18:09.697] [0 s] region   addr               size              
[08-01 10:18:09.938] [0 s] 0x0 0x0 0x20000000
[08-01 10:18:09.938] [0 s] n900_vnice_npu_soc.xip end_of_elaboration: suspend owned threads
[08-01 10:18:09.938] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[08-01 10:18:09.938] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[08-01 10:18:09.938] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[08-01 10:18:09.938] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[08-01 10:18:09.938] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[08-01 10:18:09.938] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge suspend thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[08-01 10:18:09.938] [0 s] n900_vnice_npu_soc.mem__128_axi_m_0_bridge end_of_elaboration: suspend owned threads
[08-01 10:18:10.065] [0 s] n900_vnice_npu_soc.acc0 on_reset: reset owned threads
[08-01 10:18:10.065] [0 s] n900_vnice_npu_soc.acc1 on_reset: reset owned threads
[08-01 10:18:10.065] [0 s] n900_vnice_npu_soc.ddr on_reset: reset owned threads
[08-01 10:18:10.065] [0 s] n900_vnice_npu_soc.gpio on_reset: reset owned threads
[08-01 10:18:10.065] [0 s] n900_vnice_npu_soc.hole on_reset: reset owned threads
[08-01 10:18:10.065] [0 s] n900_vnice_npu_soc.mrom on_reset: reset owned threads
[08-01 10:18:10.065] [0 s] n900_vnice_npu_soc.n900_vnice on_reset: reset owned threads
[08-01 10:18:10.066] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.nice_req_thread
[08-01 10:18:10.066] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.check_time_thread
[08-01 10:18:10.066] [0 s] n900_vnice_npu_soc.nice_remote_adapter reset  thread: n900_vnice_npu_soc.nice_remote_adapter.axi_response_thread
[08-01 10:18:10.066] [0 s] n900_vnice_npu_soc.nice_remote_adapter on_reset: reset owned threads
[08-01 10:18:10.066] [0 s] n900_vnice_npu_soc.qspi0 on_reset: reset owned threads
[08-01 10:18:10.066] [0 s] n900_vnice_npu_soc.qspi1 on_reset: reset owned threads
[08-01 10:18:10.066] [0 s] n900_vnice_npu_soc.xip on_reset: reset owned threads
[08-01 10:18:10.071] [40 ns] n900_vnice_npu_soc.por_reset reset_run active true
[08-01 10:18:10.072] [40 ns] n900_vnice transport_dbg: addr: 0x0 ,len: 0x2148
[08-01 10:18:10.072] [40 ns] n900_vnice transport_dbg: addr: 0x100000 ,len: 0xe20
[08-01 10:18:10.072] [40 ns] n900_vnice transport_dbg: addr: 0x100e20 ,len: 0x800
[08-01 10:18:10.072] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_address_phase
[08-01 10:18:10.072] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.read_data_phase
[08-01 10:18:10.072] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_address_phase
[08-01 10:18:10.072] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_phase
[08-01 10:18:10.072] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_data_send_phase
[08-01 10:18:10.072] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge resume  thread: n900_vnice_npu_soc.mem__128_axi_m_0_bridge.write_resp_phase
[08-01 10:18:10.072] [40 ns] n900_vnice_npu_soc.mem__128_axi_m_0_bridge on_reset: reset owned threads
[08-01 10:18:10.074] [60 ns] n900_vnice_npu_soc.core_reset reset_run active true
[08-01 10:18:11.179] [14142 ns] monitor: client 0.0.0.0:36462 connected
[08-01 10:18:11.224] [14706 ns] monitor get_sc_timestamp
[08-01 10:18:14.197] [74434 ns] monitor get_sc_timestamp
[08-01 10:18:17.189] [143142 ns] monitor get_sc_timestamp
[08-01 10:18:20.186] [231252 ns] monitor get_sc_timestamp
[08-01 10:18:23.188] [319561 ns] monitor get_sc_timestamp
[08-01 10:18:25.792] [396147 ns] monitor: client 0.0.0.0:36462 connected close!
[08-01 10:18:25.847] [397186 ns] monitor: client 127.0.0.1:40018 connected
[08-01 10:18:25.888] [397186 ns] monitor get_sc_timestamp
