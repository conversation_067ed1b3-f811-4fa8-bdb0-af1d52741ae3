#!/bin/bash
# Wrapper script to handle mosim-top with logging

# Clean old logs
rm -rf ./logs/*

# Set environment
export LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/data/users/jxchen/mosim_workspace/work/.lib

# Run mosim-top with all provided arguments and tee output
$MOSIM_HOME/bin/mosim-top instance -d ./conf -g ./conf/log_config.toml -n n900_vnice_npu_soc "$@" 2>&1 | tee /data/users/jxchen/mosim_workspace/work/python/logs/mosim_output.log