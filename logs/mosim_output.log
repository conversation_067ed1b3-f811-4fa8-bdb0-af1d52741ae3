              Copyright Ⓒ 2023-2025 by Simango Inc,
              ALL RIGHTS RESERVED
           Mosim Core 2.0.0 (429bae2@master) --- Jul  4 2025 16:25:05
           Mosim 2.0.0 (67fec1f5@master) --- Jul  4 2025 16:25:05
[07-31 09:28:41.682] [0 s] loaded library 'libmosim.so'.
[07-31 09:28:41.690] [0 s] loaded library 'libmosim_common_model.so'.
[07-31 09:28:41.719] [0 s] loaded library 'libn900_vnice.so'.
[07-31 09:28:41.766] [0 s] monitor: ---- start telnet service port 7070 ----
top set global quantum sc_time 2 ns
profiling: image not found: ./fw/demo_vnice.elf
/data/users/jxchen/mosim_workspace/mosim_bin/mosim/3rd/nuclei/bin/objdump: './fw/demo_vnice.elf': No such file
[07-31 09:28:42.834] [0 s] n900_vnice_npu_soc.nice_remote_adapter running....
[07-31 09:28:44.025] [17528 ns] Nuclei SDK Build Time: Jul 30 2025, 16:49:31

[07-31 09:28:45.248] [33382 ns] Download Mode: ILM

[07-31 09:28:46.091] [44036 ns] CPU Frequency 162921 Hz

[07-31 09:28:46.929] [54404 ns] CPU HartID: 0

[07-31 09:28:47.393] [60292 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.394] [60292 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30146
[07-31 09:28:47.394] [60292 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.394] [60292 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.394] [60292 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.394] [60292 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.396] [60318 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.397] [60318 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30159
[07-31 09:28:47.398] [60318 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.398] [60318 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.398] [60318 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.398] [60318 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.398] [60326 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.399] [60326 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30163
[07-31 09:28:47.399] [60326 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.399] [60326 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.399] [60326 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.399] [60326 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.400] [60334 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.400] [60334 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30167
[07-31 09:28:47.400] [60334 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.400] [60334 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.400] [60334 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.400] [60334 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.401] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.401] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa807e78b to npu, cycles 30171
[07-31 09:28:47.401] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.401] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30171, check_time is: 60342000
[07-31 09:28:47.401] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.401] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:28:47.401] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:28:47.401] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60342000
[07-31 09:28:47.401] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60342000
[07-31 09:28:47.401] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60342000
[07-31 09:28:47.403] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.403] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30171, check_time is: 60342000
[07-31 09:28:47.403] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.403] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.403] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60342000
[07-31 09:28:47.403] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60342000
[07-31 09:28:47.403] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60342000
[07-31 09:28:47.403] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.403] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30172, check_time is: 60344000
[07-31 09:28:47.403] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.403] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.403] [60342 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60342000
[07-31 09:28:47.403] [60344 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60344000
[07-31 09:28:47.403] [60344 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60344000
[07-31 09:28:47.403] [60344 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.403] [60344 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30188, check_time is: 60376000
[07-31 09:28:47.403] [60344 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.403] [60344 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.403] [60344 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60344000
[07-31 09:28:47.405] [60376 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60376000
[07-31 09:28:47.406] [60376 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60376000
[07-31 09:28:47.406] [60376 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.406] [60376 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30188, check_time is: 60376000
[07-31 09:28:47.406] [60376 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.406] [60376 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.406] [60376 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60376000
[07-31 09:28:47.406] [60376 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60376000
[07-31 09:28:47.406] [60376 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60376000
[07-31 09:28:47.407] [60376 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 13
[07-31 09:28:47.407] [60376 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.407] [60376 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 13, type: INSTR_RSP
[07-31 09:28:47.407] [60376 ns] n900_vnice_npu_soc.nice_remote_adapter  --- nice xd=1, response instr cmd, data: 408bc000
[07-31 09:28:47.413] [60452 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.413] [60452 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30226
[07-31 09:28:47.413] [60452 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.413] [60452 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.413] [60452 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.413] [60452 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.416] [60478 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.416] [60478 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30239
[07-31 09:28:47.416] [60478 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.416] [60478 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.416] [60478 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.416] [60478 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.417] [60486 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.417] [60486 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30243
[07-31 09:28:47.417] [60486 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.417] [60486 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.417] [60486 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.417] [60486 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.418] [60494 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.418] [60494 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30247
[07-31 09:28:47.419] [60494 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.419] [60494 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.419] [60494 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.419] [60494 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.419] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.419] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa807e78b to npu, cycles 30251
[07-31 09:28:47.420] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.420] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30251, check_time is: 60502000
[07-31 09:28:47.420] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.420] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:28:47.420] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:28:47.420] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60502000
[07-31 09:28:47.420] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60502000
[07-31 09:28:47.420] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60502000
[07-31 09:28:47.421] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.421] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30251, check_time is: 60502000
[07-31 09:28:47.421] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.421] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.421] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60502000
[07-31 09:28:47.421] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60502000
[07-31 09:28:47.421] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60502000
[07-31 09:28:47.421] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.421] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30252, check_time is: 60504000
[07-31 09:28:47.421] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.421] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.421] [60502 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60502000
[07-31 09:28:47.421] [60504 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60504000
[07-31 09:28:47.421] [60504 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60504000
[07-31 09:28:47.421] [60504 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.421] [60504 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30268, check_time is: 60536000
[07-31 09:28:47.421] [60504 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.421] [60504 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.421] [60504 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60504000
[07-31 09:28:47.424] [60536 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60536000
[07-31 09:28:47.424] [60536 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60536000
[07-31 09:28:47.424] [60536 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.424] [60536 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30268, check_time is: 60536000
[07-31 09:28:47.424] [60536 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.424] [60536 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.424] [60536 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60536000
[07-31 09:28:47.424] [60536 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60536000
[07-31 09:28:47.424] [60536 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60536000
[07-31 09:28:47.425] [60536 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 13
[07-31 09:28:47.425] [60536 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.425] [60536 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 13, type: INSTR_RSP
[07-31 09:28:47.425] [60536 ns] n900_vnice_npu_soc.nice_remote_adapter  --- nice xd=1, response instr cmd, data: 410a4000
[07-31 09:28:47.429] [60592 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.429] [60592 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30296
[07-31 09:28:47.429] [60592 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.429] [60592 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.429] [60592 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.429] [60592 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.432] [60618 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.432] [60618 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30309
[07-31 09:28:47.432] [60618 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.432] [60618 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.432] [60618 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.432] [60618 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.433] [60626 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.433] [60626 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30313
[07-31 09:28:47.433] [60626 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.433] [60626 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.433] [60626 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.433] [60626 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.434] [60634 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.434] [60634 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30317
[07-31 09:28:47.434] [60634 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.434] [60634 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.434] [60634 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.434] [60634 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.435] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.435] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa807e78b to npu, cycles 30321
[07-31 09:28:47.435] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.435] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30321, check_time is: 60642000
[07-31 09:28:47.435] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.435] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:28:47.435] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:28:47.435] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60642000
[07-31 09:28:47.435] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60642000
[07-31 09:28:47.435] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60642000
[07-31 09:28:47.437] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.437] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30321, check_time is: 60642000
[07-31 09:28:47.437] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.437] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.437] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60642000
[07-31 09:28:47.437] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60642000
[07-31 09:28:47.437] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60642000
[07-31 09:28:47.437] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.437] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30322, check_time is: 60644000
[07-31 09:28:47.437] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.437] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.437] [60642 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60642000
[07-31 09:28:47.437] [60644 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60644000
[07-31 09:28:47.437] [60644 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60644000
[07-31 09:28:47.438] [60644 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.438] [60644 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30338, check_time is: 60676000
[07-31 09:28:47.438] [60644 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.438] [60644 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.438] [60644 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60644000
[07-31 09:28:47.440] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60676000
[07-31 09:28:47.440] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60676000
[07-31 09:28:47.440] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.440] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30338, check_time is: 60676000
[07-31 09:28:47.440] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.440] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.440] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60676000
[07-31 09:28:47.440] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60676000
[07-31 09:28:47.440] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60676000
[07-31 09:28:47.441] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 13
[07-31 09:28:47.441] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.441] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 13, type: INSTR_RSP
[07-31 09:28:47.441] [60676 ns] n900_vnice_npu_soc.nice_remote_adapter  --- nice xd=1, response instr cmd, data: 40f08000
[07-31 09:28:47.445] [60732 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.446] [60732 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30366
[07-31 09:28:47.446] [60732 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.446] [60732 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.446] [60732 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.446] [60732 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.448] [60758 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.448] [60758 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30379
[07-31 09:28:47.449] [60758 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.449] [60758 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.449] [60758 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.449] [60758 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.449] [60766 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.449] [60766 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30383
[07-31 09:28:47.450] [60766 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.450] [60766 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.450] [60766 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.450] [60766 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.450] [60774 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.450] [60774 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30387
[07-31 09:28:47.451] [60774 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.451] [60774 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.451] [60774 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.451] [60774 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.451] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.452] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa807e78b to npu, cycles 30391
[07-31 09:28:47.452] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.452] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30391, check_time is: 60782000
[07-31 09:28:47.452] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.452] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:28:47.452] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:28:47.452] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60782000
[07-31 09:28:47.452] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60782000
[07-31 09:28:47.452] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60782000
[07-31 09:28:47.453] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.453] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30391, check_time is: 60782000
[07-31 09:28:47.453] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.453] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.453] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60782000
[07-31 09:28:47.453] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60782000
[07-31 09:28:47.453] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60782000
[07-31 09:28:47.453] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.453] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30392, check_time is: 60784000
[07-31 09:28:47.453] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.453] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.453] [60782 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60782000
[07-31 09:28:47.454] [60784 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60784000
[07-31 09:28:47.454] [60784 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60784000
[07-31 09:28:47.454] [60784 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.454] [60784 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30408, check_time is: 60816000
[07-31 09:28:47.454] [60784 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.454] [60784 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.454] [60784 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60784000
[07-31 09:28:47.456] [60816 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60816000
[07-31 09:28:47.456] [60816 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60816000
[07-31 09:28:47.456] [60816 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.456] [60816 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30408, check_time is: 60816000
[07-31 09:28:47.456] [60816 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.456] [60816 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.456] [60816 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60816000
[07-31 09:28:47.456] [60816 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60816000
[07-31 09:28:47.456] [60816 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60816000
[07-31 09:28:47.457] [60816 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 13
[07-31 09:28:47.457] [60816 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.457] [60816 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 13, type: INSTR_RSP
[07-31 09:28:47.457] [60816 ns] n900_vnice_npu_soc.nice_remote_adapter  --- nice xd=1, response instr cmd, data: 3fdd0000
[07-31 09:28:47.467] [60940 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.467] [60940 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30470
[07-31 09:28:47.468] [60940 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.468] [60940 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.468] [60940 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.468] [60940 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.470] [60966 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.471] [60966 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30483
[07-31 09:28:47.472] [60966 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.472] [60966 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.472] [60966 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.472] [60966 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.472] [60974 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.473] [60974 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30487
[07-31 09:28:47.473] [60974 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.473] [60974 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.473] [60974 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.473] [60974 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.474] [60982 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.475] [60982 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30491
[07-31 09:28:47.475] [60982 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.475] [60982 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.475] [60982 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.475] [60982 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.476] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.477] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa807e78b to npu, cycles 30495
[07-31 09:28:47.477] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.477] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30495, check_time is: 60990000
[07-31 09:28:47.477] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.477] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:28:47.477] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:28:47.477] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60990000
[07-31 09:28:47.477] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60990000
[07-31 09:28:47.477] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60990000
[07-31 09:28:47.478] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.478] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30495, check_time is: 60990000
[07-31 09:28:47.478] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.478] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.478] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60990000
[07-31 09:28:47.478] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60990000
[07-31 09:28:47.478] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60990000
[07-31 09:28:47.478] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.478] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30496, check_time is: 60992000
[07-31 09:28:47.478] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.478] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.478] [60990 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60990000
[07-31 09:28:47.479] [60992 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 60992000
[07-31 09:28:47.479] [60992 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 60992000
[07-31 09:28:47.479] [60992 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.479] [60992 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30512, check_time is: 61024000
[07-31 09:28:47.479] [60992 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.479] [60992 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.479] [60992 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 60992000
[07-31 09:28:47.483] [61024 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61024000
[07-31 09:28:47.483] [61024 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61024000
[07-31 09:28:47.483] [61024 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.483] [61024 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30512, check_time is: 61024000
[07-31 09:28:47.483] [61024 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.483] [61024 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.483] [61024 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61024000
[07-31 09:28:47.483] [61024 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61024000
[07-31 09:28:47.483] [61024 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61024000
[07-31 09:28:47.484] [61024 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 13
[07-31 09:28:47.484] [61024 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.484] [61024 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 13, type: INSTR_RSP
[07-31 09:28:47.484] [61024 ns] n900_vnice_npu_soc.nice_remote_adapter  --- nice xd=1, response instr cmd, data: bffe0000
[07-31 09:28:47.488] [61080 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.489] [61080 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30540
[07-31 09:28:47.489] [61080 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.489] [61080 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.489] [61080 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.489] [61080 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.492] [61106 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.492] [61106 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30553
[07-31 09:28:47.492] [61106 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.492] [61106 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.492] [61106 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.492] [61106 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.493] [61114 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.493] [61114 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30557
[07-31 09:28:47.494] [61114 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.494] [61114 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.494] [61114 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.494] [61114 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.494] [61122 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.495] [61122 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30561
[07-31 09:28:47.495] [61122 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.495] [61122 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.495] [61122 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.495] [61122 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.496] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.496] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa807e78b to npu, cycles 30565
[07-31 09:28:47.496] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.496] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30565, check_time is: 61130000
[07-31 09:28:47.496] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.496] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:28:47.496] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:28:47.496] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61130000
[07-31 09:28:47.496] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61130000
[07-31 09:28:47.496] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61130000
[07-31 09:28:47.497] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.497] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30565, check_time is: 61130000
[07-31 09:28:47.497] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.497] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.497] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61130000
[07-31 09:28:47.497] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61130000
[07-31 09:28:47.497] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61130000
[07-31 09:28:47.497] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.497] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30566, check_time is: 61132000
[07-31 09:28:47.497] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.497] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.497] [61130 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61130000
[07-31 09:28:47.498] [61132 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61132000
[07-31 09:28:47.498] [61132 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61132000
[07-31 09:28:47.498] [61132 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.498] [61132 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30582, check_time is: 61164000
[07-31 09:28:47.498] [61132 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.498] [61132 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.498] [61132 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61132000
[07-31 09:28:47.500] [61164 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61164000
[07-31 09:28:47.500] [61164 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61164000
[07-31 09:28:47.500] [61164 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.500] [61164 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30582, check_time is: 61164000
[07-31 09:28:47.500] [61164 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.500] [61164 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.500] [61164 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61164000
[07-31 09:28:47.500] [61164 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61164000
[07-31 09:28:47.500] [61164 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61164000
[07-31 09:28:47.501] [61164 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 13
[07-31 09:28:47.501] [61164 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.501] [61164 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 13, type: INSTR_RSP
[07-31 09:28:47.501] [61164 ns] n900_vnice_npu_soc.nice_remote_adapter  --- nice xd=1, response instr cmd, data: 40580000
[07-31 09:28:47.506] [61220 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.506] [61220 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30610
[07-31 09:28:47.506] [61220 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.506] [61220 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.506] [61220 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.506] [61220 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.510] [61246 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.511] [61246 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30623
[07-31 09:28:47.511] [61246 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.511] [61246 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.511] [61246 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.511] [61246 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.512] [61254 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.512] [61254 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30627
[07-31 09:28:47.512] [61254 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.512] [61254 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.512] [61254 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.512] [61254 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.513] [61262 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.513] [61262 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30631
[07-31 09:28:47.513] [61262 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.513] [61262 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.513] [61262 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.513] [61262 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.514] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.514] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa807e78b to npu, cycles 30635
[07-31 09:28:47.515] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.515] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30635, check_time is: 61270000
[07-31 09:28:47.515] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.515] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:28:47.515] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:28:47.515] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61270000
[07-31 09:28:47.515] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61270000
[07-31 09:28:47.515] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61270000
[07-31 09:28:47.516] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.516] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30635, check_time is: 61270000
[07-31 09:28:47.516] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.516] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.516] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61270000
[07-31 09:28:47.516] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61270000
[07-31 09:28:47.516] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61270000
[07-31 09:28:47.516] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.516] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30636, check_time is: 61272000
[07-31 09:28:47.516] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.516] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.516] [61270 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61270000
[07-31 09:28:47.516] [61272 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61272000
[07-31 09:28:47.516] [61272 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61272000
[07-31 09:28:47.516] [61272 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.516] [61272 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30652, check_time is: 61304000
[07-31 09:28:47.516] [61272 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.516] [61272 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.516] [61272 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61272000
[07-31 09:28:47.519] [61304 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61304000
[07-31 09:28:47.519] [61304 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61304000
[07-31 09:28:47.519] [61304 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.519] [61304 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30652, check_time is: 61304000
[07-31 09:28:47.519] [61304 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.519] [61304 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.519] [61304 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61304000
[07-31 09:28:47.519] [61304 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61304000
[07-31 09:28:47.519] [61304 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61304000
[07-31 09:28:47.520] [61304 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 13
[07-31 09:28:47.520] [61304 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.520] [61304 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 13, type: INSTR_RSP
[07-31 09:28:47.520] [61304 ns] n900_vnice_npu_soc.nice_remote_adapter  --- nice xd=1, response instr cmd, data: c0974000
[07-31 09:28:47.525] [61360 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.525] [61360 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30680
[07-31 09:28:47.525] [61360 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.525] [61360 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.525] [61360 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.525] [61360 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.528] [61386 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.528] [61386 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30693
[07-31 09:28:47.528] [61386 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.528] [61386 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.528] [61386 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.528] [61386 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.529] [61394 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.529] [61394 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30697
[07-31 09:28:47.530] [61394 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.530] [61394 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.530] [61394 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.530] [61394 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.530] [61402 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.530] [61402 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30701
[07-31 09:28:47.531] [61402 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.531] [61402 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.531] [61402 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.531] [61402 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.531] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.532] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa807e78b to npu, cycles 30705
[07-31 09:28:47.532] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.532] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30705, check_time is: 61410000
[07-31 09:28:47.532] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.532] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:28:47.532] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:28:47.532] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61410000
[07-31 09:28:47.532] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61410000
[07-31 09:28:47.532] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61410000
[07-31 09:28:47.533] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.533] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30705, check_time is: 61410000
[07-31 09:28:47.533] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.533] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.533] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61410000
[07-31 09:28:47.533] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61410000
[07-31 09:28:47.533] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61410000
[07-31 09:28:47.533] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.533] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30706, check_time is: 61412000
[07-31 09:28:47.533] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.533] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.533] [61410 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61410000
[07-31 09:28:47.533] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61412000
[07-31 09:28:47.533] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61412000
[07-31 09:28:47.534] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.534] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30722, check_time is: 61444000
[07-31 09:28:47.534] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.534] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.534] [61412 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61412000
[07-31 09:28:47.536] [61444 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61444000
[07-31 09:28:47.536] [61444 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61444000
[07-31 09:28:47.536] [61444 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.536] [61444 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30722, check_time is: 61444000
[07-31 09:28:47.536] [61444 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.536] [61444 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.536] [61444 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61444000
[07-31 09:28:47.536] [61444 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61444000
[07-31 09:28:47.536] [61444 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61444000
[07-31 09:28:47.537] [61444 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 13
[07-31 09:28:47.537] [61444 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.537] [61444 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 13, type: INSTR_RSP
[07-31 09:28:47.537] [61444 ns] n900_vnice_npu_soc.nice_remote_adapter  --- nice xd=1, response instr cmd, data: 407e0000
[07-31 09:28:47.546] [61550 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.547] [61550 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30775
[07-31 09:28:47.547] [61550 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.547] [61550 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.547] [61550 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.547] [61550 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.549] [61576 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.550] [61576 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30788
[07-31 09:28:47.550] [61576 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.550] [61576 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.550] [61576 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.550] [61576 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.551] [61584 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.551] [61584 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30792
[07-31 09:28:47.551] [61584 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.551] [61584 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.551] [61584 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.551] [61584 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.552] [61592 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.552] [61592 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30796
[07-31 09:28:47.553] [61592 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.553] [61592 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.553] [61592 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.553] [61592 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.553] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.554] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa807e78b to npu, cycles 30800
[07-31 09:28:47.554] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.554] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30800, check_time is: 61600000
[07-31 09:28:47.554] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.554] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:28:47.554] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:28:47.554] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61600000
[07-31 09:28:47.554] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61600000
[07-31 09:28:47.554] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61600000
[07-31 09:28:47.555] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.555] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30800, check_time is: 61600000
[07-31 09:28:47.555] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.555] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.555] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61600000
[07-31 09:28:47.555] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61600000
[07-31 09:28:47.555] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61600000
[07-31 09:28:47.555] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.555] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30801, check_time is: 61602000
[07-31 09:28:47.555] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.555] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.555] [61600 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61600000
[07-31 09:28:47.556] [61602 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61602000
[07-31 09:28:47.556] [61602 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61602000
[07-31 09:28:47.556] [61602 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.556] [61602 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30817, check_time is: 61634000
[07-31 09:28:47.556] [61602 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.556] [61602 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.556] [61602 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61602000
[07-31 09:28:47.558] [61634 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61634000
[07-31 09:28:47.558] [61634 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61634000
[07-31 09:28:47.559] [61634 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.559] [61634 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30817, check_time is: 61634000
[07-31 09:28:47.559] [61634 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.559] [61634 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.559] [61634 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61634000
[07-31 09:28:47.559] [61634 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61634000
[07-31 09:28:47.559] [61634 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61634000
[07-31 09:28:47.559] [61634 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 13
[07-31 09:28:47.559] [61634 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.559] [61634 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 13, type: INSTR_RSP
[07-31 09:28:47.559] [61634 ns] n900_vnice_npu_soc.nice_remote_adapter  --- nice xd=1, response instr cmd, data: 3ffa0000
[07-31 09:28:47.564] [61690 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.564] [61690 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30845
[07-31 09:28:47.565] [61690 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.565] [61690 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.565] [61690 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.565] [61690 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.567] [61716 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.568] [61716 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30858
[07-31 09:28:47.568] [61716 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.568] [61716 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.568] [61716 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.568] [61716 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.569] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.569] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30862
[07-31 09:28:47.569] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.569] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.569] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.569] [61724 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.570] [61732 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.570] [61732 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30866
[07-31 09:28:47.571] [61732 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.571] [61732 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.571] [61732 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.571] [61732 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.572] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.572] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa807e78b to npu, cycles 30870
[07-31 09:28:47.572] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.572] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30870, check_time is: 61740000
[07-31 09:28:47.572] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.572] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:28:47.572] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:28:47.572] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61740000
[07-31 09:28:47.572] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61740000
[07-31 09:28:47.572] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61740000
[07-31 09:28:47.573] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.573] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30870, check_time is: 61740000
[07-31 09:28:47.573] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.573] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.573] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61740000
[07-31 09:28:47.573] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61740000
[07-31 09:28:47.574] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61740000
[07-31 09:28:47.574] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.574] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30871, check_time is: 61742000
[07-31 09:28:47.574] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.574] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.574] [61740 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61740000
[07-31 09:28:47.574] [61742 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61742000
[07-31 09:28:47.574] [61742 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61742000
[07-31 09:28:47.574] [61742 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.574] [61742 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30887, check_time is: 61774000
[07-31 09:28:47.574] [61742 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.574] [61742 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.574] [61742 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61742000
[07-31 09:28:47.577] [61774 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61774000
[07-31 09:28:47.577] [61774 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61774000
[07-31 09:28:47.577] [61774 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.577] [61774 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30887, check_time is: 61774000
[07-31 09:28:47.577] [61774 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.577] [61774 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.577] [61774 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61774000
[07-31 09:28:47.577] [61774 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61774000
[07-31 09:28:47.577] [61774 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61774000
[07-31 09:28:47.577] [61774 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 13
[07-31 09:28:47.577] [61774 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.577] [61774 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 13, type: INSTR_RSP
[07-31 09:28:47.577] [61774 ns] n900_vnice_npu_soc.nice_remote_adapter  --- nice xd=1, response instr cmd, data: 408a0000
[07-31 09:28:47.582] [61830 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.582] [61830 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30915
[07-31 09:28:47.582] [61830 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.582] [61830 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.582] [61830 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.582] [61830 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.585] [61856 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.585] [61856 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30928
[07-31 09:28:47.586] [61856 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.586] [61856 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.586] [61856 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.586] [61856 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.586] [61864 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.587] [61864 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30932
[07-31 09:28:47.587] [61864 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.587] [61864 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.587] [61864 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.587] [61864 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.587] [61872 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.588] [61872 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30936
[07-31 09:28:47.588] [61872 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.588] [61872 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.588] [61872 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.588] [61872 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.589] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.589] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa807e78b to npu, cycles 30940
[07-31 09:28:47.589] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.589] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30940, check_time is: 61880000
[07-31 09:28:47.589] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.589] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:28:47.589] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:28:47.589] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61880000
[07-31 09:28:47.589] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61880000
[07-31 09:28:47.589] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61880000
[07-31 09:28:47.591] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.591] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30940, check_time is: 61880000
[07-31 09:28:47.591] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.591] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.591] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61880000
[07-31 09:28:47.591] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61880000
[07-31 09:28:47.591] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61880000
[07-31 09:28:47.591] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.591] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30941, check_time is: 61882000
[07-31 09:28:47.591] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.591] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.591] [61880 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61880000
[07-31 09:28:47.591] [61882 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61882000
[07-31 09:28:47.591] [61882 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61882000
[07-31 09:28:47.591] [61882 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.591] [61882 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30957, check_time is: 61914000
[07-31 09:28:47.591] [61882 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.591] [61882 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.591] [61882 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61882000
[07-31 09:28:47.594] [61914 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61914000
[07-31 09:28:47.594] [61914 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61914000
[07-31 09:28:47.594] [61914 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.594] [61914 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 30957, check_time is: 61914000
[07-31 09:28:47.594] [61914 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.594] [61914 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.594] [61914 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 61914000
[07-31 09:28:47.594] [61914 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 61914000
[07-31 09:28:47.594] [61914 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 61914000
[07-31 09:28:47.595] [61914 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 13
[07-31 09:28:47.595] [61914 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.595] [61914 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 13, type: INSTR_RSP
[07-31 09:28:47.595] [61914 ns] n900_vnice_npu_soc.nice_remote_adapter  --- nice xd=1, response instr cmd, data: 41272000
[07-31 09:28:47.599] [61970 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.599] [61970 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 30985
[07-31 09:28:47.600] [61970 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.600] [61970 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.600] [61970 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.600] [61970 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.602] [61996 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.602] [61996 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 30998
[07-31 09:28:47.603] [61996 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.603] [61996 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.603] [61996 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.603] [61996 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.603] [62004 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.604] [62004 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31002
[07-31 09:28:47.604] [62004 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.604] [62004 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.604] [62004 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.604] [62004 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.605] [62012 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.605] [62012 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31006
[07-31 09:28:47.605] [62012 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.605] [62012 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.605] [62012 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.605] [62012 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.606] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.606] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa807e78b to npu, cycles 31010
[07-31 09:28:47.606] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.606] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31010, check_time is: 62020000
[07-31 09:28:47.606] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.606] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:28:47.606] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:28:47.606] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62020000
[07-31 09:28:47.606] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62020000
[07-31 09:28:47.606] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62020000
[07-31 09:28:47.608] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.608] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31010, check_time is: 62020000
[07-31 09:28:47.608] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.608] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.608] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62020000
[07-31 09:28:47.608] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62020000
[07-31 09:28:47.608] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62020000
[07-31 09:28:47.608] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.608] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31011, check_time is: 62022000
[07-31 09:28:47.608] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.608] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.608] [62020 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62020000
[07-31 09:28:47.608] [62022 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62022000
[07-31 09:28:47.608] [62022 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62022000
[07-31 09:28:47.608] [62022 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.608] [62022 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31027, check_time is: 62054000
[07-31 09:28:47.608] [62022 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.608] [62022 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.608] [62022 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62022000
[07-31 09:28:47.611] [62054 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62054000
[07-31 09:28:47.611] [62054 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62054000
[07-31 09:28:47.611] [62054 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.611] [62054 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31027, check_time is: 62054000
[07-31 09:28:47.611] [62054 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.611] [62054 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.611] [62054 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62054000
[07-31 09:28:47.611] [62054 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62054000
[07-31 09:28:47.611] [62054 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62054000
[07-31 09:28:47.612] [62054 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 13
[07-31 09:28:47.612] [62054 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.612] [62054 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 13, type: INSTR_RSP
[07-31 09:28:47.612] [62054 ns] n900_vnice_npu_soc.nice_remote_adapter  --- nice xd=1, response instr cmd, data: 41810000
[07-31 09:28:47.620] [62160 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.621] [62160 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 31080
[07-31 09:28:47.621] [62160 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.621] [62160 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.621] [62160 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.621] [62160 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.623] [62186 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.624] [62186 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31093
[07-31 09:28:47.624] [62186 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.624] [62186 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.624] [62186 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.624] [62186 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.624] [62194 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.625] [62194 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31097
[07-31 09:28:47.625] [62194 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.625] [62194 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.625] [62194 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.625] [62194 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.626] [62202 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.626] [62202 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31101
[07-31 09:28:47.626] [62202 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.626] [62202 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.626] [62202 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.626] [62202 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.627] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.627] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa807e78b to npu, cycles 31105
[07-31 09:28:47.627] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.627] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31105, check_time is: 62210000
[07-31 09:28:47.627] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.627] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:28:47.627] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:28:47.627] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62210000
[07-31 09:28:47.627] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62210000
[07-31 09:28:47.627] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62210000
[07-31 09:28:47.629] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.629] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31105, check_time is: 62210000
[07-31 09:28:47.629] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.629] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.629] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62210000
[07-31 09:28:47.629] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62210000
[07-31 09:28:47.629] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62210000
[07-31 09:28:47.629] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.629] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31106, check_time is: 62212000
[07-31 09:28:47.629] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.629] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.629] [62210 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62210000
[07-31 09:28:47.630] [62212 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62212000
[07-31 09:28:47.630] [62212 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62212000
[07-31 09:28:47.630] [62212 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.630] [62212 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31122, check_time is: 62244000
[07-31 09:28:47.630] [62212 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.630] [62212 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.630] [62212 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62212000
[07-31 09:28:47.632] [62244 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62244000
[07-31 09:28:47.632] [62244 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62244000
[07-31 09:28:47.632] [62244 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.632] [62244 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31122, check_time is: 62244000
[07-31 09:28:47.632] [62244 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.632] [62244 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.632] [62244 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62244000
[07-31 09:28:47.632] [62244 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62244000
[07-31 09:28:47.632] [62244 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62244000
[07-31 09:28:47.634] [62244 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 13
[07-31 09:28:47.634] [62244 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.634] [62244 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 13, type: INSTR_RSP
[07-31 09:28:47.634] [62244 ns] n900_vnice_npu_soc.nice_remote_adapter  --- nice xd=1, response instr cmd, data: c11ba000
[07-31 09:28:47.638] [62300 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.638] [62300 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 31150
[07-31 09:28:47.639] [62300 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.639] [62300 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.639] [62300 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.639] [62300 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.641] [62326 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.642] [62326 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31163
[07-31 09:28:47.642] [62326 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.642] [62326 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.642] [62326 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.642] [62326 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.643] [62334 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.643] [62334 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31167
[07-31 09:28:47.643] [62334 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.643] [62334 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.643] [62334 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.643] [62334 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.644] [62342 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.644] [62342 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31171
[07-31 09:28:47.644] [62342 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.644] [62342 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.644] [62342 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.644] [62342 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.645] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.645] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa807e78b to npu, cycles 31175
[07-31 09:28:47.645] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.645] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31175, check_time is: 62350000
[07-31 09:28:47.645] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.645] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:28:47.645] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:28:47.645] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62350000
[07-31 09:28:47.645] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62350000
[07-31 09:28:47.645] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62350000
[07-31 09:28:47.647] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.647] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31175, check_time is: 62350000
[07-31 09:28:47.647] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.647] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.647] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62350000
[07-31 09:28:47.647] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62350000
[07-31 09:28:47.647] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62350000
[07-31 09:28:47.647] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.647] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31176, check_time is: 62352000
[07-31 09:28:47.647] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.647] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.647] [62350 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62350000
[07-31 09:28:47.648] [62352 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62352000
[07-31 09:28:47.648] [62352 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62352000
[07-31 09:28:47.648] [62352 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.648] [62352 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31192, check_time is: 62384000
[07-31 09:28:47.648] [62352 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.648] [62352 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.648] [62352 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62352000
[07-31 09:28:47.650] [62384 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62384000
[07-31 09:28:47.650] [62384 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62384000
[07-31 09:28:47.650] [62384 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.650] [62384 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31192, check_time is: 62384000
[07-31 09:28:47.650] [62384 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.650] [62384 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.650] [62384 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62384000
[07-31 09:28:47.650] [62384 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62384000
[07-31 09:28:47.650] [62384 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62384000
[07-31 09:28:47.651] [62384 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 13
[07-31 09:28:47.651] [62384 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.651] [62384 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 13, type: INSTR_RSP
[07-31 09:28:47.651] [62384 ns] n900_vnice_npu_soc.nice_remote_adapter  --- nice xd=1, response instr cmd, data: 412f6000
[07-31 09:28:47.656] [62440 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.656] [62440 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 31220
[07-31 09:28:47.656] [62440 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.656] [62440 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.656] [62440 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.656] [62440 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.658] [62466 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.659] [62466 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31233
[07-31 09:28:47.659] [62466 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.659] [62466 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.659] [62466 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.659] [62466 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.660] [62474 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.660] [62474 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31237
[07-31 09:28:47.660] [62474 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.660] [62474 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.660] [62474 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.660] [62474 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.661] [62482 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.661] [62482 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31241
[07-31 09:28:47.661] [62482 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.661] [62482 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.661] [62482 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.661] [62482 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.662] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.662] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa807e78b to npu, cycles 31245
[07-31 09:28:47.662] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.662] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31245, check_time is: 62490000
[07-31 09:28:47.662] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.662] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:28:47.662] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:28:47.662] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62490000
[07-31 09:28:47.662] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62490000
[07-31 09:28:47.662] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62490000
[07-31 09:28:47.664] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.664] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31245, check_time is: 62490000
[07-31 09:28:47.664] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.664] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.664] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62490000
[07-31 09:28:47.664] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62490000
[07-31 09:28:47.664] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62490000
[07-31 09:28:47.664] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.664] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31246, check_time is: 62492000
[07-31 09:28:47.664] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.664] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.664] [62490 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62490000
[07-31 09:28:47.664] [62492 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62492000
[07-31 09:28:47.664] [62492 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62492000
[07-31 09:28:47.664] [62492 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.664] [62492 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31262, check_time is: 62524000
[07-31 09:28:47.664] [62492 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.664] [62492 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.664] [62492 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62492000
[07-31 09:28:47.667] [62524 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62524000
[07-31 09:28:47.667] [62524 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62524000
[07-31 09:28:47.667] [62524 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.667] [62524 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31262, check_time is: 62524000
[07-31 09:28:47.667] [62524 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.667] [62524 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.667] [62524 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62524000
[07-31 09:28:47.667] [62524 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62524000
[07-31 09:28:47.667] [62524 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62524000
[07-31 09:28:47.668] [62524 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 13
[07-31 09:28:47.668] [62524 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.668] [62524 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 13, type: INSTR_RSP
[07-31 09:28:47.668] [62524 ns] n900_vnice_npu_soc.nice_remote_adapter  --- nice xd=1, response instr cmd, data: 40288000
[07-31 09:28:47.672] [62580 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.672] [62580 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xe7b00b to npu, cycles 31290
[07-31 09:28:47.672] [62580 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.672] [62580 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.672] [62580 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.673] [62580 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.675] [62606 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.675] [62606 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31303
[07-31 09:28:47.675] [62606 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.675] [62606 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.675] [62606 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.675] [62606 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.676] [62614 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.676] [62614 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31307
[07-31 09:28:47.677] [62614 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.677] [62614 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.677] [62614 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.677] [62614 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.677] [62622 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.677] [62622 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0x2e7b00b to npu, cycles 31311
[07-31 09:28:47.678] [62622 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.678] [62622 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.678] [62622 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.678] [62622 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:47.678] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:47.679] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa807e78b to npu, cycles 31315
[07-31 09:28:47.679] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.679] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31315, check_time is: 62630000
[07-31 09:28:47.679] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.679] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:28:47.679] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:28:47.679] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62630000
[07-31 09:28:47.679] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62630000
[07-31 09:28:47.679] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62630000
[07-31 09:28:47.680] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.680] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31315, check_time is: 62630000
[07-31 09:28:47.680] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.680] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.680] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62630000
[07-31 09:28:47.680] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62630000
[07-31 09:28:47.680] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62630000
[07-31 09:28:47.680] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.680] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31316, check_time is: 62632000
[07-31 09:28:47.680] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.680] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.680] [62630 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62630000
[07-31 09:28:47.681] [62632 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62632000
[07-31 09:28:47.681] [62632 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62632000
[07-31 09:28:47.681] [62632 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.681] [62632 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31332, check_time is: 62664000
[07-31 09:28:47.681] [62632 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.681] [62632 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.681] [62632 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62632000
[07-31 09:28:47.683] [62664 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62664000
[07-31 09:28:47.683] [62664 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62664000
[07-31 09:28:47.683] [62664 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:47.683] [62664 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 31332, check_time is: 62664000
[07-31 09:28:47.683] [62664 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:47.683] [62664 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:47.683] [62664 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 62664000
[07-31 09:28:47.683] [62664 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 62664000
[07-31 09:28:47.683] [62664 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 62664000
[07-31 09:28:47.684] [62664 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 13
[07-31 09:28:47.684] [62664 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:47.684] [62664 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 13, type: INSTR_RSP
[07-31 09:28:47.684] [62664 ns] n900_vnice_npu_soc.nice_remote_adapter  --- nice xd=1, response instr cmd, data: 40460000
[07-31 09:28:47.775] [63812 ns] ret_vals = 
[07-31 09:28:48.178] [69020 ns] 0x408bc000
[07-31 09:28:48.533] [73572 ns] 0x410a4000
[07-31 09:28:48.862] [77704 ns] 0x40f08000
[07-31 09:28:49.167] [81646 ns] 0x3fdd0000
[07-31 09:28:49.467] [85542 ns] 0xbffe0000
[07-31 09:28:49.769] [89438 ns] 0x40580000
[07-31 09:28:50.077] [93312 ns] 0xc0974000
[07-31 09:28:50.379] [97186 ns] 0x407e0000
[07-31 09:28:50.678] [101060 ns] 0x3ffa0000
[07-31 09:28:50.976] [104934 ns] 0x408a0000
[07-31 09:28:51.274] [108808 ns] 0x41272000
[07-31 09:28:51.583] [112678 ns] 0x41810000
[07-31 09:28:51.881] [116552 ns] 0xc11ba000
[07-31 09:28:52.180] [120426 ns] 0x412f6000
[07-31 09:28:52.479] [124300 ns] 0x40288000
[07-31 09:28:52.793] [128174 ns] 0x40460000
[07-31 09:28:52.944] [130138 ns] 
[07-31 09:28:52.954] [130266 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:52.955] [130266 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xa00478b to npu, cycles 65133
[07-31 09:28:52.956] [130266 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:52.956] [130266 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 65133, check_time is: 130266000
[07-31 09:28:52.956] [130266 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:52.956] [130266 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by nice_instr
[07-31 09:28:52.956] [130266 ns] n900_vnice_npu_soc.nice_remote_adapter ---check_time_thread
[07-31 09:28:52.956] [130266 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 130266000
[07-31 09:28:52.956] [130266 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time over! at systemc_time ps: 130266000
[07-31 09:28:52.956] [130266 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send check at time ps: 130266000
[07-31 09:28:52.956] [130266 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:52.956] [130266 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 1410130541, check_time is: 2820261082000
[07-31 09:28:52.956] [130266 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:52.956] [130266 ns] n900_vnice_npu_soc.nice_remote_adapter --- check_time_event.notify by check_req
[07-31 09:28:52.956] [130266 ns] n900_vnice_npu_soc.nice_remote_adapter ---wait for check time start! at systemc_time ps: 130266000
[07-31 09:28:52.958] [130282 ns] n900_vnice_npu_soc.nice_remote_adapter has nice req, ---> sending ready request and waiting npu ready ...
[07-31 09:28:52.958] [130282 ns] n900_vnice_npu_soc.nice_remote_adapter ---> send nice instr: 0xfe00000b to npu, cycles 65141
[07-31 09:28:52.959] [130282 ns] n900_vnice_npu_soc.nice_remote_adapter <--- receive npu response, length: 9
[07-31 09:28:52.959] [130282 ns] n900_vnice_npu_soc.nice_remote_adapter    check_time_cycles: 0, check_time is: 0
[07-31 09:28:52.959] [130282 ns] n900_vnice_npu_soc.nice_remote_adapter    length: 9, type: NULL
[07-31 09:28:52.959] [130282 ns] n900_vnice_npu_soc.nice_remote_adapter --- nice xd=0, response 0 to cpu
[07-31 09:28:52.959] [130284 ns] n900_vnice_npu_soc.nice_remote_adapter ---check time updated to 0, over! at systemc_time ps: 130284000
[07-31 09:28:53.033] [131190 ns] All tests finished
[07-31 09:28:53.266] [134204 ns]           
[07-31 09:28:59.366] [237503 ns] monitor: client 0.0.0.0:40756 connected close!

Info: /OSCI/SystemC: Simulation stopped by user.
Elapsed time: 17.6277 s
6748.98 cycles/s
