[07-29 14:38:13.423] [info] work/n900_vnice_npu_soc {"type":"command","command":"dispose"}
[07-30 14:29:50.860] [info] work/n900_vnice_npu_soc {"type":"command","command":"pageLoaded"}
[07-30 14:30:01.623] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-30 14:30:01.749] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTop<PERSON>son","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 14:30:01.910] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTop<PERSON>son","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 14:30:01.914] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[07-30 14:34:03.314] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":true,"task":"n900_vnice_npu_soc"}}
[07-30 14:34:04.733] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 14:34:04.951] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-30 14:34:05.071] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 14:34:05.234] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[07-30 14:35:27.655] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 14:35:29.130] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":true,"task":"n900_vnice_npu_soc"}}
[07-30 15:02:48.452] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-30 15:02:48.694] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 15:02:48.741] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[07-30 15:03:23.622] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":true,"task":"n900_vnice_npu_soc"}}
[07-30 15:03:25.731] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 15:28:03.479] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 15:28:04.950] [info] work/n900_vnice_npu_soc {"type":"command","command":"dispose"}
[07-30 16:51:25.344] [info] work/n900_vnice_npu_soc {"type":"command","command":"pageLoaded"}
[07-30 16:51:33.805] [info] work/n900_vnice_npu_soc {"type":"command","command":"dispose"}
[07-30 16:51:35.665] [info] work/n900_vnice_npu_soc {"type":"command","command":"pageLoaded"}
[07-30 16:51:44.445] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 16:52:44.242] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-30 16:52:44.397] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 16:52:44.573] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 16:52:44.578] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[07-30 16:53:02.840] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 16:53:10.092] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 16:53:10.361] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":true,"task":"n900_vnice_npu_soc"}}
[07-30 16:53:50.234] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-30 16:53:50.401] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 16:53:50.628] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[07-30 16:54:08.994] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 16:54:24.268] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":true,"task":"n900_vnice_npu_soc"}}
[07-30 16:54:25.840] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 16:54:27.451] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-30 16:54:27.556] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 16:54:27.705] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[07-30 16:54:28.991] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 16:54:42.025] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":true,"task":"n900_vnice_npu_soc"}}
[07-30 16:56:16.888] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 16:56:19.410] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 16:57:25.468] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 16:57:38.995] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-30 16:57:39.326] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 16:57:52.498] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 16:58:56.196] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 16:59:08.222] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 16:59:08.253] [info] work/n900_vnice_npu_soc {"type":"command","command":"dispose"}
[07-30 16:59:17.564] [info] work/n900_vnice_npu_soc {"type":"command","command":"pageLoaded"}
[07-30 16:59:21.342] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-30 16:59:24.283] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:15:59.785] [info] work/n900_vnice_npu_soc {"type":"command","command":"updateLaunchConfig","payload":{"launchConfig":{"prelaunch":"\ncd ../../python\npython3 npu_demo.py ","launch":"rm -rf ./logs/*; $MOSIM_HOME/bin/mosim-top instance -d ./conf -g ./conf/log_config.toml -n n900_vnice_npu_soc > /data/users/jxchen/mosim_workspace/work/python/logs/mosim_output.log 2>&1","postlaunchBackground":true,"postlaunch":"pid=$(ps aux| grep npu_demo|grep -v grep | awk '{print $2}') && kill -9 $pid"}}}
[07-31 09:16:03.285] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:16:03.435] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-31 09:16:03.555] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[07-31 09:16:55.687] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-31 09:17:34.249] [info] work/n900_vnice_npu_soc {"type":"command","command":"dispose"}
[07-31 09:17:49.557] [info] work/n900_vnice_npu_soc {"type":"command","command":"pageLoaded"}
[07-31 09:17:59.882] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:20:05.170] [info] work/n900_vnice_npu_soc {"type":"command","command":"updateLaunchConfig","payload":{"launchConfig":{"prelaunch":"cd ../../python\npython3 npu_demo.py 2>&1 | tee -a /data/users/jxchen/mosim_workspace/work/python/logs/npu_demo_output.log","launch":"rm -rf ./logs/*; $MOSIM_HOME/bin/mosim-top instance -d ./conf -g ./conf/log_config.toml -n n900_vnice_npu_soc 2>&1 | tee /data/users/jxchen/mosim_workspace/work/python/logs/mosim_output.log","postlaunchBackground":true,"postlaunch":"pid=$(ps aux| grep npu_demo|grep -v grep | awk '{print $2}') && kill -9 $pid"}}}
[07-31 09:20:09.071] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:20:09.246] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-31 09:20:09.358] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[07-31 09:21:38.261] [info] work/n900_vnice_npu_soc {"type":"command","command":"pageLoaded"}
[07-31 09:22:11.248] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:22:19.616] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:22:55.178] [info] work/n900_vnice_npu_soc {"type":"command","command":"updateLaunchConfig","payload":{"launchConfig":{"prelaunch":"cd /data/users/jxchen/mosim_workspace/work/python\npython3 npu_demo.py 2>&1 | tee /data/users/jxchen/mosim_workspace/work/python/logs/npu_demo_output.log","launch":"rm -rf ./logs/*; $MOSIM_HOME/bin/mosim-top instance -d ./conf -g ./conf/log_config.toml -n n900_vnice_npu_soc 2>&1 | tee /data/users/jxchen/mosim_workspace/work/python/logs/mosim_output.log","postlaunchBackground":true,"postlaunch":"pid=$(ps aux| grep npu_demo|grep -v grep | awk '{print $2}') && kill -9 $pid"}}}
[07-31 09:22:56.961] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:22:57.127] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-31 09:22:57.242] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[07-31 09:23:04.956] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:23:17.324] [info] work/n900_vnice_npu_soc {"type":"command","command":"updateLaunchConfig","payload":{"launchConfig":{"prelaunch":"cd ../../python\npython3 npu_demo.py 2>&1 | tee ./logs/npu_demo_output.log","launch":"rm -rf ./logs/*; $MOSIM_HOME/bin/mosim-top instance -d ./conf -g ./conf/log_config.toml -n n900_vnice_npu_soc 2>&1 | tee ../../../python/logs/mosim_output.log","postlaunchBackground":true,"postlaunch":"pid=$(ps aux| grep npu_demo|grep -v grep | awk '{print $2}') && kill -9 $pid"}}}
[07-31 09:23:18.669] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:23:18.824] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-31 09:23:18.946] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[07-31 09:23:24.345] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:25:24.508] [info] work/n900_vnice_npu_soc {"type":"command","command":"updateLaunchConfig","payload":{"launchConfig":{"prelaunch":"cd /data/users/jxchen/mosim_workspace/work/python\npython3 npu_demo.py 2>&1 | tee /data/users/jxchen/mosim_workspace/work/python/logs/npu_demo_output.log","launch":"rm -rf ./logs/*; env LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:/data/users/jxchen/mosim_workspace/work/.lib $MOSIM_HOME/bin/mosim-top instance -d ./conf -g ./conf/log_config.toml -n n900_vnice_npu_soc -k n900_vnice_npu_soc:1753924998943 -s /data/users/jxchen/mosim_workspace/work/.mosim/settings.json 2>&1 | tee /data/users/jxchen/mosim_workspace/work/python/logs/mosim_output.log","postlaunchBackground":true,"postlaunch":"pid=$(ps aux| grep npu_demo|grep -v grep | awk '{print $2}') && kill -9 $pid"}}}
[07-31 09:25:25.797] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:25:25.961] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-31 09:25:26.068] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[07-31 09:25:47.460] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:26:02.980] [info] work/n900_vnice_npu_soc {"type":"command","command":"updateLaunchConfig","payload":{"launchConfig":{"prelaunch":"cd /data/users/jxchen/mosim_workspace/work/python\npython3 npu_demo.py 2>&1 | tee /data/users/jxchen/mosim_workspace/work/python/logs/npu_demo_output.log","launch":"rm -rf ./logs/*; ($MOSIM_HOME/bin/mosim-top instance -d ./conf -g ./conf/log_config.toml -n n900_vnice_npu_soc) 2>&1 | tee /data/users/jxchen/mosim_workspace/work/python/logs/mosim_output.log","postlaunchBackground":true,"postlaunch":"pid=$(ps aux| grep npu_demo|grep -v grep | awk '{print $2}') && kill -9 $pid"}}}
[07-31 09:26:04.209] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:26:04.365] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-31 09:26:04.476] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[07-31 09:26:10.789] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:26:32.749] [info] work/n900_vnice_npu_soc {"type":"command","command":"updateLaunchConfig","payload":{"launchConfig":{"prelaunch":"cd /data/users/jxchen/mosim_workspace/work/python\npython3 npu_demo.py 2>&1 | tee /data/users/jxchen/mosim_workspace/work/python/logs/npu_demo_output.log","launch":"rm -rf ./logs/*; $MOSIM_HOME/bin/mosim-top instance -d ./conf -g ./conf/log_config.toml -n n900_vnice_npu_soc 2>&1 | tee /data/users/jxchen/mosim_workspace/work/python/logs/mosim_output.log","postlaunchBackground":true,"postlaunch":"pid=$(ps aux| grep npu_demo|grep -v grep | awk '{print $2}') && kill -9 $pid"}}}
[07-31 09:26:34.039] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:26:34.200] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-31 09:26:34.317] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[07-31 09:27:44.301] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:27:49.824] [info] work/n900_vnice_npu_soc {"type":"command","command":"updateLaunchConfig","payload":{"launchConfig":{"prelaunch":"cd /data/users/jxchen/mosim_workspace/work/python\npython3 npu_demo.py 2>&1 | tee /data/users/jxchen/mosim_workspace/work/python/logs/npu_demo_output.log","launch":"./run_with_log.sh","postlaunchBackground":true,"postlaunch":"pid=$(ps aux| grep npu_demo|grep -v grep | awk '{print $2}') && kill -9 $pid"}}}
[07-31 09:27:50.997] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:27:51.152] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-31 09:27:51.298] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[07-31 09:28:36.036] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":true,"task":"n900_vnice_npu_soc"}}
[07-31 09:28:37.509] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:28:40.911] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:28:41.218] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-31 09:28:41.222] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[07-31 09:28:59.292] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":true,"task":"n900_vnice_npu_soc"}}
[07-31 09:29:00.878] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-31 09:31:07.154] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-31 09:31:08.918] [info] work/n900_vnice_npu_soc {"type":"command","command":"dispose"}
[07-31 09:36:34.208] [info] work/n900_vnice_npu_soc {"type":"command","command":"pageLoaded"}
[07-31 09:36:36.791] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-31 09:36:37.145] [info] work/n900_vnice_npu_soc {"type":"command","command":"getLaunchConfig","payload":{"task":"n900_vnice_npu_soc"}}
[07-31 09:36:37.307] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"run soc","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-31 09:36:37.433] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":false,"task":"n900_vnice_npu_soc"}}
[07-31 09:36:40.579] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-31 09:36:45.162] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-31 09:36:55.011] [info] work/n900_vnice_npu_soc {"type":"command","command":"saveTopJson","payload":{"type":"cell:changed","socPath":"/data/users/jxchen/mosim_workspace/work/soc/n900_vnice_npu_soc","socName":"n900_vnice_npu_soc"}}
[07-31 09:37:05.608] [info] work/n900_vnice_npu_soc {"type":"command","command":"toggleRun","payload":{"state":true,"task":"n900_vnice_npu_soc"}}
[07-31 09:37:05.609] [info] work/n900_vnice_npu_soc {"type":"command","command":"dispose"}