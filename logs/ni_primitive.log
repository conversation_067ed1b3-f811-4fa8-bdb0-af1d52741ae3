[30146] Create PrimitiveInfo {"group": 0, "mask": 1}
[30171] Create PrimitiveInfo {"vp.type_out": 2, "vp.type_in1": 2, "vp.type_in2": 0, "vp.wd_out": 4, "vp.wd_in1": 4, "vp.wd_in2": 0, "vp.op": 0, "vp.rem_dim0_out": 0, "vp.size_dim0b_out": 0, "vp.rem_dim0_in1": 0, "vp.size_dim0b_in1": 16, "vp.rem_dim0_in2": 0, "vp.size_dim0b_in2": 0, "vp.safu": 0, "vp.dis0": 0, "vp.rm": 0, "vs.base_addr_in1": 0, "group": 0, "mask": 1}
[30226] Create PrimitiveInfo {"group": 0, "mask": 2}
[30251] Create PrimitiveInfo {"vp.type_out": 2, "vp.type_in1": 2, "vp.type_in2": 0, "vp.wd_out": 4, "vp.wd_in1": 4, "vp.wd_in2": 0, "vp.op": 0, "vp.rem_dim0_out": 0, "vp.size_dim0b_out": 0, "vp.rem_dim0_in1": 0, "vp.size_dim0b_in1": 16, "vp.rem_dim0_in2": 0, "vp.size_dim0b_in2": 0, "vp.safu": 0, "vp.dis0": 0, "vp.rm": 0, "vs.base_addr_in1": 0, "group": 0, "mask": 2}
[30296] Create PrimitiveInfo {"group": 0, "mask": 4}
[30321] Create PrimitiveInfo {"vp.type_out": 2, "vp.type_in1": 2, "vp.type_in2": 0, "vp.wd_out": 4, "vp.wd_in1": 4, "vp.wd_in2": 0, "vp.op": 0, "vp.rem_dim0_out": 0, "vp.size_dim0b_out": 0, "vp.rem_dim0_in1": 0, "vp.size_dim0b_in1": 16, "vp.rem_dim0_in2": 0, "vp.size_dim0b_in2": 0, "vp.safu": 0, "vp.dis0": 0, "vp.rm": 0, "vs.base_addr_in1": 0, "group": 0, "mask": 4}
[30366] Create PrimitiveInfo {"group": 0, "mask": 8}
[30391] Create PrimitiveInfo {"vp.type_out": 2, "vp.type_in1": 2, "vp.type_in2": 0, "vp.wd_out": 4, "vp.wd_in1": 4, "vp.wd_in2": 0, "vp.op": 0, "vp.rem_dim0_out": 0, "vp.size_dim0b_out": 0, "vp.rem_dim0_in1": 0, "vp.size_dim0b_in1": 16, "vp.rem_dim0_in2": 0, "vp.size_dim0b_in2": 0, "vp.safu": 0, "vp.dis0": 0, "vp.rm": 0, "vs.base_addr_in1": 0, "group": 0, "mask": 8}
[30470] Create PrimitiveInfo {"group": 1, "mask": 1}
[30495] Create PrimitiveInfo {"vp.type_out": 2, "vp.type_in1": 2, "vp.type_in2": 0, "vp.wd_out": 4, "vp.wd_in1": 4, "vp.wd_in2": 0, "vp.op": 0, "vp.rem_dim0_out": 0, "vp.size_dim0b_out": 0, "vp.rem_dim0_in1": 0, "vp.size_dim0b_in1": 16, "vp.rem_dim0_in2": 0, "vp.size_dim0b_in2": 0, "vp.safu": 0, "vp.dis0": 0, "vp.rm": 0, "vs.base_addr_in1": 0, "group": 1, "mask": 1}
[30540] Create PrimitiveInfo {"group": 1, "mask": 2}
[30565] Create PrimitiveInfo {"vp.type_out": 2, "vp.type_in1": 2, "vp.type_in2": 0, "vp.wd_out": 4, "vp.wd_in1": 4, "vp.wd_in2": 0, "vp.op": 0, "vp.rem_dim0_out": 0, "vp.size_dim0b_out": 0, "vp.rem_dim0_in1": 0, "vp.size_dim0b_in1": 16, "vp.rem_dim0_in2": 0, "vp.size_dim0b_in2": 0, "vp.safu": 0, "vp.dis0": 0, "vp.rm": 0, "vs.base_addr_in1": 0, "group": 1, "mask": 2}
[30610] Create PrimitiveInfo {"group": 1, "mask": 4}
[30635] Create PrimitiveInfo {"vp.type_out": 2, "vp.type_in1": 2, "vp.type_in2": 0, "vp.wd_out": 4, "vp.wd_in1": 4, "vp.wd_in2": 0, "vp.op": 0, "vp.rem_dim0_out": 0, "vp.size_dim0b_out": 0, "vp.rem_dim0_in1": 0, "vp.size_dim0b_in1": 16, "vp.rem_dim0_in2": 0, "vp.size_dim0b_in2": 0, "vp.safu": 0, "vp.dis0": 0, "vp.rm": 0, "vs.base_addr_in1": 0, "group": 1, "mask": 4}
[30680] Create PrimitiveInfo {"group": 1, "mask": 8}
[30705] Create PrimitiveInfo {"vp.type_out": 2, "vp.type_in1": 2, "vp.type_in2": 0, "vp.wd_out": 4, "vp.wd_in1": 4, "vp.wd_in2": 0, "vp.op": 0, "vp.rem_dim0_out": 0, "vp.size_dim0b_out": 0, "vp.rem_dim0_in1": 0, "vp.size_dim0b_in1": 16, "vp.rem_dim0_in2": 0, "vp.size_dim0b_in2": 0, "vp.safu": 0, "vp.dis0": 0, "vp.rm": 0, "vs.base_addr_in1": 0, "group": 1, "mask": 8}
[30775] Create PrimitiveInfo {"group": 2, "mask": 1}
[30800] Create PrimitiveInfo {"vp.type_out": 2, "vp.type_in1": 2, "vp.type_in2": 0, "vp.wd_out": 4, "vp.wd_in1": 4, "vp.wd_in2": 0, "vp.op": 0, "vp.rem_dim0_out": 0, "vp.size_dim0b_out": 0, "vp.rem_dim0_in1": 0, "vp.size_dim0b_in1": 16, "vp.rem_dim0_in2": 0, "vp.size_dim0b_in2": 0, "vp.safu": 0, "vp.dis0": 0, "vp.rm": 0, "vs.base_addr_in1": 0, "group": 2, "mask": 1}
[30845] Create PrimitiveInfo {"group": 2, "mask": 2}
[30870] Create PrimitiveInfo {"vp.type_out": 2, "vp.type_in1": 2, "vp.type_in2": 0, "vp.wd_out": 4, "vp.wd_in1": 4, "vp.wd_in2": 0, "vp.op": 0, "vp.rem_dim0_out": 0, "vp.size_dim0b_out": 0, "vp.rem_dim0_in1": 0, "vp.size_dim0b_in1": 16, "vp.rem_dim0_in2": 0, "vp.size_dim0b_in2": 0, "vp.safu": 0, "vp.dis0": 0, "vp.rm": 0, "vs.base_addr_in1": 0, "group": 2, "mask": 2}
[30915] Create PrimitiveInfo {"group": 2, "mask": 4}
[30940] Create PrimitiveInfo {"vp.type_out": 2, "vp.type_in1": 2, "vp.type_in2": 0, "vp.wd_out": 4, "vp.wd_in1": 4, "vp.wd_in2": 0, "vp.op": 0, "vp.rem_dim0_out": 0, "vp.size_dim0b_out": 0, "vp.rem_dim0_in1": 0, "vp.size_dim0b_in1": 16, "vp.rem_dim0_in2": 0, "vp.size_dim0b_in2": 0, "vp.safu": 0, "vp.dis0": 0, "vp.rm": 0, "vs.base_addr_in1": 0, "group": 2, "mask": 4}
[30985] Create PrimitiveInfo {"group": 2, "mask": 8}
[31010] Create PrimitiveInfo {"vp.type_out": 2, "vp.type_in1": 2, "vp.type_in2": 0, "vp.wd_out": 4, "vp.wd_in1": 4, "vp.wd_in2": 0, "vp.op": 0, "vp.rem_dim0_out": 0, "vp.size_dim0b_out": 0, "vp.rem_dim0_in1": 0, "vp.size_dim0b_in1": 16, "vp.rem_dim0_in2": 0, "vp.size_dim0b_in2": 0, "vp.safu": 0, "vp.dis0": 0, "vp.rm": 0, "vs.base_addr_in1": 0, "group": 2, "mask": 8}
[31080] Create PrimitiveInfo {"group": 3, "mask": 1}
[31105] Create PrimitiveInfo {"vp.type_out": 2, "vp.type_in1": 2, "vp.type_in2": 0, "vp.wd_out": 4, "vp.wd_in1": 4, "vp.wd_in2": 0, "vp.op": 0, "vp.rem_dim0_out": 0, "vp.size_dim0b_out": 0, "vp.rem_dim0_in1": 0, "vp.size_dim0b_in1": 16, "vp.rem_dim0_in2": 0, "vp.size_dim0b_in2": 0, "vp.safu": 0, "vp.dis0": 0, "vp.rm": 0, "vs.base_addr_in1": 0, "group": 3, "mask": 1}
[31150] Create PrimitiveInfo {"group": 3, "mask": 2}
[31175] Create PrimitiveInfo {"vp.type_out": 2, "vp.type_in1": 2, "vp.type_in2": 0, "vp.wd_out": 4, "vp.wd_in1": 4, "vp.wd_in2": 0, "vp.op": 0, "vp.rem_dim0_out": 0, "vp.size_dim0b_out": 0, "vp.rem_dim0_in1": 0, "vp.size_dim0b_in1": 16, "vp.rem_dim0_in2": 0, "vp.size_dim0b_in2": 0, "vp.safu": 0, "vp.dis0": 0, "vp.rm": 0, "vs.base_addr_in1": 0, "group": 3, "mask": 2}
[31220] Create PrimitiveInfo {"group": 3, "mask": 4}
[31245] Create PrimitiveInfo {"vp.type_out": 2, "vp.type_in1": 2, "vp.type_in2": 0, "vp.wd_out": 4, "vp.wd_in1": 4, "vp.wd_in2": 0, "vp.op": 0, "vp.rem_dim0_out": 0, "vp.size_dim0b_out": 0, "vp.rem_dim0_in1": 0, "vp.size_dim0b_in1": 16, "vp.rem_dim0_in2": 0, "vp.size_dim0b_in2": 0, "vp.safu": 0, "vp.dis0": 0, "vp.rm": 0, "vs.base_addr_in1": 0, "group": 3, "mask": 4}
[31290] Create PrimitiveInfo {"group": 3, "mask": 8}
[31315] Create PrimitiveInfo {"vp.type_out": 2, "vp.type_in1": 2, "vp.type_in2": 0, "vp.wd_out": 4, "vp.wd_in1": 4, "vp.wd_in2": 0, "vp.op": 0, "vp.rem_dim0_out": 0, "vp.size_dim0b_out": 0, "vp.rem_dim0_in1": 0, "vp.size_dim0b_in1": 16, "vp.rem_dim0_in2": 0, "vp.size_dim0b_in2": 0, "vp.safu": 0, "vp.dis0": 0, "vp.rm": 0, "vs.base_addr_in1": 0, "group": 3, "mask": 8}
[65133] Create PrimitiveInfo {"group": 3, "mask": 8}
