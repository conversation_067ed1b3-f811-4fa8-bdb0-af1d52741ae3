Server '/data/users/jxchen/.mosim/sockets/npu.sock' is listening for incoming connections...
GOLDEN_VM_SYSTEM initialized with 16 cores

Importing 16 tensors from ./in_out/inout_data/input_tensor.json
============================================================

Importing tensor: in1_0_0
  Core ID: 0
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9922]
  ✓ Successfully imported

Importing tensor: in1_0_1
  Core ID: 1
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-0.9844, 0.9922]
  ✓ Successfully imported

Importing tensor: in1_0_2
  Core ID: 2
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9922]
  ✓ Successfully imported

Importing tensor: in1_0_3
  Core ID: 3
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-0.9922, 0.9844]
  ✓ Successfully imported

Importing tensor: in1_1_0
  Core ID: 4
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-0.9922, 0.9844]
  ✓ Successfully imported

Importing tensor: in1_1_1
  Core ID: 5
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9844]
  ✓ Successfully imported

Importing tensor: in1_1_2
  Core ID: 6
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-0.9844, 0.9922]
  ✓ Successfully imported

Importing tensor: in1_1_3
  Core ID: 7
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9922]
  ✓ Successfully imported

Importing tensor: in1_2_0
  Core ID: 8
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9844]
  ✓ Successfully imported

Importing tensor: in1_2_1
  Core ID: 9
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9844]
  ✓ Successfully imported

Importing tensor: in1_2_2
  Core ID: 10
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9844]
  ✓ Successfully imported

Importing tensor: in1_2_3
  Core ID: 11
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-0.9922, 0.9922]
  ✓ Successfully imported

Importing tensor: in1_3_0
  Core ID: 12
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9922]
  ✓ Successfully imported

Importing tensor: in1_3_1
  Core ID: 13
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9922]
  ✓ Successfully imported

Importing tensor: in1_3_2
  Core ID: 14
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-0.9922, 0.9922]
  ✓ Successfully imported

Importing tensor: in1_3_3
  Core ID: 15
  Memory: SPAD[0]
  Address: 0x00000000
  Shape: (1, 1, 256)
  Dtype: BF16
  Data range: [-1.0000, 0.9922]
  ✓ Successfully imported

============================================================
Data import completed
at 2025-07-31 09:28:43.480767 npu running ...
Connection from fd=5
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000075c200000008
ipcSocketServer receive request data: 00000000000075c2
* * * * Request Simulate to 30146 * * * *
How many requests in queue? - 0
cur_cycle: 0 , next_cycle: 30146
[Subsystem::handle] Received handshake request @sim_time= 30146
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000075c20000000c
ipcSocketServer receive request data: 00e7b00b0000000000000001
* * * * Request Simulate to 30146 * * * *
How many requests in queue? - 0
cur_cycle: 30146 , next_cycle: 30146
[Subsystem::handle] Received nice instruction request @sim_time= 30146
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000075cf00000008
ipcSocketServer receive request data: 00000000000075cf
* * * * Request Simulate to 30159 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 0, 1)
Verifying, current_cycle:30146 - sychronizing_npus:False - target_sim_cycle:30159
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 0, PrimName.GROUP_MASK, group/mask: 0/1, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30146 , next_cycle: 30159
dispatching: Pid: 0, PrimName.GROUP_MASK, group/mask: 0/1, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 30159
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000075cf0000000c
ipcSocketServer receive request data: 02e7b00b000400000000090a
* * * * Request Simulate to 30159 * * * *
How many requests in queue? - 0
cur_cycle: 30159 , next_cycle: 30159
[Subsystem::handle] Received nice instruction request @sim_time= 30159
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000075d300000008
ipcSocketServer receive request data: 00000000000075d3
* * * * Request Simulate to 30163 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262144, 2314)
ddd, is_drv=False
cur_cycle: 30159 , next_cycle: 30163
[Subsystem::handle] Received handshake request @sim_time= 30163
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000075d30000000c
ipcSocketServer receive request data: 02e7b00b0004000200000400
* * * * Request Simulate to 30163 * * * *
How many requests in queue? - 0
cur_cycle: 30163 , next_cycle: 30163
[Subsystem::handle] Received nice instruction request @sim_time= 30163
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000075d700000008
ipcSocketServer receive request data: 00000000000075d7
* * * * Request Simulate to 30167 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262146, 1024)
ddd, is_drv=False
cur_cycle: 30163 , next_cycle: 30167
[Subsystem::handle] Received handshake request @sim_time= 30167
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000075d70000000c
ipcSocketServer receive request data: 02e7b00b0004000400000000
* * * * Request Simulate to 30167 * * * *
How many requests in queue? - 0
cur_cycle: 30167 , next_cycle: 30167
[Subsystem::handle] Received nice instruction request @sim_time= 30167
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000075db00000008
ipcSocketServer receive request data: 00000000000075db
* * * * Request Simulate to 30171 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262148, 0)
ddd, is_drv=False
cur_cycle: 30167 , next_cycle: 30171
[Subsystem::handle] Received handshake request @sim_time= 30171
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000075db0000000c
ipcSocketServer receive request data: a807e78b0000000000000000
* * * * Request Simulate to 30171 * * * *
How many requests in queue? - 0
cur_cycle: 30171 , next_cycle: 30171
[Subsystem::handle] Received nice instruction request @sim_time= 30171
----------------------------------------------------
ipcSocketServer receive request header: ****************db00000000
* * * * Request Simulate to 30171 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle vs_drv(a807e78b, 0, 0)
Verifying, current_cycle:30171 - sychronizing_npus:False - target_sim_cycle:30171
Group:0 - Mask:[True, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 1, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: byte_base: 0x0, byte_stride: (512, 512), size: (256, 1, 1), type: BF, width: 16
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30171 , next_cycle: 30171
dispatching: Pid: 1, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received check request @sim_time= 30171
----------------------------------------------------
ipcSocketServer receive request header: ****************db00000000
* * * * Request Simulate to 30171 * * * *
How many requests in queue? - 0
NPU Group.ID: 0.0, Start: Pid: 1, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30171, 30188), #cycle: 17
0 - SRAMBank 1 from 0x0000_0000 to 0x0000_ffe0, Cycle cost: Rin1=16, Rin2=0, Rorig=0, Wout=0, Rcomm=0, Wcomm=0, and Total=16
1 - VPU with 16 Engines each receives 2×32bit inputs, Cycle cost: VectorProcess=11, Latency=1
cur_cycle: 30171 , next_cycle: 30172
[Subsystem::handle] Received check request @sim_time= 30171
----------------------------------------------------
ipcSocketServer receive request header: ****************dc00000000
* * * * Request Simulate to 30172 * * * *
How many requests in queue? - 0
cur_cycle: 30171 , next_cycle: 30188
[Subsystem::handle] Received check request @sim_time= 30172
----------------------------------------------------
ipcSocketServer receive request header: ****************ec00000000
* * * * Request Simulate to 30188 * * * *
How many requests in queue? - 0
cur_cycle: 30172 , next_cycle: 30188
[Subsystem::handle] Received check request @sim_time= 30188
----------------------------------------------------
ipcSocketServer receive request header: ****************ec00000000
* * * * Request Simulate to 30188 * * * *
How many requests in queue? - 0
NPU Core 0: Stored return value 1082900480 for prim_id 1, inst_id 0
NPU Group.ID: 0.0, Finish: Pid: 1, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30171, 30188), #cycle: 17
Generated response for inst_id 0, type nice, ret 1082900480
cur_cycle: 30188 , next_cycle: 30189
[Subsystem::handle] Received check request @sim_time= 30188
[Subsystem::check_handler] check nice instruction response: id=0, type=nice, ret=0x408bc000
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000761200000008
ipcSocketServer receive request data: 0000000000007612
* * * * Request Simulate to 30226 * * * *
How many requests in queue? - 0
cur_cycle: 30188 , next_cycle: 30226
[Subsystem::handle] Received handshake request @sim_time= 30226
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000076120000000c
ipcSocketServer receive request data: 00e7b00b0000000000000002
* * * * Request Simulate to 30226 * * * *
How many requests in queue? - 0
cur_cycle: 30226 , next_cycle: 30226
[Subsystem::handle] Received nice instruction request @sim_time= 30226
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000761f00000008
ipcSocketServer receive request data: 000000000000761f
* * * * Request Simulate to 30239 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 0, 2)
Verifying, current_cycle:30226 - sychronizing_npus:False - target_sim_cycle:30239
Group:0 - Mask:[True, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 2, PrimName.GROUP_MASK, group/mask: 0/2, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30226 , next_cycle: 30239
dispatching: Pid: 2, PrimName.GROUP_MASK, group/mask: 0/2, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 30239
----------------------------------------------------
ipcSocketServer receive request header: 02000000000000761f0000000c
ipcSocketServer receive request data: 02e7b00b000400000000090a
* * * * Request Simulate to 30239 * * * *
How many requests in queue? - 0
cur_cycle: 30239 , next_cycle: 30239
[Subsystem::handle] Received nice instruction request @sim_time= 30239
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000762300000008
ipcSocketServer receive request data: 0000000000007623
* * * * Request Simulate to 30243 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262144, 2314)
ddd, is_drv=False
cur_cycle: 30239 , next_cycle: 30243
[Subsystem::handle] Received handshake request @sim_time= 30243
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000076230000000c
ipcSocketServer receive request data: 02e7b00b0004000200000400
* * * * Request Simulate to 30243 * * * *
How many requests in queue? - 0
cur_cycle: 30243 , next_cycle: 30243
[Subsystem::handle] Received nice instruction request @sim_time= 30243
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000762700000008
ipcSocketServer receive request data: 0000000000007627
* * * * Request Simulate to 30247 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262146, 1024)
ddd, is_drv=False
cur_cycle: 30243 , next_cycle: 30247
[Subsystem::handle] Received handshake request @sim_time= 30247
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000076270000000c
ipcSocketServer receive request data: 02e7b00b0004000400000000
* * * * Request Simulate to 30247 * * * *
How many requests in queue? - 0
cur_cycle: 30247 , next_cycle: 30247
[Subsystem::handle] Received nice instruction request @sim_time= 30247
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000762b00000008
ipcSocketServer receive request data: 000000000000762b
* * * * Request Simulate to 30251 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262148, 0)
ddd, is_drv=False
cur_cycle: 30247 , next_cycle: 30251
[Subsystem::handle] Received handshake request @sim_time= 30251
----------------------------------------------------
ipcSocketServer receive request header: 02000000000000762b0000000c
ipcSocketServer receive request data: a807e78b0000000000000000
* * * * Request Simulate to 30251 * * * *
How many requests in queue? - 0
cur_cycle: 30251 , next_cycle: 30251
[Subsystem::handle] Received nice instruction request @sim_time= 30251
----------------------------------------------------
ipcSocketServer receive request header: ****************2b00000000
* * * * Request Simulate to 30251 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle vs_drv(a807e78b, 0, 0)
Verifying, current_cycle:30251 - sychronizing_npus:False - target_sim_cycle:30251
Group:0 - Mask:[False, True, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 3, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: byte_base: 0x0, byte_stride: (512, 512), size: (256, 1, 1), type: BF, width: 16
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30251 , next_cycle: 30251
dispatching: Pid: 3, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received check request @sim_time= 30251
----------------------------------------------------
ipcSocketServer receive request header: ****************2b00000000
* * * * Request Simulate to 30251 * * * *
How many requests in queue? - 0
NPU Group.ID: 0.1, Start: Pid: 3, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30251, 30268), #cycle: 17
0 - SRAMBank 1 from 0x0000_0000 to 0x0000_ffe0, Cycle cost: Rin1=16, Rin2=0, Rorig=0, Wout=0, Rcomm=0, Wcomm=0, and Total=16
1 - VPU with 16 Engines each receives 2×32bit inputs, Cycle cost: VectorProcess=11, Latency=1
cur_cycle: 30251 , next_cycle: 30252
[Subsystem::handle] Received check request @sim_time= 30251
----------------------------------------------------
ipcSocketServer receive request header: ****************2c00000000
* * * * Request Simulate to 30252 * * * *
How many requests in queue? - 0
cur_cycle: 30251 , next_cycle: 30268
[Subsystem::handle] Received check request @sim_time= 30252
----------------------------------------------------
ipcSocketServer receive request header: ****************3c00000000
* * * * Request Simulate to 30268 * * * *
How many requests in queue? - 0
cur_cycle: 30252 , next_cycle: 30268
[Subsystem::handle] Received check request @sim_time= 30268
----------------------------------------------------
ipcSocketServer receive request header: ****************3c00000000
* * * * Request Simulate to 30268 * * * *
How many requests in queue? - 0
NPU Core 1: Stored return value 1091190784 for prim_id 3, inst_id 1
NPU Group.ID: 0.1, Finish: Pid: 3, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30251, 30268), #cycle: 17
Generated response for inst_id 1, type nice, ret 1091190784
cur_cycle: 30268 , next_cycle: 30269
[Subsystem::handle] Received check request @sim_time= 30268
[Subsystem::check_handler] check nice instruction response: id=0, type=nice, ret=0x410a4000
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000765800000008
ipcSocketServer receive request data: 0000000000007658
* * * * Request Simulate to 30296 * * * *
How many requests in queue? - 0
cur_cycle: 30268 , next_cycle: 30296
[Subsystem::handle] Received handshake request @sim_time= 30296
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000076580000000c
ipcSocketServer receive request data: 00e7b00b0000000000000004
* * * * Request Simulate to 30296 * * * *
How many requests in queue? - 0
cur_cycle: 30296 , next_cycle: 30296
[Subsystem::handle] Received nice instruction request @sim_time= 30296
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000766500000008
ipcSocketServer receive request data: 0000000000007665
* * * * Request Simulate to 30309 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 0, 4)
Verifying, current_cycle:30296 - sychronizing_npus:False - target_sim_cycle:30309
Group:0 - Mask:[False, True, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 4, PrimName.GROUP_MASK, group/mask: 0/4, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30296 , next_cycle: 30309
dispatching: Pid: 4, PrimName.GROUP_MASK, group/mask: 0/4, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 30309
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000076650000000c
ipcSocketServer receive request data: 02e7b00b000400000000090a
* * * * Request Simulate to 30309 * * * *
How many requests in queue? - 0
cur_cycle: 30309 , next_cycle: 30309
[Subsystem::handle] Received nice instruction request @sim_time= 30309
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000766900000008
ipcSocketServer receive request data: 0000000000007669
* * * * Request Simulate to 30313 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262144, 2314)
ddd, is_drv=False
cur_cycle: 30309 , next_cycle: 30313
[Subsystem::handle] Received handshake request @sim_time= 30313
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000076690000000c
ipcSocketServer receive request data: 02e7b00b0004000200000400
* * * * Request Simulate to 30313 * * * *
How many requests in queue? - 0
cur_cycle: 30313 , next_cycle: 30313
[Subsystem::handle] Received nice instruction request @sim_time= 30313
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000766d00000008
ipcSocketServer receive request data: 000000000000766d
* * * * Request Simulate to 30317 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262146, 1024)
ddd, is_drv=False
cur_cycle: 30313 , next_cycle: 30317
[Subsystem::handle] Received handshake request @sim_time= 30317
----------------------------------------------------
ipcSocketServer receive request header: 02000000000000766d0000000c
ipcSocketServer receive request data: 02e7b00b0004000400000000
* * * * Request Simulate to 30317 * * * *
How many requests in queue? - 0
cur_cycle: 30317 , next_cycle: 30317
[Subsystem::handle] Received nice instruction request @sim_time= 30317
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000767100000008
ipcSocketServer receive request data: 0000000000007671
* * * * Request Simulate to 30321 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262148, 0)
ddd, is_drv=False
cur_cycle: 30317 , next_cycle: 30321
[Subsystem::handle] Received handshake request @sim_time= 30321
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000076710000000c
ipcSocketServer receive request data: a807e78b0000000000000000
* * * * Request Simulate to 30321 * * * *
How many requests in queue? - 0
cur_cycle: 30321 , next_cycle: 30321
[Subsystem::handle] Received nice instruction request @sim_time= 30321
----------------------------------------------------
ipcSocketServer receive request header: ****************7100000000
* * * * Request Simulate to 30321 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle vs_drv(a807e78b, 0, 0)
Verifying, current_cycle:30321 - sychronizing_npus:False - target_sim_cycle:30321
Group:0 - Mask:[False, False, True, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 5, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: byte_base: 0x0, byte_stride: (512, 512), size: (256, 1, 1), type: BF, width: 16
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30321 , next_cycle: 30321
dispatching: Pid: 5, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received check request @sim_time= 30321
----------------------------------------------------
ipcSocketServer receive request header: ****************7100000000
* * * * Request Simulate to 30321 * * * *
How many requests in queue? - 0
NPU Group.ID: 0.2, Start: Pid: 5, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30321, 30338), #cycle: 17
0 - SRAMBank 1 from 0x0000_0000 to 0x0000_ffe0, Cycle cost: Rin1=16, Rin2=0, Rorig=0, Wout=0, Rcomm=0, Wcomm=0, and Total=16
1 - VPU with 16 Engines each receives 2×32bit inputs, Cycle cost: VectorProcess=11, Latency=1
cur_cycle: 30321 , next_cycle: 30322
[Subsystem::handle] Received check request @sim_time= 30321
----------------------------------------------------
ipcSocketServer receive request header: ****************7200000000
* * * * Request Simulate to 30322 * * * *
How many requests in queue? - 0
cur_cycle: 30321 , next_cycle: 30338
[Subsystem::handle] Received check request @sim_time= 30322
----------------------------------------------------
ipcSocketServer receive request header: ****************8200000000
* * * * Request Simulate to 30338 * * * *
How many requests in queue? - 0
cur_cycle: 30322 , next_cycle: 30338
[Subsystem::handle] Received check request @sim_time= 30338
----------------------------------------------------
ipcSocketServer receive request header: ****************8200000000
* * * * Request Simulate to 30338 * * * *
How many requests in queue? - 0
NPU Core 2: Stored return value 1089503232 for prim_id 5, inst_id 2
NPU Group.ID: 0.2, Finish: Pid: 5, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30321, 30338), #cycle: 17
Generated response for inst_id 2, type nice, ret 1089503232
cur_cycle: 30338 , next_cycle: 30339
[Subsystem::handle] Received check request @sim_time= 30338
[Subsystem::check_handler] check nice instruction response: id=0, type=nice, ret=0x40f08000
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000769e00000008
ipcSocketServer receive request data: 000000000000769e
* * * * Request Simulate to 30366 * * * *
How many requests in queue? - 0
cur_cycle: 30338 , next_cycle: 30366
[Subsystem::handle] Received handshake request @sim_time= 30366
----------------------------------------------------
ipcSocketServer receive request header: 02000000000000769e0000000c
ipcSocketServer receive request data: 00e7b00b0000000000000008
* * * * Request Simulate to 30366 * * * *
How many requests in queue? - 0
cur_cycle: 30366 , next_cycle: 30366
[Subsystem::handle] Received nice instruction request @sim_time= 30366
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000076ab00000008
ipcSocketServer receive request data: 00000000000076ab
* * * * Request Simulate to 30379 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 0, 8)
Verifying, current_cycle:30366 - sychronizing_npus:False - target_sim_cycle:30379
Group:0 - Mask:[False, False, True, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 6, PrimName.GROUP_MASK, group/mask: 0/8, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30366 , next_cycle: 30379
dispatching: Pid: 6, PrimName.GROUP_MASK, group/mask: 0/8, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 30379
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000076ab0000000c
ipcSocketServer receive request data: 02e7b00b000400000000090a
* * * * Request Simulate to 30379 * * * *
How many requests in queue? - 0
cur_cycle: 30379 , next_cycle: 30379
[Subsystem::handle] Received nice instruction request @sim_time= 30379
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000076af00000008
ipcSocketServer receive request data: 00000000000076af
* * * * Request Simulate to 30383 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262144, 2314)
ddd, is_drv=False
cur_cycle: 30379 , next_cycle: 30383
[Subsystem::handle] Received handshake request @sim_time= 30383
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000076af0000000c
ipcSocketServer receive request data: 02e7b00b0004000200000400
* * * * Request Simulate to 30383 * * * *
How many requests in queue? - 0
cur_cycle: 30383 , next_cycle: 30383
[Subsystem::handle] Received nice instruction request @sim_time= 30383
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000076b300000008
ipcSocketServer receive request data: 00000000000076b3
* * * * Request Simulate to 30387 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262146, 1024)
ddd, is_drv=False
cur_cycle: 30383 , next_cycle: 30387
[Subsystem::handle] Received handshake request @sim_time= 30387
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000076b30000000c
ipcSocketServer receive request data: 02e7b00b0004000400000000
* * * * Request Simulate to 30387 * * * *
How many requests in queue? - 0
cur_cycle: 30387 , next_cycle: 30387
[Subsystem::handle] Received nice instruction request @sim_time= 30387
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000076b700000008
ipcSocketServer receive request data: 00000000000076b7
* * * * Request Simulate to 30391 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262148, 0)
ddd, is_drv=False
cur_cycle: 30387 , next_cycle: 30391
[Subsystem::handle] Received handshake request @sim_time= 30391
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000076b70000000c
ipcSocketServer receive request data: a807e78b0000000000000000
* * * * Request Simulate to 30391 * * * *
How many requests in queue? - 0
cur_cycle: 30391 , next_cycle: 30391
[Subsystem::handle] Received nice instruction request @sim_time= 30391
----------------------------------------------------
ipcSocketServer receive request header: ****************b700000000
* * * * Request Simulate to 30391 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle vs_drv(a807e78b, 0, 0)
Verifying, current_cycle:30391 - sychronizing_npus:False - target_sim_cycle:30391
Group:0 - Mask:[False, False, False, True]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 7, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: byte_base: 0x0, byte_stride: (512, 512), size: (256, 1, 1), type: BF, width: 16
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30391 , next_cycle: 30391
dispatching: Pid: 7, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received check request @sim_time= 30391
----------------------------------------------------
ipcSocketServer receive request header: ****************b700000000
* * * * Request Simulate to 30391 * * * *
How many requests in queue? - 0
NPU Group.ID: 0.3, Start: Pid: 7, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30391, 30408), #cycle: 17
0 - SRAMBank 1 from 0x0000_0000 to 0x0000_ffe0, Cycle cost: Rin1=16, Rin2=0, Rorig=0, Wout=0, Rcomm=0, Wcomm=0, and Total=16
1 - VPU with 16 Engines each receives 2×32bit inputs, Cycle cost: VectorProcess=11, Latency=1
cur_cycle: 30391 , next_cycle: 30392
[Subsystem::handle] Received check request @sim_time= 30391
----------------------------------------------------
ipcSocketServer receive request header: ****************b800000000
* * * * Request Simulate to 30392 * * * *
How many requests in queue? - 0
cur_cycle: 30391 , next_cycle: 30408
[Subsystem::handle] Received check request @sim_time= 30392
----------------------------------------------------
ipcSocketServer receive request header: ****************c800000000
* * * * Request Simulate to 30408 * * * *
How many requests in queue? - 0
cur_cycle: 30392 , next_cycle: 30408
[Subsystem::handle] Received check request @sim_time= 30408
----------------------------------------------------
ipcSocketServer receive request header: ****************c800000000
* * * * Request Simulate to 30408 * * * *
How many requests in queue? - 0
NPU Core 3: Stored return value 1071448064 for prim_id 7, inst_id 3
NPU Group.ID: 0.3, Finish: Pid: 7, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30391, 30408), #cycle: 17
Generated response for inst_id 3, type nice, ret 1071448064
cur_cycle: 30408 , next_cycle: 30409
[Subsystem::handle] Received check request @sim_time= 30408
[Subsystem::check_handler] check nice instruction response: id=0, type=nice, ret=0x3fdd0000
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000770600000008
ipcSocketServer receive request data: 0000000000007706
* * * * Request Simulate to 30470 * * * *
How many requests in queue? - 0
cur_cycle: 30408 , next_cycle: 30470
[Subsystem::handle] Received handshake request @sim_time= 30470
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000077060000000c
ipcSocketServer receive request data: 00e7b00b0000000100000001
* * * * Request Simulate to 30470 * * * *
How many requests in queue? - 0
cur_cycle: 30470 , next_cycle: 30470
[Subsystem::handle] Received nice instruction request @sim_time= 30470
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000771300000008
ipcSocketServer receive request data: 0000000000007713
* * * * Request Simulate to 30483 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 1, 1)
Verifying, current_cycle:30470 - sychronizing_npus:False - target_sim_cycle:30483
Group:0 - Mask:[False, False, False, True]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 8, PrimName.GROUP_MASK, group/mask: 1/1, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30470 , next_cycle: 30483
dispatching: Pid: 8, PrimName.GROUP_MASK, group/mask: 1/1, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 30483
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000077130000000c
ipcSocketServer receive request data: 02e7b00b000400000000090a
* * * * Request Simulate to 30483 * * * *
How many requests in queue? - 0
cur_cycle: 30483 , next_cycle: 30483
[Subsystem::handle] Received nice instruction request @sim_time= 30483
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000771700000008
ipcSocketServer receive request data: 0000000000007717
* * * * Request Simulate to 30487 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262144, 2314)
ddd, is_drv=False
cur_cycle: 30483 , next_cycle: 30487
[Subsystem::handle] Received handshake request @sim_time= 30487
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000077170000000c
ipcSocketServer receive request data: 02e7b00b0004000200000400
* * * * Request Simulate to 30487 * * * *
How many requests in queue? - 0
cur_cycle: 30487 , next_cycle: 30487
[Subsystem::handle] Received nice instruction request @sim_time= 30487
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000771b00000008
ipcSocketServer receive request data: 000000000000771b
* * * * Request Simulate to 30491 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262146, 1024)
ddd, is_drv=False
cur_cycle: 30487 , next_cycle: 30491
[Subsystem::handle] Received handshake request @sim_time= 30491
----------------------------------------------------
ipcSocketServer receive request header: 02000000000000771b0000000c
ipcSocketServer receive request data: 02e7b00b0004000400000000
* * * * Request Simulate to 30491 * * * *
How many requests in queue? - 0
cur_cycle: 30491 , next_cycle: 30491
[Subsystem::handle] Received nice instruction request @sim_time= 30491
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000771f00000008
ipcSocketServer receive request data: 000000000000771f
* * * * Request Simulate to 30495 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262148, 0)
ddd, is_drv=False
cur_cycle: 30491 , next_cycle: 30495
[Subsystem::handle] Received handshake request @sim_time= 30495
----------------------------------------------------
ipcSocketServer receive request header: 02000000000000771f0000000c
ipcSocketServer receive request data: a807e78b0000000000000000
* * * * Request Simulate to 30495 * * * *
How many requests in queue? - 0
cur_cycle: 30495 , next_cycle: 30495
[Subsystem::handle] Received nice instruction request @sim_time= 30495
----------------------------------------------------
ipcSocketServer receive request header: ****************1f00000000
* * * * Request Simulate to 30495 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle vs_drv(a807e78b, 0, 0)
Verifying, current_cycle:30495 - sychronizing_npus:False - target_sim_cycle:30495
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[True, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 9, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: byte_base: 0x0, byte_stride: (512, 512), size: (256, 1, 1), type: BF, width: 16
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30495 , next_cycle: 30495
dispatching: Pid: 9, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received check request @sim_time= 30495
----------------------------------------------------
ipcSocketServer receive request header: ****************1f00000000
* * * * Request Simulate to 30495 * * * *
How many requests in queue? - 0
NPU Group.ID: 1.0, Start: Pid: 9, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30495, 30512), #cycle: 17
0 - SRAMBank 1 from 0x0000_0000 to 0x0000_ffe0, Cycle cost: Rin1=16, Rin2=0, Rorig=0, Wout=0, Rcomm=0, Wcomm=0, and Total=16
1 - VPU with 16 Engines each receives 2×32bit inputs, Cycle cost: VectorProcess=11, Latency=1
cur_cycle: 30495 , next_cycle: 30496
[Subsystem::handle] Received check request @sim_time= 30495
----------------------------------------------------
ipcSocketServer receive request header: ****************2000000000
* * * * Request Simulate to 30496 * * * *
How many requests in queue? - 0
cur_cycle: 30495 , next_cycle: 30512
[Subsystem::handle] Received check request @sim_time= 30496
----------------------------------------------------
ipcSocketServer receive request header: ****************3000000000
* * * * Request Simulate to 30512 * * * *
How many requests in queue? - 0
cur_cycle: 30496 , next_cycle: 30512
[Subsystem::handle] Received check request @sim_time= 30512
----------------------------------------------------
ipcSocketServer receive request header: ****************3000000000
* * * * Request Simulate to 30512 * * * *
How many requests in queue? - 0
NPU Core 4: Stored return value 3221094400 for prim_id 9, inst_id 4
NPU Group.ID: 1.0, Finish: Pid: 9, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30495, 30512), #cycle: 17
Generated response for inst_id 4, type nice, ret 3221094400
cur_cycle: 30512 , next_cycle: 30513
[Subsystem::handle] Received check request @sim_time= 30512
[Subsystem::check_handler] check nice instruction response: id=0, type=nice, ret=0xbffe0000
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000774c00000008
ipcSocketServer receive request data: 000000000000774c
* * * * Request Simulate to 30540 * * * *
How many requests in queue? - 0
cur_cycle: 30512 , next_cycle: 30540
[Subsystem::handle] Received handshake request @sim_time= 30540
----------------------------------------------------
ipcSocketServer receive request header: 02000000000000774c0000000c
ipcSocketServer receive request data: 00e7b00b0000000100000002
* * * * Request Simulate to 30540 * * * *
How many requests in queue? - 0
cur_cycle: 30540 , next_cycle: 30540
[Subsystem::handle] Received nice instruction request @sim_time= 30540
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000775900000008
ipcSocketServer receive request data: 0000000000007759
* * * * Request Simulate to 30553 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 1, 2)
Verifying, current_cycle:30540 - sychronizing_npus:False - target_sim_cycle:30553
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[True, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 10, PrimName.GROUP_MASK, group/mask: 1/2, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30540 , next_cycle: 30553
dispatching: Pid: 10, PrimName.GROUP_MASK, group/mask: 1/2, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 30553
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000077590000000c
ipcSocketServer receive request data: 02e7b00b000400000000090a
* * * * Request Simulate to 30553 * * * *
How many requests in queue? - 0
cur_cycle: 30553 , next_cycle: 30553
[Subsystem::handle] Received nice instruction request @sim_time= 30553
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000775d00000008
ipcSocketServer receive request data: 000000000000775d
* * * * Request Simulate to 30557 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262144, 2314)
ddd, is_drv=False
cur_cycle: 30553 , next_cycle: 30557
[Subsystem::handle] Received handshake request @sim_time= 30557
----------------------------------------------------
ipcSocketServer receive request header: 02000000000000775d0000000c
ipcSocketServer receive request data: 02e7b00b0004000200000400
* * * * Request Simulate to 30557 * * * *
How many requests in queue? - 0
cur_cycle: 30557 , next_cycle: 30557
[Subsystem::handle] Received nice instruction request @sim_time= 30557
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000776100000008
ipcSocketServer receive request data: 0000000000007761
* * * * Request Simulate to 30561 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262146, 1024)
ddd, is_drv=False
cur_cycle: 30557 , next_cycle: 30561
[Subsystem::handle] Received handshake request @sim_time= 30561
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000077610000000c
ipcSocketServer receive request data: 02e7b00b0004000400000000
* * * * Request Simulate to 30561 * * * *
How many requests in queue? - 0
cur_cycle: 30561 , next_cycle: 30561
[Subsystem::handle] Received nice instruction request @sim_time= 30561
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000776500000008
ipcSocketServer receive request data: 0000000000007765
* * * * Request Simulate to 30565 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262148, 0)
ddd, is_drv=False
cur_cycle: 30561 , next_cycle: 30565
[Subsystem::handle] Received handshake request @sim_time= 30565
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000077650000000c
ipcSocketServer receive request data: a807e78b0000000000000000
* * * * Request Simulate to 30565 * * * *
How many requests in queue? - 0
cur_cycle: 30565 , next_cycle: 30565
[Subsystem::handle] Received nice instruction request @sim_time= 30565
----------------------------------------------------
ipcSocketServer receive request header: ****************6500000000
* * * * Request Simulate to 30565 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle vs_drv(a807e78b, 0, 0)
Verifying, current_cycle:30565 - sychronizing_npus:False - target_sim_cycle:30565
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, True, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 11, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: byte_base: 0x0, byte_stride: (512, 512), size: (256, 1, 1), type: BF, width: 16
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30565 , next_cycle: 30565
dispatching: Pid: 11, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received check request @sim_time= 30565
----------------------------------------------------
ipcSocketServer receive request header: ****************6500000000
* * * * Request Simulate to 30565 * * * *
How many requests in queue? - 0
NPU Group.ID: 1.1, Start: Pid: 11, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30565, 30582), #cycle: 17
0 - SRAMBank 1 from 0x0000_0000 to 0x0000_ffe0, Cycle cost: Rin1=16, Rin2=0, Rorig=0, Wout=0, Rcomm=0, Wcomm=0, and Total=16
1 - VPU with 16 Engines each receives 2×32bit inputs, Cycle cost: VectorProcess=11, Latency=1
cur_cycle: 30565 , next_cycle: 30566
[Subsystem::handle] Received check request @sim_time= 30565
----------------------------------------------------
ipcSocketServer receive request header: ****************6600000000
* * * * Request Simulate to 30566 * * * *
How many requests in queue? - 0
cur_cycle: 30565 , next_cycle: 30582
[Subsystem::handle] Received check request @sim_time= 30566
----------------------------------------------------
ipcSocketServer receive request header: ****************7600000000
* * * * Request Simulate to 30582 * * * *
How many requests in queue? - 0
cur_cycle: 30566 , next_cycle: 30582
[Subsystem::handle] Received check request @sim_time= 30582
----------------------------------------------------
ipcSocketServer receive request header: ****************7600000000
* * * * Request Simulate to 30582 * * * *
How many requests in queue? - 0
NPU Core 5: Stored return value 1079508992 for prim_id 11, inst_id 5
NPU Group.ID: 1.1, Finish: Pid: 11, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30565, 30582), #cycle: 17
Generated response for inst_id 5, type nice, ret 1079508992
cur_cycle: 30582 , next_cycle: 30583
[Subsystem::handle] Received check request @sim_time= 30582
[Subsystem::check_handler] check nice instruction response: id=0, type=nice, ret=0x40580000
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000779200000008
ipcSocketServer receive request data: 0000000000007792
* * * * Request Simulate to 30610 * * * *
How many requests in queue? - 0
cur_cycle: 30582 , next_cycle: 30610
[Subsystem::handle] Received handshake request @sim_time= 30610
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000077920000000c
ipcSocketServer receive request data: 00e7b00b0000000100000004
* * * * Request Simulate to 30610 * * * *
How many requests in queue? - 0
cur_cycle: 30610 , next_cycle: 30610
[Subsystem::handle] Received nice instruction request @sim_time= 30610
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000779f00000008
ipcSocketServer receive request data: 000000000000779f
* * * * Request Simulate to 30623 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 1, 4)
Verifying, current_cycle:30610 - sychronizing_npus:False - target_sim_cycle:30623
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, True, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 12, PrimName.GROUP_MASK, group/mask: 1/4, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30610 , next_cycle: 30623
dispatching: Pid: 12, PrimName.GROUP_MASK, group/mask: 1/4, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 30623
----------------------------------------------------
ipcSocketServer receive request header: 02000000000000779f0000000c
ipcSocketServer receive request data: 02e7b00b000400000000090a
* * * * Request Simulate to 30623 * * * *
How many requests in queue? - 0
cur_cycle: 30623 , next_cycle: 30623
[Subsystem::handle] Received nice instruction request @sim_time= 30623
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000077a300000008
ipcSocketServer receive request data: 00000000000077a3
* * * * Request Simulate to 30627 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262144, 2314)
ddd, is_drv=False
cur_cycle: 30623 , next_cycle: 30627
[Subsystem::handle] Received handshake request @sim_time= 30627
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000077a30000000c
ipcSocketServer receive request data: 02e7b00b0004000200000400
* * * * Request Simulate to 30627 * * * *
How many requests in queue? - 0
cur_cycle: 30627 , next_cycle: 30627
[Subsystem::handle] Received nice instruction request @sim_time= 30627
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000077a700000008
ipcSocketServer receive request data: 00000000000077a7
* * * * Request Simulate to 30631 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262146, 1024)
ddd, is_drv=False
cur_cycle: 30627 , next_cycle: 30631
[Subsystem::handle] Received handshake request @sim_time= 30631
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000077a70000000c
ipcSocketServer receive request data: 02e7b00b0004000400000000
* * * * Request Simulate to 30631 * * * *
How many requests in queue? - 0
cur_cycle: 30631 , next_cycle: 30631
[Subsystem::handle] Received nice instruction request @sim_time= 30631
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000077ab00000008
ipcSocketServer receive request data: 00000000000077ab
* * * * Request Simulate to 30635 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262148, 0)
ddd, is_drv=False
cur_cycle: 30631 , next_cycle: 30635
[Subsystem::handle] Received handshake request @sim_time= 30635
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000077ab0000000c
ipcSocketServer receive request data: a807e78b0000000000000000
* * * * Request Simulate to 30635 * * * *
How many requests in queue? - 0
cur_cycle: 30635 , next_cycle: 30635
[Subsystem::handle] Received nice instruction request @sim_time= 30635
----------------------------------------------------
ipcSocketServer receive request header: ****************ab00000000
* * * * Request Simulate to 30635 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle vs_drv(a807e78b, 0, 0)
Verifying, current_cycle:30635 - sychronizing_npus:False - target_sim_cycle:30635
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, True, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 13, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: byte_base: 0x0, byte_stride: (512, 512), size: (256, 1, 1), type: BF, width: 16
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30635 , next_cycle: 30635
dispatching: Pid: 13, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received check request @sim_time= 30635
----------------------------------------------------
ipcSocketServer receive request header: ****************ab00000000
* * * * Request Simulate to 30635 * * * *
How many requests in queue? - 0
NPU Group.ID: 1.2, Start: Pid: 13, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30635, 30652), #cycle: 17
0 - SRAMBank 1 from 0x0000_0000 to 0x0000_ffe0, Cycle cost: Rin1=16, Rin2=0, Rorig=0, Wout=0, Rcomm=0, Wcomm=0, and Total=16
1 - VPU with 16 Engines each receives 2×32bit inputs, Cycle cost: VectorProcess=11, Latency=1
cur_cycle: 30635 , next_cycle: 30636
[Subsystem::handle] Received check request @sim_time= 30635
----------------------------------------------------
ipcSocketServer receive request header: ****************ac00000000
* * * * Request Simulate to 30636 * * * *
How many requests in queue? - 0
cur_cycle: 30635 , next_cycle: 30652
[Subsystem::handle] Received check request @sim_time= 30636
----------------------------------------------------
ipcSocketServer receive request header: ****************bc00000000
* * * * Request Simulate to 30652 * * * *
How many requests in queue? - 0
cur_cycle: 30636 , next_cycle: 30652
[Subsystem::handle] Received check request @sim_time= 30652
----------------------------------------------------
ipcSocketServer receive request header: ****************bc00000000
* * * * Request Simulate to 30652 * * * *
How many requests in queue? - 0
NPU Core 6: Stored return value 3231137792 for prim_id 13, inst_id 6
NPU Group.ID: 1.2, Finish: Pid: 13, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30635, 30652), #cycle: 17
Generated response for inst_id 6, type nice, ret 3231137792
cur_cycle: 30652 , next_cycle: 30653
[Subsystem::handle] Received check request @sim_time= 30652
[Subsystem::check_handler] check nice instruction response: id=0, type=nice, ret=0xc0974000
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000077d800000008
ipcSocketServer receive request data: 00000000000077d8
* * * * Request Simulate to 30680 * * * *
How many requests in queue? - 0
cur_cycle: 30652 , next_cycle: 30680
[Subsystem::handle] Received handshake request @sim_time= 30680
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000077d80000000c
ipcSocketServer receive request data: 00e7b00b0000000100000008
* * * * Request Simulate to 30680 * * * *
How many requests in queue? - 0
cur_cycle: 30680 , next_cycle: 30680
[Subsystem::handle] Received nice instruction request @sim_time= 30680
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000077e500000008
ipcSocketServer receive request data: 00000000000077e5
* * * * Request Simulate to 30693 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 1, 8)
Verifying, current_cycle:30680 - sychronizing_npus:False - target_sim_cycle:30693
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, True, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 14, PrimName.GROUP_MASK, group/mask: 1/8, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30680 , next_cycle: 30693
dispatching: Pid: 14, PrimName.GROUP_MASK, group/mask: 1/8, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 30693
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000077e50000000c
ipcSocketServer receive request data: 02e7b00b000400000000090a
* * * * Request Simulate to 30693 * * * *
How many requests in queue? - 0
cur_cycle: 30693 , next_cycle: 30693
[Subsystem::handle] Received nice instruction request @sim_time= 30693
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000077e900000008
ipcSocketServer receive request data: 00000000000077e9
* * * * Request Simulate to 30697 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262144, 2314)
ddd, is_drv=False
cur_cycle: 30693 , next_cycle: 30697
[Subsystem::handle] Received handshake request @sim_time= 30697
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000077e90000000c
ipcSocketServer receive request data: 02e7b00b0004000200000400
* * * * Request Simulate to 30697 * * * *
How many requests in queue? - 0
cur_cycle: 30697 , next_cycle: 30697
[Subsystem::handle] Received nice instruction request @sim_time= 30697
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000077ed00000008
ipcSocketServer receive request data: 00000000000077ed
* * * * Request Simulate to 30701 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262146, 1024)
ddd, is_drv=False
cur_cycle: 30697 , next_cycle: 30701
[Subsystem::handle] Received handshake request @sim_time= 30701
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000077ed0000000c
ipcSocketServer receive request data: 02e7b00b0004000400000000
* * * * Request Simulate to 30701 * * * *
How many requests in queue? - 0
cur_cycle: 30701 , next_cycle: 30701
[Subsystem::handle] Received nice instruction request @sim_time= 30701
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000077f100000008
ipcSocketServer receive request data: 00000000000077f1
* * * * Request Simulate to 30705 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262148, 0)
ddd, is_drv=False
cur_cycle: 30701 , next_cycle: 30705
[Subsystem::handle] Received handshake request @sim_time= 30705
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000077f10000000c
ipcSocketServer receive request data: a807e78b0000000000000000
* * * * Request Simulate to 30705 * * * *
How many requests in queue? - 0
cur_cycle: 30705 , next_cycle: 30705
[Subsystem::handle] Received nice instruction request @sim_time= 30705
----------------------------------------------------
ipcSocketServer receive request header: ****************f100000000
* * * * Request Simulate to 30705 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle vs_drv(a807e78b, 0, 0)
Verifying, current_cycle:30705 - sychronizing_npus:False - target_sim_cycle:30705
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, True]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 15, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: byte_base: 0x0, byte_stride: (512, 512), size: (256, 1, 1), type: BF, width: 16
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30705 , next_cycle: 30705
dispatching: Pid: 15, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received check request @sim_time= 30705
----------------------------------------------------
ipcSocketServer receive request header: ****************f100000000
* * * * Request Simulate to 30705 * * * *
How many requests in queue? - 0
NPU Group.ID: 1.3, Start: Pid: 15, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30705, 30722), #cycle: 17
0 - SRAMBank 1 from 0x0000_0000 to 0x0000_ffe0, Cycle cost: Rin1=16, Rin2=0, Rorig=0, Wout=0, Rcomm=0, Wcomm=0, and Total=16
1 - VPU with 16 Engines each receives 2×32bit inputs, Cycle cost: VectorProcess=11, Latency=1
cur_cycle: 30705 , next_cycle: 30706
[Subsystem::handle] Received check request @sim_time= 30705
----------------------------------------------------
ipcSocketServer receive request header: ****************f200000000
* * * * Request Simulate to 30706 * * * *
How many requests in queue? - 0
cur_cycle: 30705 , next_cycle: 30722
[Subsystem::handle] Received check request @sim_time= 30706
----------------------------------------------------
ipcSocketServer receive request header: ****************0200000000
* * * * Request Simulate to 30722 * * * *
How many requests in queue? - 0
cur_cycle: 30706 , next_cycle: 30722
[Subsystem::handle] Received check request @sim_time= 30722
----------------------------------------------------
ipcSocketServer receive request header: ****************0200000000
* * * * Request Simulate to 30722 * * * *
How many requests in queue? - 0
NPU Core 7: Stored return value 1081999360 for prim_id 15, inst_id 7
NPU Group.ID: 1.3, Finish: Pid: 15, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30705, 30722), #cycle: 17
Generated response for inst_id 7, type nice, ret 1081999360
cur_cycle: 30722 , next_cycle: 30723
[Subsystem::handle] Received check request @sim_time= 30722
[Subsystem::check_handler] check nice instruction response: id=0, type=nice, ret=0x407e0000
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000783700000008
ipcSocketServer receive request data: 0000000000007837
* * * * Request Simulate to 30775 * * * *
How many requests in queue? - 0
cur_cycle: 30722 , next_cycle: 30775
[Subsystem::handle] Received handshake request @sim_time= 30775
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000078370000000c
ipcSocketServer receive request data: 00e7b00b0000000200000001
* * * * Request Simulate to 30775 * * * *
How many requests in queue? - 0
cur_cycle: 30775 , next_cycle: 30775
[Subsystem::handle] Received nice instruction request @sim_time= 30775
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000784400000008
ipcSocketServer receive request data: 0000000000007844
* * * * Request Simulate to 30788 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 2, 1)
Verifying, current_cycle:30775 - sychronizing_npus:False - target_sim_cycle:30788
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, True]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 16, PrimName.GROUP_MASK, group/mask: 2/1, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30775 , next_cycle: 30788
dispatching: Pid: 16, PrimName.GROUP_MASK, group/mask: 2/1, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 30788
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000078440000000c
ipcSocketServer receive request data: 02e7b00b000400000000090a
* * * * Request Simulate to 30788 * * * *
How many requests in queue? - 0
cur_cycle: 30788 , next_cycle: 30788
[Subsystem::handle] Received nice instruction request @sim_time= 30788
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000784800000008
ipcSocketServer receive request data: 0000000000007848
* * * * Request Simulate to 30792 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262144, 2314)
ddd, is_drv=False
cur_cycle: 30788 , next_cycle: 30792
[Subsystem::handle] Received handshake request @sim_time= 30792
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000078480000000c
ipcSocketServer receive request data: 02e7b00b0004000200000400
* * * * Request Simulate to 30792 * * * *
How many requests in queue? - 0
cur_cycle: 30792 , next_cycle: 30792
[Subsystem::handle] Received nice instruction request @sim_time= 30792
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000784c00000008
ipcSocketServer receive request data: 000000000000784c
* * * * Request Simulate to 30796 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262146, 1024)
ddd, is_drv=False
cur_cycle: 30792 , next_cycle: 30796
[Subsystem::handle] Received handshake request @sim_time= 30796
----------------------------------------------------
ipcSocketServer receive request header: 02000000000000784c0000000c
ipcSocketServer receive request data: 02e7b00b0004000400000000
* * * * Request Simulate to 30796 * * * *
How many requests in queue? - 0
cur_cycle: 30796 , next_cycle: 30796
[Subsystem::handle] Received nice instruction request @sim_time= 30796
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000785000000008
ipcSocketServer receive request data: 0000000000007850
* * * * Request Simulate to 30800 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262148, 0)
ddd, is_drv=False
cur_cycle: 30796 , next_cycle: 30800
[Subsystem::handle] Received handshake request @sim_time= 30800
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000078500000000c
ipcSocketServer receive request data: a807e78b0000000000000000
* * * * Request Simulate to 30800 * * * *
How many requests in queue? - 0
cur_cycle: 30800 , next_cycle: 30800
[Subsystem::handle] Received nice instruction request @sim_time= 30800
----------------------------------------------------
ipcSocketServer receive request header: ****************5000000000
* * * * Request Simulate to 30800 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle vs_drv(a807e78b, 0, 0)
Verifying, current_cycle:30800 - sychronizing_npus:False - target_sim_cycle:30800
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[True, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 17, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: byte_base: 0x0, byte_stride: (512, 512), size: (256, 1, 1), type: BF, width: 16
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30800 , next_cycle: 30800
dispatching: Pid: 17, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received check request @sim_time= 30800
----------------------------------------------------
ipcSocketServer receive request header: ****************5000000000
* * * * Request Simulate to 30800 * * * *
How many requests in queue? - 0
NPU Group.ID: 2.0, Start: Pid: 17, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30800, 30817), #cycle: 17
0 - SRAMBank 1 from 0x0000_0000 to 0x0000_ffe0, Cycle cost: Rin1=16, Rin2=0, Rorig=0, Wout=0, Rcomm=0, Wcomm=0, and Total=16
1 - VPU with 16 Engines each receives 2×32bit inputs, Cycle cost: VectorProcess=11, Latency=1
cur_cycle: 30800 , next_cycle: 30801
[Subsystem::handle] Received check request @sim_time= 30800
----------------------------------------------------
ipcSocketServer receive request header: ****************5100000000
* * * * Request Simulate to 30801 * * * *
How many requests in queue? - 0
cur_cycle: 30800 , next_cycle: 30817
[Subsystem::handle] Received check request @sim_time= 30801
----------------------------------------------------
ipcSocketServer receive request header: ****************6100000000
* * * * Request Simulate to 30817 * * * *
How many requests in queue? - 0
cur_cycle: 30801 , next_cycle: 30817
[Subsystem::handle] Received check request @sim_time= 30817
----------------------------------------------------
ipcSocketServer receive request header: ****************6100000000
* * * * Request Simulate to 30817 * * * *
How many requests in queue? - 0
NPU Core 8: Stored return value 1073348608 for prim_id 17, inst_id 8
NPU Group.ID: 2.0, Finish: Pid: 17, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30800, 30817), #cycle: 17
Generated response for inst_id 8, type nice, ret 1073348608
cur_cycle: 30817 , next_cycle: 30818
[Subsystem::handle] Received check request @sim_time= 30817
[Subsystem::check_handler] check nice instruction response: id=0, type=nice, ret=0x3ffa0000
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000787d00000008
ipcSocketServer receive request data: 000000000000787d
* * * * Request Simulate to 30845 * * * *
How many requests in queue? - 0
cur_cycle: 30817 , next_cycle: 30845
[Subsystem::handle] Received handshake request @sim_time= 30845
----------------------------------------------------
ipcSocketServer receive request header: 02000000000000787d0000000c
ipcSocketServer receive request data: 00e7b00b0000000200000002
* * * * Request Simulate to 30845 * * * *
How many requests in queue? - 0
cur_cycle: 30845 , next_cycle: 30845
[Subsystem::handle] Received nice instruction request @sim_time= 30845
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000788a00000008
ipcSocketServer receive request data: 000000000000788a
* * * * Request Simulate to 30858 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 2, 2)
Verifying, current_cycle:30845 - sychronizing_npus:False - target_sim_cycle:30858
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[True, False, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 18, PrimName.GROUP_MASK, group/mask: 2/2, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30845 , next_cycle: 30858
dispatching: Pid: 18, PrimName.GROUP_MASK, group/mask: 2/2, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 30858
----------------------------------------------------
ipcSocketServer receive request header: 02000000000000788a0000000c
ipcSocketServer receive request data: 02e7b00b000400000000090a
* * * * Request Simulate to 30858 * * * *
How many requests in queue? - 0
cur_cycle: 30858 , next_cycle: 30858
[Subsystem::handle] Received nice instruction request @sim_time= 30858
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000788e00000008
ipcSocketServer receive request data: 000000000000788e
* * * * Request Simulate to 30862 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262144, 2314)
ddd, is_drv=False
cur_cycle: 30858 , next_cycle: 30862
[Subsystem::handle] Received handshake request @sim_time= 30862
----------------------------------------------------
ipcSocketServer receive request header: 02000000000000788e0000000c
ipcSocketServer receive request data: 02e7b00b0004000200000400
* * * * Request Simulate to 30862 * * * *
How many requests in queue? - 0
cur_cycle: 30862 , next_cycle: 30862
[Subsystem::handle] Received nice instruction request @sim_time= 30862
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000789200000008
ipcSocketServer receive request data: 0000000000007892
* * * * Request Simulate to 30866 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262146, 1024)
ddd, is_drv=False
cur_cycle: 30862 , next_cycle: 30866
[Subsystem::handle] Received handshake request @sim_time= 30866
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000078920000000c
ipcSocketServer receive request data: 02e7b00b0004000400000000
* * * * Request Simulate to 30866 * * * *
How many requests in queue? - 0
cur_cycle: 30866 , next_cycle: 30866
[Subsystem::handle] Received nice instruction request @sim_time= 30866
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000789600000008
ipcSocketServer receive request data: 0000000000007896
* * * * Request Simulate to 30870 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262148, 0)
ddd, is_drv=False
cur_cycle: 30866 , next_cycle: 30870
[Subsystem::handle] Received handshake request @sim_time= 30870
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000078960000000c
ipcSocketServer receive request data: a807e78b0000000000000000
* * * * Request Simulate to 30870 * * * *
How many requests in queue? - 0
cur_cycle: 30870 , next_cycle: 30870
[Subsystem::handle] Received nice instruction request @sim_time= 30870
----------------------------------------------------
ipcSocketServer receive request header: ****************9600000000
* * * * Request Simulate to 30870 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle vs_drv(a807e78b, 0, 0)
Verifying, current_cycle:30870 - sychronizing_npus:False - target_sim_cycle:30870
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, True, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 19, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: byte_base: 0x0, byte_stride: (512, 512), size: (256, 1, 1), type: BF, width: 16
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30870 , next_cycle: 30870
dispatching: Pid: 19, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received check request @sim_time= 30870
----------------------------------------------------
ipcSocketServer receive request header: ****************9600000000
* * * * Request Simulate to 30870 * * * *
How many requests in queue? - 0
NPU Group.ID: 2.1, Start: Pid: 19, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30870, 30887), #cycle: 17
0 - SRAMBank 1 from 0x0000_0000 to 0x0000_ffe0, Cycle cost: Rin1=16, Rin2=0, Rorig=0, Wout=0, Rcomm=0, Wcomm=0, and Total=16
1 - VPU with 16 Engines each receives 2×32bit inputs, Cycle cost: VectorProcess=11, Latency=1
cur_cycle: 30870 , next_cycle: 30871
[Subsystem::handle] Received check request @sim_time= 30870
----------------------------------------------------
ipcSocketServer receive request header: ****************9700000000
* * * * Request Simulate to 30871 * * * *
How many requests in queue? - 0
cur_cycle: 30870 , next_cycle: 30887
[Subsystem::handle] Received check request @sim_time= 30871
----------------------------------------------------
ipcSocketServer receive request header: ****************a700000000
* * * * Request Simulate to 30887 * * * *
How many requests in queue? - 0
cur_cycle: 30871 , next_cycle: 30887
[Subsystem::handle] Received check request @sim_time= 30887
----------------------------------------------------
ipcSocketServer receive request header: ****************a700000000
* * * * Request Simulate to 30887 * * * *
How many requests in queue? - 0
NPU Core 9: Stored return value 1082785792 for prim_id 19, inst_id 9
NPU Group.ID: 2.1, Finish: Pid: 19, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30870, 30887), #cycle: 17
Generated response for inst_id 9, type nice, ret 1082785792
cur_cycle: 30887 , next_cycle: 30888
[Subsystem::handle] Received check request @sim_time= 30887
[Subsystem::check_handler] check nice instruction response: id=0, type=nice, ret=0x408a0000
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000078c300000008
ipcSocketServer receive request data: 00000000000078c3
* * * * Request Simulate to 30915 * * * *
How many requests in queue? - 0
cur_cycle: 30887 , next_cycle: 30915
[Subsystem::handle] Received handshake request @sim_time= 30915
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000078c30000000c
ipcSocketServer receive request data: 00e7b00b0000000200000004
* * * * Request Simulate to 30915 * * * *
How many requests in queue? - 0
cur_cycle: 30915 , next_cycle: 30915
[Subsystem::handle] Received nice instruction request @sim_time= 30915
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000078d000000008
ipcSocketServer receive request data: 00000000000078d0
* * * * Request Simulate to 30928 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 2, 4)
Verifying, current_cycle:30915 - sychronizing_npus:False - target_sim_cycle:30928
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, True, False, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 20, PrimName.GROUP_MASK, group/mask: 2/4, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30915 , next_cycle: 30928
dispatching: Pid: 20, PrimName.GROUP_MASK, group/mask: 2/4, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 30928
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000078d00000000c
ipcSocketServer receive request data: 02e7b00b000400000000090a
* * * * Request Simulate to 30928 * * * *
How many requests in queue? - 0
cur_cycle: 30928 , next_cycle: 30928
[Subsystem::handle] Received nice instruction request @sim_time= 30928
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000078d400000008
ipcSocketServer receive request data: 00000000000078d4
* * * * Request Simulate to 30932 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262144, 2314)
ddd, is_drv=False
cur_cycle: 30928 , next_cycle: 30932
[Subsystem::handle] Received handshake request @sim_time= 30932
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000078d40000000c
ipcSocketServer receive request data: 02e7b00b0004000200000400
* * * * Request Simulate to 30932 * * * *
How many requests in queue? - 0
cur_cycle: 30932 , next_cycle: 30932
[Subsystem::handle] Received nice instruction request @sim_time= 30932
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000078d800000008
ipcSocketServer receive request data: 00000000000078d8
* * * * Request Simulate to 30936 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262146, 1024)
ddd, is_drv=False
cur_cycle: 30932 , next_cycle: 30936
[Subsystem::handle] Received handshake request @sim_time= 30936
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000078d80000000c
ipcSocketServer receive request data: 02e7b00b0004000400000000
* * * * Request Simulate to 30936 * * * *
How many requests in queue? - 0
cur_cycle: 30936 , next_cycle: 30936
[Subsystem::handle] Received nice instruction request @sim_time= 30936
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000078dc00000008
ipcSocketServer receive request data: 00000000000078dc
* * * * Request Simulate to 30940 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262148, 0)
ddd, is_drv=False
cur_cycle: 30936 , next_cycle: 30940
[Subsystem::handle] Received handshake request @sim_time= 30940
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000078dc0000000c
ipcSocketServer receive request data: a807e78b0000000000000000
* * * * Request Simulate to 30940 * * * *
How many requests in queue? - 0
cur_cycle: 30940 , next_cycle: 30940
[Subsystem::handle] Received nice instruction request @sim_time= 30940
----------------------------------------------------
ipcSocketServer receive request header: ****************dc00000000
* * * * Request Simulate to 30940 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle vs_drv(a807e78b, 0, 0)
Verifying, current_cycle:30940 - sychronizing_npus:False - target_sim_cycle:30940
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, True, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 21, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: byte_base: 0x0, byte_stride: (512, 512), size: (256, 1, 1), type: BF, width: 16
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30940 , next_cycle: 30940
dispatching: Pid: 21, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received check request @sim_time= 30940
----------------------------------------------------
ipcSocketServer receive request header: ****************dc00000000
* * * * Request Simulate to 30940 * * * *
How many requests in queue? - 0
NPU Group.ID: 2.2, Start: Pid: 21, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30940, 30957), #cycle: 17
0 - SRAMBank 1 from 0x0000_0000 to 0x0000_ffe0, Cycle cost: Rin1=16, Rin2=0, Rorig=0, Wout=0, Rcomm=0, Wcomm=0, and Total=16
1 - VPU with 16 Engines each receives 2×32bit inputs, Cycle cost: VectorProcess=11, Latency=1
cur_cycle: 30940 , next_cycle: 30941
[Subsystem::handle] Received check request @sim_time= 30940
----------------------------------------------------
ipcSocketServer receive request header: ****************dd00000000
* * * * Request Simulate to 30941 * * * *
How many requests in queue? - 0
cur_cycle: 30940 , next_cycle: 30957
[Subsystem::handle] Received check request @sim_time= 30941
----------------------------------------------------
ipcSocketServer receive request header: ****************ed00000000
* * * * Request Simulate to 30957 * * * *
How many requests in queue? - 0
cur_cycle: 30941 , next_cycle: 30957
[Subsystem::handle] Received check request @sim_time= 30957
----------------------------------------------------
ipcSocketServer receive request header: ****************ed00000000
* * * * Request Simulate to 30957 * * * *
How many requests in queue? - 0
NPU Core 10: Stored return value 1093083136 for prim_id 21, inst_id 10
NPU Group.ID: 2.2, Finish: Pid: 21, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(30940, 30957), #cycle: 17
Generated response for inst_id 10, type nice, ret 1093083136
cur_cycle: 30957 , next_cycle: 30958
[Subsystem::handle] Received check request @sim_time= 30957
[Subsystem::check_handler] check nice instruction response: id=0, type=nice, ret=0x41272000
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000790900000008
ipcSocketServer receive request data: 0000000000007909
* * * * Request Simulate to 30985 * * * *
How many requests in queue? - 0
cur_cycle: 30957 , next_cycle: 30985
[Subsystem::handle] Received handshake request @sim_time= 30985
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000079090000000c
ipcSocketServer receive request data: 00e7b00b0000000200000008
* * * * Request Simulate to 30985 * * * *
How many requests in queue? - 0
cur_cycle: 30985 , next_cycle: 30985
[Subsystem::handle] Received nice instruction request @sim_time= 30985
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000791600000008
ipcSocketServer receive request data: 0000000000007916
* * * * Request Simulate to 30998 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 2, 8)
Verifying, current_cycle:30985 - sychronizing_npus:False - target_sim_cycle:30998
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, True, False]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 22, PrimName.GROUP_MASK, group/mask: 2/8, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 30985 , next_cycle: 30998
dispatching: Pid: 22, PrimName.GROUP_MASK, group/mask: 2/8, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 30998
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000079160000000c
ipcSocketServer receive request data: 02e7b00b000400000000090a
* * * * Request Simulate to 30998 * * * *
How many requests in queue? - 0
cur_cycle: 30998 , next_cycle: 30998
[Subsystem::handle] Received nice instruction request @sim_time= 30998
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000791a00000008
ipcSocketServer receive request data: 000000000000791a
* * * * Request Simulate to 31002 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262144, 2314)
ddd, is_drv=False
cur_cycle: 30998 , next_cycle: 31002
[Subsystem::handle] Received handshake request @sim_time= 31002
----------------------------------------------------
ipcSocketServer receive request header: 02000000000000791a0000000c
ipcSocketServer receive request data: 02e7b00b0004000200000400
* * * * Request Simulate to 31002 * * * *
How many requests in queue? - 0
cur_cycle: 31002 , next_cycle: 31002
[Subsystem::handle] Received nice instruction request @sim_time= 31002
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000791e00000008
ipcSocketServer receive request data: 000000000000791e
* * * * Request Simulate to 31006 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262146, 1024)
ddd, is_drv=False
cur_cycle: 31002 , next_cycle: 31006
[Subsystem::handle] Received handshake request @sim_time= 31006
----------------------------------------------------
ipcSocketServer receive request header: 02000000000000791e0000000c
ipcSocketServer receive request data: 02e7b00b0004000400000000
* * * * Request Simulate to 31006 * * * *
How many requests in queue? - 0
cur_cycle: 31006 , next_cycle: 31006
[Subsystem::handle] Received nice instruction request @sim_time= 31006
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000792200000008
ipcSocketServer receive request data: 0000000000007922
* * * * Request Simulate to 31010 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262148, 0)
ddd, is_drv=False
cur_cycle: 31006 , next_cycle: 31010
[Subsystem::handle] Received handshake request @sim_time= 31010
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000079220000000c
ipcSocketServer receive request data: a807e78b0000000000000000
* * * * Request Simulate to 31010 * * * *
How many requests in queue? - 0
cur_cycle: 31010 , next_cycle: 31010
[Subsystem::handle] Received nice instruction request @sim_time= 31010
----------------------------------------------------
ipcSocketServer receive request header: ****************2200000000
* * * * Request Simulate to 31010 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle vs_drv(a807e78b, 0, 0)
Verifying, current_cycle:31010 - sychronizing_npus:False - target_sim_cycle:31010
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, True]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 23, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: byte_base: 0x0, byte_stride: (512, 512), size: (256, 1, 1), type: BF, width: 16
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 31010 , next_cycle: 31010
dispatching: Pid: 23, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received check request @sim_time= 31010
----------------------------------------------------
ipcSocketServer receive request header: ****************2200000000
* * * * Request Simulate to 31010 * * * *
How many requests in queue? - 0
NPU Group.ID: 2.3, Start: Pid: 23, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(31010, 31027), #cycle: 17
0 - SRAMBank 1 from 0x0000_0000 to 0x0000_ffe0, Cycle cost: Rin1=16, Rin2=0, Rorig=0, Wout=0, Rcomm=0, Wcomm=0, and Total=16
1 - VPU with 16 Engines each receives 2×32bit inputs, Cycle cost: VectorProcess=11, Latency=1
cur_cycle: 31010 , next_cycle: 31011
[Subsystem::handle] Received check request @sim_time= 31010
----------------------------------------------------
ipcSocketServer receive request header: ****************2300000000
* * * * Request Simulate to 31011 * * * *
How many requests in queue? - 0
cur_cycle: 31010 , next_cycle: 31027
[Subsystem::handle] Received check request @sim_time= 31011
----------------------------------------------------
ipcSocketServer receive request header: ****************3300000000
* * * * Request Simulate to 31027 * * * *
How many requests in queue? - 0
cur_cycle: 31011 , next_cycle: 31027
[Subsystem::handle] Received check request @sim_time= 31027
----------------------------------------------------
ipcSocketServer receive request header: ****************3300000000
* * * * Request Simulate to 31027 * * * *
How many requests in queue? - 0
NPU Core 11: Stored return value 1098973184 for prim_id 23, inst_id 11
NPU Group.ID: 2.3, Finish: Pid: 23, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(31010, 31027), #cycle: 17
Generated response for inst_id 11, type nice, ret 1098973184
cur_cycle: 31027 , next_cycle: 31028
[Subsystem::handle] Received check request @sim_time= 31027
[Subsystem::check_handler] check nice instruction response: id=0, type=nice, ret=0x41810000
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000796800000008
ipcSocketServer receive request data: 0000000000007968
* * * * Request Simulate to 31080 * * * *
How many requests in queue? - 0
cur_cycle: 31027 , next_cycle: 31080
[Subsystem::handle] Received handshake request @sim_time= 31080
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000079680000000c
ipcSocketServer receive request data: 00e7b00b0000000300000001
* * * * Request Simulate to 31080 * * * *
How many requests in queue? - 0
cur_cycle: 31080 , next_cycle: 31080
[Subsystem::handle] Received nice instruction request @sim_time= 31080
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000797500000008
ipcSocketServer receive request data: 0000000000007975
* * * * Request Simulate to 31093 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 3, 1)
Verifying, current_cycle:31080 - sychronizing_npus:False - target_sim_cycle:31093
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, True]
Group:3 - Mask:[False, False, False, False]
Verifying primitive: Pid: 24, PrimName.GROUP_MASK, group/mask: 3/1, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 31080 , next_cycle: 31093
dispatching: Pid: 24, PrimName.GROUP_MASK, group/mask: 3/1, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 31093
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000079750000000c
ipcSocketServer receive request data: 02e7b00b000400000000090a
* * * * Request Simulate to 31093 * * * *
How many requests in queue? - 0
cur_cycle: 31093 , next_cycle: 31093
[Subsystem::handle] Received nice instruction request @sim_time= 31093
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000797900000008
ipcSocketServer receive request data: 0000000000007979
* * * * Request Simulate to 31097 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262144, 2314)
ddd, is_drv=False
cur_cycle: 31093 , next_cycle: 31097
[Subsystem::handle] Received handshake request @sim_time= 31097
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000079790000000c
ipcSocketServer receive request data: 02e7b00b0004000200000400
* * * * Request Simulate to 31097 * * * *
How many requests in queue? - 0
cur_cycle: 31097 , next_cycle: 31097
[Subsystem::handle] Received nice instruction request @sim_time= 31097
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000797d00000008
ipcSocketServer receive request data: 000000000000797d
* * * * Request Simulate to 31101 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262146, 1024)
ddd, is_drv=False
cur_cycle: 31097 , next_cycle: 31101
[Subsystem::handle] Received handshake request @sim_time= 31101
----------------------------------------------------
ipcSocketServer receive request header: 02000000000000797d0000000c
ipcSocketServer receive request data: 02e7b00b0004000400000000
* * * * Request Simulate to 31101 * * * *
How many requests in queue? - 0
cur_cycle: 31101 , next_cycle: 31101
[Subsystem::handle] Received nice instruction request @sim_time= 31101
----------------------------------------------------
ipcSocketServer receive request header: 01000000000000798100000008
ipcSocketServer receive request data: 0000000000007981
* * * * Request Simulate to 31105 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262148, 0)
ddd, is_drv=False
cur_cycle: 31101 , next_cycle: 31105
[Subsystem::handle] Received handshake request @sim_time= 31105
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000079810000000c
ipcSocketServer receive request data: a807e78b0000000000000000
* * * * Request Simulate to 31105 * * * *
How many requests in queue? - 0
cur_cycle: 31105 , next_cycle: 31105
[Subsystem::handle] Received nice instruction request @sim_time= 31105
----------------------------------------------------
ipcSocketServer receive request header: ****************8100000000
* * * * Request Simulate to 31105 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle vs_drv(a807e78b, 0, 0)
Verifying, current_cycle:31105 - sychronizing_npus:False - target_sim_cycle:31105
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[True, False, False, False]
Verifying primitive: Pid: 25, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: byte_base: 0x0, byte_stride: (512, 512), size: (256, 1, 1), type: BF, width: 16
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 31105 , next_cycle: 31105
dispatching: Pid: 25, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received check request @sim_time= 31105
----------------------------------------------------
ipcSocketServer receive request header: ****************8100000000
* * * * Request Simulate to 31105 * * * *
How many requests in queue? - 0
NPU Group.ID: 3.0, Start: Pid: 25, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(31105, 31122), #cycle: 17
0 - SRAMBank 1 from 0x0000_0000 to 0x0000_ffe0, Cycle cost: Rin1=16, Rin2=0, Rorig=0, Wout=0, Rcomm=0, Wcomm=0, and Total=16
1 - VPU with 16 Engines each receives 2×32bit inputs, Cycle cost: VectorProcess=11, Latency=1
cur_cycle: 31105 , next_cycle: 31106
[Subsystem::handle] Received check request @sim_time= 31105
----------------------------------------------------
ipcSocketServer receive request header: ****************8200000000
* * * * Request Simulate to 31106 * * * *
How many requests in queue? - 0
cur_cycle: 31105 , next_cycle: 31122
[Subsystem::handle] Received check request @sim_time= 31106
----------------------------------------------------
ipcSocketServer receive request header: ****************9200000000
* * * * Request Simulate to 31122 * * * *
How many requests in queue? - 0
cur_cycle: 31106 , next_cycle: 31122
[Subsystem::handle] Received check request @sim_time= 31122
----------------------------------------------------
ipcSocketServer receive request header: ****************9200000000
* * * * Request Simulate to 31122 * * * *
How many requests in queue? - 0
NPU Core 12: Stored return value 3239813120 for prim_id 25, inst_id 12
NPU Group.ID: 3.0, Finish: Pid: 25, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(31105, 31122), #cycle: 17
Generated response for inst_id 12, type nice, ret 3239813120
cur_cycle: 31122 , next_cycle: 31123
[Subsystem::handle] Received check request @sim_time= 31122
[Subsystem::check_handler] check nice instruction response: id=0, type=nice, ret=0xc11ba000
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000079ae00000008
ipcSocketServer receive request data: 00000000000079ae
* * * * Request Simulate to 31150 * * * *
How many requests in queue? - 0
cur_cycle: 31122 , next_cycle: 31150
[Subsystem::handle] Received handshake request @sim_time= 31150
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000079ae0000000c
ipcSocketServer receive request data: 00e7b00b0000000300000002
* * * * Request Simulate to 31150 * * * *
How many requests in queue? - 0
cur_cycle: 31150 , next_cycle: 31150
[Subsystem::handle] Received nice instruction request @sim_time= 31150
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000079bb00000008
ipcSocketServer receive request data: 00000000000079bb
* * * * Request Simulate to 31163 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 3, 2)
Verifying, current_cycle:31150 - sychronizing_npus:False - target_sim_cycle:31163
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[True, False, False, False]
Verifying primitive: Pid: 26, PrimName.GROUP_MASK, group/mask: 3/2, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 31150 , next_cycle: 31163
dispatching: Pid: 26, PrimName.GROUP_MASK, group/mask: 3/2, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 31163
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000079bb0000000c
ipcSocketServer receive request data: 02e7b00b000400000000090a
* * * * Request Simulate to 31163 * * * *
How many requests in queue? - 0
cur_cycle: 31163 , next_cycle: 31163
[Subsystem::handle] Received nice instruction request @sim_time= 31163
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000079bf00000008
ipcSocketServer receive request data: 00000000000079bf
* * * * Request Simulate to 31167 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262144, 2314)
ddd, is_drv=False
cur_cycle: 31163 , next_cycle: 31167
[Subsystem::handle] Received handshake request @sim_time= 31167
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000079bf0000000c
ipcSocketServer receive request data: 02e7b00b0004000200000400
* * * * Request Simulate to 31167 * * * *
How many requests in queue? - 0
cur_cycle: 31167 , next_cycle: 31167
[Subsystem::handle] Received nice instruction request @sim_time= 31167
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000079c300000008
ipcSocketServer receive request data: 00000000000079c3
* * * * Request Simulate to 31171 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262146, 1024)
ddd, is_drv=False
cur_cycle: 31167 , next_cycle: 31171
[Subsystem::handle] Received handshake request @sim_time= 31171
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000079c30000000c
ipcSocketServer receive request data: 02e7b00b0004000400000000
* * * * Request Simulate to 31171 * * * *
How many requests in queue? - 0
cur_cycle: 31171 , next_cycle: 31171
[Subsystem::handle] Received nice instruction request @sim_time= 31171
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000079c700000008
ipcSocketServer receive request data: 00000000000079c7
* * * * Request Simulate to 31175 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262148, 0)
ddd, is_drv=False
cur_cycle: 31171 , next_cycle: 31175
[Subsystem::handle] Received handshake request @sim_time= 31175
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000079c70000000c
ipcSocketServer receive request data: a807e78b0000000000000000
* * * * Request Simulate to 31175 * * * *
How many requests in queue? - 0
cur_cycle: 31175 , next_cycle: 31175
[Subsystem::handle] Received nice instruction request @sim_time= 31175
----------------------------------------------------
ipcSocketServer receive request header: ****************c700000000
* * * * Request Simulate to 31175 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle vs_drv(a807e78b, 0, 0)
Verifying, current_cycle:31175 - sychronizing_npus:False - target_sim_cycle:31175
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, True, False, False]
Verifying primitive: Pid: 27, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: byte_base: 0x0, byte_stride: (512, 512), size: (256, 1, 1), type: BF, width: 16
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 31175 , next_cycle: 31175
dispatching: Pid: 27, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received check request @sim_time= 31175
----------------------------------------------------
ipcSocketServer receive request header: ****************c700000000
* * * * Request Simulate to 31175 * * * *
How many requests in queue? - 0
NPU Group.ID: 3.1, Start: Pid: 27, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(31175, 31192), #cycle: 17
0 - SRAMBank 1 from 0x0000_0000 to 0x0000_ffe0, Cycle cost: Rin1=16, Rin2=0, Rorig=0, Wout=0, Rcomm=0, Wcomm=0, and Total=16
1 - VPU with 16 Engines each receives 2×32bit inputs, Cycle cost: VectorProcess=11, Latency=1
cur_cycle: 31175 , next_cycle: 31176
[Subsystem::handle] Received check request @sim_time= 31175
----------------------------------------------------
ipcSocketServer receive request header: ****************c800000000
* * * * Request Simulate to 31176 * * * *
How many requests in queue? - 0
cur_cycle: 31175 , next_cycle: 31192
[Subsystem::handle] Received check request @sim_time= 31176
----------------------------------------------------
ipcSocketServer receive request header: ****************d800000000
* * * * Request Simulate to 31192 * * * *
How many requests in queue? - 0
cur_cycle: 31176 , next_cycle: 31192
[Subsystem::handle] Received check request @sim_time= 31192
----------------------------------------------------
ipcSocketServer receive request header: ****************d800000000
* * * * Request Simulate to 31192 * * * *
How many requests in queue? - 0
NPU Core 13: Stored return value 1093623808 for prim_id 27, inst_id 13
NPU Group.ID: 3.1, Finish: Pid: 27, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(31175, 31192), #cycle: 17
Generated response for inst_id 13, type nice, ret 1093623808
cur_cycle: 31192 , next_cycle: 31193
[Subsystem::handle] Received check request @sim_time= 31192
[Subsystem::check_handler] check nice instruction response: id=0, type=nice, ret=0x412f6000
----------------------------------------------------
ipcSocketServer receive request header: 0100000000000079f400000008
ipcSocketServer receive request data: 00000000000079f4
* * * * Request Simulate to 31220 * * * *
How many requests in queue? - 0
cur_cycle: 31192 , next_cycle: 31220
[Subsystem::handle] Received handshake request @sim_time= 31220
----------------------------------------------------
ipcSocketServer receive request header: 0200000000000079f40000000c
ipcSocketServer receive request data: 00e7b00b0000000300000004
* * * * Request Simulate to 31220 * * * *
How many requests in queue? - 0
cur_cycle: 31220 , next_cycle: 31220
[Subsystem::handle] Received nice instruction request @sim_time= 31220
----------------------------------------------------
ipcSocketServer receive request header: 010000000000007a0100000008
ipcSocketServer receive request data: 0000000000007a01
* * * * Request Simulate to 31233 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 3, 4)
Verifying, current_cycle:31220 - sychronizing_npus:False - target_sim_cycle:31233
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, True, False, False]
Verifying primitive: Pid: 28, PrimName.GROUP_MASK, group/mask: 3/4, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 31220 , next_cycle: 31233
dispatching: Pid: 28, PrimName.GROUP_MASK, group/mask: 3/4, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 31233
----------------------------------------------------
ipcSocketServer receive request header: 020000000000007a010000000c
ipcSocketServer receive request data: 02e7b00b000400000000090a
* * * * Request Simulate to 31233 * * * *
How many requests in queue? - 0
cur_cycle: 31233 , next_cycle: 31233
[Subsystem::handle] Received nice instruction request @sim_time= 31233
----------------------------------------------------
ipcSocketServer receive request header: 010000000000007a0500000008
ipcSocketServer receive request data: 0000000000007a05
* * * * Request Simulate to 31237 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262144, 2314)
ddd, is_drv=False
cur_cycle: 31233 , next_cycle: 31237
[Subsystem::handle] Received handshake request @sim_time= 31237
----------------------------------------------------
ipcSocketServer receive request header: 020000000000007a050000000c
ipcSocketServer receive request data: 02e7b00b0004000200000400
* * * * Request Simulate to 31237 * * * *
How many requests in queue? - 0
cur_cycle: 31237 , next_cycle: 31237
[Subsystem::handle] Received nice instruction request @sim_time= 31237
----------------------------------------------------
ipcSocketServer receive request header: 010000000000007a0900000008
ipcSocketServer receive request data: 0000000000007a09
* * * * Request Simulate to 31241 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262146, 1024)
ddd, is_drv=False
cur_cycle: 31237 , next_cycle: 31241
[Subsystem::handle] Received handshake request @sim_time= 31241
----------------------------------------------------
ipcSocketServer receive request header: 020000000000007a090000000c
ipcSocketServer receive request data: 02e7b00b0004000400000000
* * * * Request Simulate to 31241 * * * *
How many requests in queue? - 0
cur_cycle: 31241 , next_cycle: 31241
[Subsystem::handle] Received nice instruction request @sim_time= 31241
----------------------------------------------------
ipcSocketServer receive request header: 010000000000007a0d00000008
ipcSocketServer receive request data: 0000000000007a0d
* * * * Request Simulate to 31245 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262148, 0)
ddd, is_drv=False
cur_cycle: 31241 , next_cycle: 31245
[Subsystem::handle] Received handshake request @sim_time= 31245
----------------------------------------------------
ipcSocketServer receive request header: 020000000000007a0d0000000c
ipcSocketServer receive request data: a807e78b0000000000000000
* * * * Request Simulate to 31245 * * * *
How many requests in queue? - 0
cur_cycle: 31245 , next_cycle: 31245
[Subsystem::handle] Received nice instruction request @sim_time= 31245
----------------------------------------------------
ipcSocketServer receive request header: 050000000000007a0d00000000
* * * * Request Simulate to 31245 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle vs_drv(a807e78b, 0, 0)
Verifying, current_cycle:31245 - sychronizing_npus:False - target_sim_cycle:31245
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, True, False]
Verifying primitive: Pid: 29, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: byte_base: 0x0, byte_stride: (512, 512), size: (256, 1, 1), type: BF, width: 16
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 31245 , next_cycle: 31245
dispatching: Pid: 29, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received check request @sim_time= 31245
----------------------------------------------------
ipcSocketServer receive request header: 050000000000007a0d00000000
* * * * Request Simulate to 31245 * * * *
How many requests in queue? - 0
NPU Group.ID: 3.2, Start: Pid: 29, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(31245, 31262), #cycle: 17
0 - SRAMBank 1 from 0x0000_0000 to 0x0000_ffe0, Cycle cost: Rin1=16, Rin2=0, Rorig=0, Wout=0, Rcomm=0, Wcomm=0, and Total=16
1 - VPU with 16 Engines each receives 2×32bit inputs, Cycle cost: VectorProcess=11, Latency=1
cur_cycle: 31245 , next_cycle: 31246
[Subsystem::handle] Received check request @sim_time= 31245
----------------------------------------------------
ipcSocketServer receive request header: 050000000000007a0e00000000
* * * * Request Simulate to 31246 * * * *
How many requests in queue? - 0
cur_cycle: 31245 , next_cycle: 31262
[Subsystem::handle] Received check request @sim_time= 31246
----------------------------------------------------
ipcSocketServer receive request header: 050000000000007a1e00000000
* * * * Request Simulate to 31262 * * * *
How many requests in queue? - 0
cur_cycle: 31246 , next_cycle: 31262
[Subsystem::handle] Received check request @sim_time= 31262
----------------------------------------------------
ipcSocketServer receive request header: 050000000000007a1e00000000
* * * * Request Simulate to 31262 * * * *
How many requests in queue? - 0
NPU Core 14: Stored return value 1076396032 for prim_id 29, inst_id 14
NPU Group.ID: 3.2, Finish: Pid: 29, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(31245, 31262), #cycle: 17
Generated response for inst_id 14, type nice, ret 1076396032
cur_cycle: 31262 , next_cycle: 31263
[Subsystem::handle] Received check request @sim_time= 31262
[Subsystem::check_handler] check nice instruction response: id=0, type=nice, ret=0x40288000
----------------------------------------------------
ipcSocketServer receive request header: 010000000000007a3a00000008
ipcSocketServer receive request data: 0000000000007a3a
* * * * Request Simulate to 31290 * * * *
How many requests in queue? - 0
cur_cycle: 31262 , next_cycle: 31290
[Subsystem::handle] Received handshake request @sim_time= 31290
----------------------------------------------------
ipcSocketServer receive request header: 020000000000007a3a0000000c
ipcSocketServer receive request data: 00e7b00b0000000300000008
* * * * Request Simulate to 31290 * * * *
How many requests in queue? - 0
cur_cycle: 31290 , next_cycle: 31290
[Subsystem::handle] Received nice instruction request @sim_time= 31290
----------------------------------------------------
ipcSocketServer receive request header: 010000000000007a4700000008
ipcSocketServer receive request data: 0000000000007a47
* * * * Request Simulate to 31303 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle mask_group_drv(00e7b00b, 3, 8)
Verifying, current_cycle:31290 - sychronizing_npus:False - target_sim_cycle:31303
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, True, False]
Verifying primitive: Pid: 30, PrimName.GROUP_MASK, group/mask: 3/8, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: This is an empty tensor!
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 31290 , next_cycle: 31303
dispatching: Pid: 30, PrimName.GROUP_MASK, group/mask: 3/8, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received handshake request @sim_time= 31303
----------------------------------------------------
ipcSocketServer receive request header: 020000000000007a470000000c
ipcSocketServer receive request data: 02e7b00b000400000000090a
* * * * Request Simulate to 31303 * * * *
How many requests in queue? - 0
cur_cycle: 31303 , next_cycle: 31303
[Subsystem::handle] Received nice instruction request @sim_time= 31303
----------------------------------------------------
ipcSocketServer receive request header: 010000000000007a4b00000008
ipcSocketServer receive request data: 0000000000007a4b
* * * * Request Simulate to 31307 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262144, 2314)
ddd, is_drv=False
cur_cycle: 31303 , next_cycle: 31307
[Subsystem::handle] Received handshake request @sim_time= 31307
----------------------------------------------------
ipcSocketServer receive request header: 020000000000007a4b0000000c
ipcSocketServer receive request data: 02e7b00b0004000200000400
* * * * Request Simulate to 31307 * * * *
How many requests in queue? - 0
cur_cycle: 31307 , next_cycle: 31307
[Subsystem::handle] Received nice instruction request @sim_time= 31307
----------------------------------------------------
ipcSocketServer receive request header: 010000000000007a4f00000008
ipcSocketServer receive request data: 0000000000007a4f
* * * * Request Simulate to 31311 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262146, 1024)
ddd, is_drv=False
cur_cycle: 31307 , next_cycle: 31311
[Subsystem::handle] Received handshake request @sim_time= 31311
----------------------------------------------------
ipcSocketServer receive request header: 020000000000007a4f0000000c
ipcSocketServer receive request data: 02e7b00b0004000400000000
* * * * Request Simulate to 31311 * * * *
How many requests in queue? - 0
cur_cycle: 31311 , next_cycle: 31311
[Subsystem::handle] Received nice instruction request @sim_time= 31311
----------------------------------------------------
ipcSocketServer receive request header: 010000000000007a5300000008
ipcSocketServer receive request data: 0000000000007a53
* * * * Request Simulate to 31315 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle npu_cfg_drv(02e7b00b, 262148, 0)
ddd, is_drv=False
cur_cycle: 31311 , next_cycle: 31315
[Subsystem::handle] Received handshake request @sim_time= 31315
----------------------------------------------------
ipcSocketServer receive request header: 020000000000007a530000000c
ipcSocketServer receive request data: a807e78b0000000000000000
* * * * Request Simulate to 31315 * * * *
How many requests in queue? - 0
cur_cycle: 31315 , next_cycle: 31315
[Subsystem::handle] Received nice instruction request @sim_time= 31315
----------------------------------------------------
ipcSocketServer receive request header: 050000000000007a5300000000
* * * * Request Simulate to 31315 * * * *
How many requests in queue? - 1
[NiceInterface::decoder] handle vs_drv(a807e78b, 0, 0)
Verifying, current_cycle:31315 - sychronizing_npus:False - target_sim_cycle:31315
Group:0 - Mask:[False, False, False, False]
Group:1 - Mask:[False, False, False, False]
Group:2 - Mask:[False, False, False, False]
Group:3 - Mask:[False, False, False, True]
Verifying primitive: Pid: 31, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
Verifying Tensor_in1: byte_base: 0x0, byte_stride: (512, 512), size: (256, 1, 1), type: BF, width: 16
Verifying Tensor_in2: This is an empty tensor!
Verifying Tensor_out: This is an empty tensor!
Verifying conv_settings: Empty Conv Info!
cur_cycle: 31315 , next_cycle: 31315
dispatching: Pid: 31, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(None, None), #cycle: ?
[Subsystem::handle] Received check request @sim_time= 31315
----------------------------------------------------
ipcSocketServer receive request header: 050000000000007a5300000000
* * * * Request Simulate to 31315 * * * *
How many requests in queue? - 0
NPU Group.ID: 3.3, Start: Pid: 31, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(31315, 31332), #cycle: 17
0 - SRAMBank 1 from 0x0000_0000 to 0x0000_ffe0, Cycle cost: Rin1=16, Rin2=0, Rorig=0, Wout=0, Rcomm=0, Wcomm=0, and Total=16
1 - VPU with 16 Engines each receives 2×32bit inputs, Cycle cost: VectorProcess=11, Latency=1
cur_cycle: 31315 , next_cycle: 31316
[Subsystem::handle] Received check request @sim_time= 31315
----------------------------------------------------
ipcSocketServer receive request header: 050000000000007a5400000000
* * * * Request Simulate to 31316 * * * *
How many requests in queue? - 0
cur_cycle: 31315 , next_cycle: 31332
[Subsystem::handle] Received check request @sim_time= 31316
----------------------------------------------------
ipcSocketServer receive request header: 050000000000007a6400000000
* * * * Request Simulate to 31332 * * * *
How many requests in queue? - 0
cur_cycle: 31316 , next_cycle: 31332
[Subsystem::handle] Received check request @sim_time= 31332
----------------------------------------------------
ipcSocketServer receive request header: 050000000000007a6400000000
* * * * Request Simulate to 31332 * * * *
How many requests in queue? - 0
NPU Core 15: Stored return value 1078329344 for prim_id 31, inst_id 15
NPU Group.ID: 3.3, Finish: Pid: 31, PrimName.V_S×1, VectorProcess.ADD, begins and ends @cycle(31315, 31332), #cycle: 17
Generated response for inst_id 15, type nice, ret 1078329344
cur_cycle: 31332 , next_cycle: 31333
[Subsystem::handle] Received check request @sim_time= 31332
[Subsystem::check_handler] check nice instruction response: id=0, type=nice, ret=0x40460000
----------------------------------------------------
